# Hướng dẫn Import/Export Câu hỏi trắc nghiệm từ Excel

## Tổng quan
Chức năng Import/Export cho phép giáo viên:
- Xuất danh sách câu hỏi hiện có ra file Excel
- Nhập câu hỏi từ file Excel vào hệ thống
- Tải template Excel mẫu để tạo câu hỏi

## Cấu trúc file Excel

### Các cột bắt buộc:
1. **<PERSON><PERSON> thứ tự**: <PERSON><PERSON> thứ tự của câu hỏi (tự động)
2. **Câu hỏi**: Nội dung câu hỏi (bắt buộc)
3. **Đáp án A**: L<PERSON><PERSON> chọn A (bắt buộc)
4. **Đáp án B**: Lựa chọn B (bắt buộc)
5. **Đáp án C**: Lựa chọn C (bắt buộc)
6. **Đáp án D**: Lựa chọn D (b<PERSON><PERSON> buộc)
7. **Đáp án đúng**: <PERSON><PERSON><PERSON> á<PERSON> đúng (bắt buộc)
8. **<PERSON><PERSON><PERSON><PERSON> thích**: <PERSON><PERSON><PERSON><PERSON> thích đáp án (tùy chọn)

### Các cột tùy chọn (chỉ có trong export):
- **Môn học**: Tên môn học
- **Lớp học**: Tên lớp học
- **Độ khó**: Mức độ khó (Dễ/Trung bình/Khó)
- **Điểm**: Số điểm của câu hỏi
- **Tags**: Các thẻ tag

## Quy tắc nhập liệu

### Đáp án đúng:
- Sử dụng chữ cái A, B, C, hoặc D
- Có thể có nhiều đáp án đúng, cách nhau bằng dấu phẩy: `A, B, D`
- Không phân biệt hoa thường
- Ví dụ hợp lệ: `A`, `B`, `A, C`, `A,B,D`

### Câu hỏi và đáp án:
- Không được để trống
- Có thể chứa ký tự đặc biệt, số, và emoji
- Độ dài tối đa: 1000 ký tự cho câu hỏi, 500 ký tự cho mỗi đáp án

### Giải thích:
- Tùy chọn, có thể để trống
- Độ dài tối đa: 1000 ký tự

## Cách sử dụng

### 1. Tải Template
1. Nhấn nút **"Tải Template"** trong giao diện Ngân hàng câu hỏi
2. File template sẽ được tải về với tên `template-cau-hoi-trac-nghiem.xlsx`
3. Mở file và điền thông tin câu hỏi theo mẫu

### 2. Import câu hỏi
1. Chuẩn bị file Excel theo đúng format
2. Nhấn nút **"Import Excel"**
3. Chọn file Excel từ máy tính
4. Hệ thống sẽ hiển thị dialog tiến trình xử lý
5. Xem kết quả import và các lỗi (nếu có)

### 3. Export câu hỏi
1. Sử dụng bộ lọc để chọn câu hỏi cần xuất (tùy chọn)
2. Nhấn nút **"Export Excel"**
3. File Excel sẽ được tải về với tên `cau-hoi-trac-nghiem-YYYY-MM-DD.xlsx`

## Xử lý lỗi

### Lỗi thường gặp:
1. **Thiếu thông tin bắt buộc**: Kiểm tra các cột bắt buộc không được để trống
2. **Đáp án đúng không hợp lệ**: Chỉ sử dụng A, B, C, D
3. **Format file không đúng**: Chỉ hỗ trợ file .xlsx và .xls
4. **File rỗng**: File phải có ít nhất 1 dòng dữ liệu

### Khi có lỗi:
- Hệ thống sẽ hiển thị danh sách lỗi chi tiết
- Các câu hỏi hợp lệ vẫn được import thành công
- Sửa lỗi trong file Excel và thử import lại

## Ví dụ file Excel

| Số thứ tự | Câu hỏi | Đáp án A | Đáp án B | Đáp án C | Đáp án D | Đáp án đúng | Giải thích |
|-----------|---------|----------|----------|----------|----------|-------------|------------|
| 1 | Thủ đô của Việt Nam là gì? | Hà Nội | Hồ Chí Minh | Đà Nẵng | Cần Thơ | A | Hà Nội là thủ đô của Việt Nam từ năm 1010 |
| 2 | Kết quả của 2 + 3 là? | 4 | 5 | 6 | 7 | B | 2 + 3 = 5 |
| 3 | Những ngôn ngữ nào sau đây là ngôn ngữ lập trình? | Python | HTML | JavaScript | CSS | A, C | Python và JavaScript là ngôn ngữ lập trình |

## Lưu ý quan trọng

1. **Backup dữ liệu**: Luôn sao lưu dữ liệu trước khi import số lượng lớn
2. **Kiểm tra trước**: Sử dụng file nhỏ để test trước khi import file lớn
3. **Môn học và Lớp học**: Sau khi import, cần cập nhật thông tin môn học và lớp học cho từng câu hỏi
4. **Encoding**: Đảm bảo file Excel được lưu với encoding UTF-8 để hiển thị đúng tiếng Việt
5. **Kích thước file**: Khuyến nghị import tối đa 1000 câu hỏi mỗi lần

## Hỗ trợ

Nếu gặp vấn đề khi sử dụng chức năng Import/Export, vui lòng:
1. Kiểm tra lại format file Excel
2. Đảm bảo dữ liệu đúng quy tắc
3. Liên hệ bộ phận hỗ trợ kỹ thuật
