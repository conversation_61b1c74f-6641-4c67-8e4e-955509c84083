# Chức năng Import/Export Câu hỏi trắc nghiệm từ Excel

## 🎯 Tổng quan
Đã thêm thành công chức năng Import và Export câu hỏi trắc nghiệm từ/ra file Excel vào hệ thống Bee STEM Solutions.

## ✨ Tính năng mới

### 1. Export câu hỏi ra Excel
- **Nút "Export Excel"**: Xuất tất cả câu hỏi hiện tại (c<PERSON> thể lọc) ra file Excel
- **Format đầy đủ**: <PERSON><PERSON> gồm tất cả thông tin câu hỏi, đá<PERSON> án, mô<PERSON> họ<PERSON>, lớ<PERSON> họ<PERSON>, độ khó, điểm, tags
- **Tên file tự động**: `cau-hoi-trac-nghiem-YYYY-MM-DD.xlsx`

### 2. Import câu hỏi từ Excel
- **Nút "Import Excel"**: Nhập câu hỏi từ file Excel đã chuẩn bị
- **Validation đầy đủ**: Ki<PERSON>m tra format, dữ liệu b<PERSON>t buộc, đáp án đúng
- **Progress tracking**: Hiển thị tiến trình xử lý và kết quả chi tiết
- **Error handling**: Báo lỗi cụ thể cho từng dòng có vấn đề

### 3. Template Excel
- **Nút "Tải Template"**: Tải file Excel mẫu với format chuẩn
- **Dữ liệu mẫu**: Có sẵn 3 câu hỏi ví dụ để tham khảo
- **Hướng dẫn rõ ràng**: Format cột và cách điền dữ liệu

### 4. Hướng dẫn sử dụng
- **Nút "Hướng dẫn"**: Dialog hướng dẫn chi tiết cách sử dụng
- **Format file**: Giải thích cấu trúc file Excel cần thiết
- **Lưu ý quan trọng**: Các điều cần chú ý khi import/export

## 🔧 Cài đặt Dependencies

Đã thêm các thư viện cần thiết vào `package.json`:
```json
{
  "xlsx": "^0.18.5",
  "file-saver": "^2.0.5"
}
```

## 📁 Files đã thêm/sửa đổi

### 1. Components
- `frontend/src/components/Academy/Teacher/components/QuestionBank.jsx` - Thêm UI và logic import/export

### 2. Utilities
- `frontend/src/utils/excelUtils.js` - Các hàm tiện ích xử lý Excel

### 3. Services
- `frontend/src/services/elearningAPI.js` - Thêm API endpoints cho import/export

### 4. Documentation
- `docs/IMPORT_EXPORT_QUESTIONS.md` - Hướng dẫn chi tiết
- `demo/sample-questions.xlsx` - File Excel mẫu

## 📋 Format file Excel

### Các cột bắt buộc:
1. **Số thứ tự**: Số thứ tự câu hỏi
2. **Câu hỏi**: Nội dung câu hỏi (bắt buộc)
3. **Đáp án A**: Lựa chọn A (bắt buộc)
4. **Đáp án B**: Lựa chọn B (bắt buộc)
5. **Đáp án C**: Lựa chọn C (bắt buộc)
6. **Đáp án D**: Lựa chọn D (bắt buộc)
7. **Đáp án đúng**: A, B, C, D hoặc kết hợp (A, B, D) (bắt buộc)
8. **Giải thích**: Giải thích đáp án (tùy chọn)

### Quy tắc đáp án đúng:
- Sử dụng chữ cái A, B, C, D
- Nhiều đáp án đúng: `A, B, D` hoặc `A,B,D`
- Không phân biệt hoa thường
- Ví dụ hợp lệ: `A`, `B`, `A, C`, `A,B,D`

## 🚀 Cách sử dụng

### Export câu hỏi:
1. Vào trang "Ngân hàng câu hỏi"
2. Sử dụng bộ lọc nếu cần (tùy chọn)
3. Nhấn nút "Export Excel"
4. File sẽ được tải về máy tính

### Import câu hỏi:
1. Nhấn "Tải Template" để tải file mẫu
2. Điền thông tin câu hỏi vào file Excel
3. Nhấn "Import Excel" và chọn file
4. Xem kết quả import và sửa lỗi nếu có
5. Cập nhật môn học và lớp học cho câu hỏi sau khi import

## ⚠️ Lưu ý quan trọng

1. **Backup dữ liệu**: Luôn sao lưu trước khi import số lượng lớn
2. **Test với file nhỏ**: Thử với vài câu hỏi trước khi import hàng trăm câu
3. **Môn học và Lớp học**: Cần cập nhật thủ công sau khi import
4. **Encoding**: File Excel phải hỗ trợ UTF-8 cho tiếng Việt
5. **Giới hạn**: Khuyến nghị tối đa 1000 câu hỏi mỗi lần import

## 🔍 Validation và Error Handling

### Kiểm tra tự động:
- Các cột bắt buộc không được trống
- Format đáp án đúng hợp lệ
- Cấu trúc file Excel đúng
- Encoding và ký tự đặc biệt

### Báo lỗi chi tiết:
- Hiển thị số dòng có lỗi
- Mô tả cụ thể lỗi gì
- Thống kê tổng quan (thành công/thất bại)
- Danh sách lỗi có thể scroll

## 🎨 UI/UX Features

### Design hiện đại:
- Buttons với gradient và hover effects
- Progress bar với animation
- Dialog với rounded corners và shadows
- Color coding cho các loại thông tin

### User Experience:
- Loading states và progress tracking
- Success/error notifications
- Detailed help dialog
- Responsive design

## 🔮 Tương lai

### Có thể mở rộng:
1. **Bulk edit**: Chỉnh sửa hàng loạt môn học/lớp học
2. **Advanced filters**: Lọc theo nhiều tiêu chí khi export
3. **Question templates**: Tạo template cho từng môn học
4. **Import validation**: Preview trước khi import
5. **Batch operations**: Xóa/cập nhật hàng loạt

### API Backend cần implement:
- `POST /api/elearning/teacher/questions/bulk-import/`
- `GET /api/elearning/teacher/questions/export/`

## 📞 Hỗ trợ

Nếu gặp vấn đề:
1. Kiểm tra format file Excel theo hướng dẫn
2. Xem chi tiết lỗi trong dialog import
3. Thử với file template mẫu
4. Liên hệ team phát triển nếu cần hỗ trợ thêm
