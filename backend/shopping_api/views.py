import qrcode
import io
import base64

from utils.Utils import validate_captcha_token, send_email, send_email_html
from utils.permissions import IsSaler

from datetime import datetime
from vietnam_provinces.enums import ProvinceEnum, DistrictEnum
from vietnam_provinces.enums.wards import WardEnum

from rest_framework import status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated


from django.db.models import Q, Min, OuterRef, Subquery
from django.http import JsonResponse

from auth_api.models import (
    Membership,
    Profile
)

from auth_api.serializers import (
    MembershipSerializer
)

from product_api.serializers import (
    CategorySerializer,
    ProductSerializer,
)

from product_api.models import (
    Inventory,
    Category,
    BarcodeInventory,
)

from .serializers import (
    CartSerializer,
    CartDetailSerializer
)

from .models import (
    Cart,
    CartDetail
)

from admin_api.models import (
    Coupon
)

from blog_api.models import (
    BlogPost,
    BlogCategory
)

from blog_api.serializers import (
    BlogPostSerializer,
    BlogCategorySerializer
)


def get_unique_products(queryset, exclude: str = None):
    """
    Get unique products by name with lowest price and smallest SKU.

    Args:
        queryset: Initial queryset of Inventory objects with filters already applied

    Returns:
        List of unique Inventory objects
    """
    # First get minimum prices for each name
    min_prices = queryset.values('name').annotate(
        min_price=Min('sale_price')
    )

    # Then get the products with minimum prices
    result = []
    for item in min_prices:
        product = queryset.filter(
            name=item['name'],
            sale_price=item['min_price']
        ).order_by('sku').first()
        if product:
            if exclude and product.sku.split('-')[0] == exclude.split('-')[0]:
                continue
            result.append(product)

    return result


class HomeView(APIView):
    def get(self, request):
        initial_queryset = Inventory.objects.filter(
            visible=True,
            is_removed=False
        )

        products = get_unique_products(initial_queryset)
        serializer = ProductSerializer(
            products, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['GET'])
def get_alternative_product(request, pk=""):
    try:
        current_product = Inventory.objects.get(sku=pk)
        initial_queryset = Inventory.objects.filter(
            visible=True,
            category=current_product.category
        ).exclude(sku=pk)

        if not initial_queryset.exists():
            initial_queryset = Inventory.objects.filter(
                visible=True
            ).exclude(sku=pk)

        products = get_unique_products(initial_queryset, exclude=pk)[
            :4]  # Giới hạn 4 sản phẩm
        serializer = ProductSerializer(
            products, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Inventory.DoesNotExist:
        return Response({"error": "No product found"}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def filter_by_category(request, pk=""):
    try:
        category = Category.objects.get(id=pk)
        initial_queryset = Inventory.objects.filter(
            visible=True,
            category=category
        )
        products = get_unique_products(initial_queryset)
        serializer = ProductSerializer(
            products, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except:
        return Response({"error": "No product found"}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def filter_by_product(request, keyword=""):
    try:
        initial_queryset = Inventory.objects.filter(
            (Q(sku__icontains=keyword) | Q(name__icontains=keyword)),
            visible=True
        )
        products = get_unique_products(initial_queryset)
        serializer = ProductSerializer(
            products, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except:
        return Response({"error": "No product found"}, status=status.HTTP_404_NOT_FOUND)


@api_view(['GET'])
def filter_by_barcode(request, keyword=""):
    try:
        barcode = BarcodeInventory.objects.get(barcode=keyword)
        if barcode:
            product = barcode.product
            serializer = ProductSerializer(
                product, context={'request': request})
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response({"error": "No product found"}, status=status.HTTP_404_NOT_FOUND)
    except:
        return Response({"error": "No product found"}, status=status.HTTP_404_NOT_FOUND)


class MembershipView(APIView):
    permission_classes = [IsSaler]

    def get(self, request, keyword=""):
        try:
            members = Membership.objects.filter(phone__icontains=keyword).all()
            serializer = MembershipSerializer(
                members, many=True, context={'request': request})
            return Response(serializer.data, status=status.HTTP_200_OK)
        except:
            return Response({"error": "User not found"}, status=status.HTTP_404_NOT_FOUND)

    def post(self, request):
        seller = request.user
        name = request.data.get("name")
        phone = request.data.get('phone')

        # Check if the membership exists
        membership, created = Membership.objects.get_or_create(
            phone=phone,
            defaults={"name": name, "seller": seller},
        )
        serializer = MembershipSerializer(
            membership, context={'request': request})
        if created:
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        else:
            return Response(serializer.data, status=status.HTTP_200_OK)


class ProductView(APIView):

    def get(self, request, pk):
        product = Inventory.objects.get(sku=pk)
        serializer = ProductSerializer(product, context={'request': request})
        return Response(serializer.data)


class ProductNameView(APIView):
    """Rule of query same attribute: <SKU>-<Attribute>"""

    def get(self, request, pk):
        if request.user.is_authenticated and (request.user.profile.is_supervisor or request.user.profile.is_admin):
            product = Inventory.objects.filter(
                sku__istartswith=pk.split("-")[0])
        else:
            product = Inventory.objects.filter(
                sku__istartswith=pk.split("-")[0], visible=True)
        if product:
            serializer = ProductSerializer(
                product, context={'request': request}, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_404_NOT_FOUND)


@api_view(["GET"])
def get_product_category(request):
    categories = Category.objects.all()
    serializer = CategorySerializer(
        categories, many=True, context={'request': request})
    return Response(serializer.data, status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def add_to_cart(request):
    user = request.user
    sku = request.data.get('sku')
    quantity = request.data.get('quantity', 1)

    # Add product to cart
    try:
        product = Inventory.objects.get(sku=sku)
    except Inventory.DoesNotExist:
        return Response({'error': 'Không tìm thấy sản phẩm'}, status=status.HTTP_404_NOT_FOUND)
    cart, created = Cart.objects.get_or_create(user=user, status="Tạm thời")
    cart_item, created = CartDetail.objects.get_or_create(
        cart=cart, item=product)
    cart_item.quantity += int(quantity) if not created else int(quantity)
    cart_item.sale_price = product.sale_price
    cart_item.save()

    # Refresh cart
    cartDetail = CartDetail.objects.filter(cart=cart)
    serializer = CartDetailSerializer(
        cartDetail, many=True, context={'request': request})
    return Response(serializer.data, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([IsSaler])
def add_to_pos_cart(request):
    seller = request.user
    items = request.data.get("items", [])
    customer = request.data.get("customer")

    # Remove previous pos cart
    cart, created = Cart.objects.get_or_create(user=seller, status="Tạm thời")
    cart.customer_name = customer["name"]
    cart.customer_phone = customer["phone"]
    cart.customer_note = customer["note"]
    cart.payment_method = customer["payment"]
    cart.save()

    if not created:
        cart_item = CartDetail.objects.filter(cart=cart)
        for item in cart_item:
            item.delete()

    # Add items to cart
    for item in items:
        product = Inventory.objects.get(sku=item["sku"])
        quantity = int(item["quantity"])
        cart_item, created = CartDetail.objects.get_or_create(
            cart=cart, item=product, quantity=quantity)
        cart_item.sale_price = product.sale_price
        cart_item.save()

    # Refresh cart
    serializer = CartSerializer(cart, context={'request': request})
    return Response(serializer.data, status=status.HTTP_201_CREATED)


@api_view(['POST'])
@permission_classes([IsSaler])
def remove_pos_cart(request):
    seller = request.user

    # Remove previous pos cart
    cart, created = Cart.objects.get_or_create(user=seller, status="Tạm thời")
    cart.customer_name = ""
    cart.customer_phone = ""
    cart.customer_note = ""
    cart.payment_method = ""
    cart.save()

    if not created:
        cart_item = CartDetail.objects.filter(cart=cart)
        for item in cart_item:
            item.delete()

    return Response({"status": "done"}, status=status.HTTP_200_OK)


@api_view(["POST"])
@permission_classes([IsSaler])
def checkout_pos_cart(request):
    seller = request.user

    cart, _ = Cart.objects.get_or_create(user=seller, status="Tạm thời")
    cart.payment_method = request.data.get('payment_method')
    cart.paid_code = request.data.get('paid_code')
    cart.status = "Đã giao hàng"
    cart.paid = True
    cart.ship_fee = 0
    cart.orderTime = datetime.now()
    cart.ship_to_customer = datetime.now()
    cart.ship_success = datetime.now()
    cart.processUser = seller

    # # Apply Coupon
    # coupon_code = request.data.get('coupon_code')
    # if coupon_code:
    #     try:
    #         coupon = Coupon.objects.get(code=coupon_code)
    #         cart.coupon = coupon
    #         coupon.used_count += 1
    #         coupon.save()
    #     except Coupon.DoesNotExist:
    #         pass

    total_price = 0
    total_discount = 0
    cartDetail = CartDetail.objects.filter(cart=cart)
    for _item in cartDetail:
        if _item.quantity == 0:
            # Remove all cart item that no more visible (removed/disabled) or empty inventory
            _item.delete()
        else:
            total_price += _item.apply_promotion() * _item.quantity
            total_discount += (_item.sale_price -
                               _item.apply_promotion()) * _item.quantity
    cart.total_price = total_price
    cart.total_discount = total_discount
    cart.save()
    cart.calculate_coupon_discount()
    return Response({"status": "done"}, status=status.HTTP_200_OK)


class CartView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request, pk=""):
        user = request.user
        if 'filter' not in request.GET:
            cart = Cart.objects.filter(user=user, status="Tạm thời").first(
            ) if pk == "" else Cart.objects.get(id=pk)
            if cart is None:
                return Response({"message": "Chưa có sản phẩm nào trong giỏ hàng của bạn"},
                                status=status.HTTP_404_NOT_FOUND)
            serializer = CartSerializer(cart)
        else:
            query = request.GET['filter']
            if query == 'All':
                cart = Cart.objects.exclude(
                    status="Tạm thời").order_by('-createTime').all()
            elif query == "me":
                cart = Cart.objects.filter(user=user).exclude(
                    status="Tạm thời").order_by('-createTime').all()
            serializer = CartSerializer(cart, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request):
        user = request.user
        cart = Cart.objects.filter(user=user, status="Tạm thời").first()
        if cart is None:
            return Response({"message": "Chưa có sản phẩm nào trong giỏ hàng của bạn"},
                            status=status.HTTP_404_NOT_FOUND)

        """Customer update receiver information"""
        if "customer_name" in request.data:
            cart.customer_name = request.data.get('customer_name')
            cart.customer_phone = request.data.get('customer_phone')
            cart.customer_address = request.data.get('customer_address')
            cart.customer_ward = request.data.get('customer_ward')
            cart.customer_district = request.data.get('customer_district')
            cart.customer_province = request.data.get('customer_province')
            cart.customer_note = request.data.get('customer_note')
            cart.save()

        """Customer update payment method"""
        if "payment_method" in request.data:
            """Validate Captcha"""
            captcha_token = request.data.get('captcha_token')
            if not validate_captcha_token(captcha_token):
                return Response({"error": "reCAPTCHA không hợp lệ!"}, status=status.HTTP_400_BAD_REQUEST)

            cart.payment_method = request.data.get('payment_method')
            cart.status = "Chờ xác nhận"
            cart.ship_fee = request.data.get('delivery_fee')
            cart.orderTime = datetime.now()

            """Apply Coupon"""
            coupon_code = request.data.get('coupon_code')
            if coupon_code:
                try:
                    coupon = Coupon.objects.get(code=coupon_code)
                    cart.coupon = coupon
                    coupon.used_count += 1
                    coupon.save()
                except Coupon.DoesNotExist:
                    pass

            total_price = 0
            total_discount = 0
            cartDetail = CartDetail.objects.filter(cart=cart)
            for _item in cartDetail:
                if _item.quantity == 0:
                    # Remove all cart item that no more visible (removed/disabled) or empty inventory
                    _item.delete()
                else:
                    total_price += _item.apply_promotion() * _item.quantity
                    total_discount += (_item.sale_price -
                                       _item.apply_promotion()) * _item.quantity
            cart.total_price = total_price
            cart.total_discount = total_discount
            cart.save()
            cart.calculate_coupon_discount()

            """Send email to shop"""
            send_email_html(
                subject=f"[BeE][Xác nhận đơn hàng] #{cart.short_id()}",
                template_name='shop/send_shop.html',
                context={
                    "user_name": "Đ/v Bán hàng",
                    "cart_obj": cart,
                },
                email_to=[
                    saler.user.email for saler in Profile.objects.filter(is_saler=True)]
            )

            """Send email to user"""
            send_email_html(
                subject=f"[BeE][Xác nhận đơn hàng] #{cart.short_id()}",
                template_name='shop/send_user.html',
                context={
                    "user_name": user.profile.name,
                    "cart_obj": cart,
                },
                email_to=[user.email]
            )

        return Response(status=status.HTTP_200_OK)


class CartDetailView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        if "filter" not in request.GET:
            cart = Cart.objects.filter(user=user, status="Tạm thời").first()
            if cart is None:
                return Response({"message": "Chưa có sản phẩm nào trong giỏ hàng của bạn"},
                                status=status.HTTP_404_NOT_FOUND)
            cartDetail = CartDetail.objects.filter(cart=cart)
            serializer = CartDetailSerializer(
                cartDetail, many=True, context={'request': request})
        else:
            cart_id = request.GET["filter"]
            try:
                cart = Cart.objects.get(id=cart_id)
            except Cart.DoesNotExist:
                return Response({"error": "Cart not found"}, status=status.HTTP_404_NOT_FOUND)
            cartDetail = CartDetail.objects.filter(cart=cart)
            serializer = CartDetailSerializer(
                cartDetail, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)

    def patch(self, request, pk=""):
        new_qty = request.data.get('quantity')
        try:
            cartDetail = CartDetail.objects.get(id=pk)
        except CartDetail.DoesNotExist:
            return Response({"message": "Sản phẩm không tồn tại trong giỏ hàng"}, status=status.HTTP_404_NOT_FOUND)
        cartDetail.quantity = new_qty
        cartDetail.save()
        return Response(status=status.HTTP_200_OK)

    def delete(self, request, pk=""):
        try:
            cartDetail = CartDetail.objects.get(id=pk)
        except CartDetail.DoesNotExist:
            return Response({"message": "Sản phẩm không tồn tại trong giỏ hàng"}, status=status.HTTP_404_NOT_FOUND)
        cartDetail.delete()
        return Response(status=status.HTTP_200_OK)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def apply_coupon(request):
    code = request.data.get('coupon_code')
    try:
        total = float(request.data.get('total'))
    except:
        total = 0
    try:
        coupon = Coupon.objects.get(code=code)
        if coupon.is_valid():
            discount_value = float(coupon.promotion.discount_value)
            promotion_type = coupon.promotion.promotion_type
            discount = 0
            if promotion_type == 'PERCENTAGE':
                discount = total * (discount_value / 100)
            elif promotion_type == 'FIXED_AMOUNT':
                discount = discount_value
            else:
                discount = 0
            return Response({"success": True, "discount": discount}, status=status.HTTP_200_OK)
        else:
            return Response({"success": False, "message": "Mã giảm giá đã hết hạn/sử dụng hết!"}, status=status.HTTP_200_OK)
    except Coupon.DoesNotExist:
        return Response({"Lỗi": f"Không tồn tại Mã giảm giá {code}"}, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_province(request):
    provinceList = []
    for province in ProvinceEnum:
        provinceList.append({
            "name": province.value.name,
            "code": province.value.code
        })
    return Response(provinceList, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_district(request, provinceId=""):
    districtList = []
    for district in DistrictEnum:
        if district.value.province_code == int(provinceId):
            districtList.append({
                "province_id": provinceId,
                "name": district.value.name,
                "code": district.value.code
            })
    return Response(districtList, status=status.HTTP_200_OK)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_ward(request, provinceId="", districtId=""):
    wardList = []
    for ward in WardEnum:
        if ward.value.district_code == int(districtId):
            wardList.append(ward.value.name)
    return Response(wardList, status=status.HTTP_200_OK)


@api_view(["POST"])
def contact_form(request):
    """Send Email from Contact view"""
    captcha_token = request.data.get('captcha_token')
    if not validate_captcha_token(captcha_token):
        return Response({"error": "reCAPTCHA không hợp lệ!"}, status=status.HTTP_400_BAD_REQUEST)

    name = request.data.get('name')
    email = request.data.get('email')
    phone = request.data.get('phone')
    message = request.data.get('message')

    subject = f"[LIÊN HỆ][{name} - {phone}]"
    message = ('Dear anh/chị,\n'
               + f'Hệ thống nhận được thông tin cần hỗ trợ từ email {email} với nội dung:\n'
               + f'{message}\n\n'
               + 'Xin chân thành cám ơn anh chị đã đóng góp ý kiến!\n'
               + 'BeE')
    try:
        send_email(subject, message, [email])
    except Exception as ex:
        return Response({"error": str(ex)}, status=status.HTTP_400_BAD_REQUEST)
    return Response(status=status.HTTP_200_OK)


@api_view(["GET"])
def get_blogs(request):
    """Get top 10 latest posts"""
    posts = BlogPost.objects.filter(
        is_published=True).order_by("-created_at")[:6]
    serializer = BlogPostSerializer(
        posts, many=True, context={'request': request})
    return Response(serializer.data)


@api_view(["GET"])
def get_blog_view(request, slug=""):
    """Get blog view"""
    try:
        post = BlogPost.objects.get(slug=slug, is_published=True)
        serializer = BlogPostSerializer(post, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except BlogPost.DoesNotExist:
        return Response({"error": "Không tìm thấy blog"}, status=status.HTTP_404_NOT_FOUND)


@api_view(["GET"])
def get_blog_categories(request):
    categories = BlogCategory.objects.all()
    serializers = BlogCategorySerializer(
        categories, many=True)
    return Response(serializers.data, status=status.HTTP_200_OK)


@api_view(["GET"])
def get_blogs_by_category(request, category_slug=""):
    try:
        if category_slug == "all":
            posts = BlogPost.objects.filter(
                is_published=True).order_by("-created_at").all()
        else:
            category = BlogCategory.objects.get(slug=category_slug)
            posts = BlogPost.objects.filter(
                category=category, is_published=True).order_by("-created_at").all()
        serializer = BlogPostSerializer(
            posts, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Exception as ex:
        return Response({"error": str(ex)}, status=status.HTTP_404_NOT_FOUND)


@api_view(["GET"])
def related_blogs(request, pk=""):
    try:
        post = BlogPost.objects.get(id=pk, is_published=True)
        related_posts = BlogPost.objects.filter(
            category=post.category, is_published=True).exclude(id=pk).order_by("-created_at")[:3]
        serializers = BlogPostSerializer(
            related_posts, many=True, context={'request': request})
        return Response(serializers.data, status=status.HTTP_200_OK)
    except Exception as ex:
        return Response({"error": str(ex)}, status=status.HTTP_404_NOT_FOUND)


@api_view(["GET"])
def get_products_by_category(request, pk=""):
    try:
        if pk == "all":
            initial_queryset = Inventory.objects.filter(
                visible=True)  # .order_by("-created_at")
        else:
            try:
                category = Category.objects.get(id=pk)
            except Category.DoesNotExist:
                return Response({"error": "Không tìm thấy danh mục"}, status=status.HTTP_404_NOT_FOUND)
            initial_queryset = Inventory.objects.filter(
                category=category, visible=True)  # .order_by("-created_at")
        products = get_unique_products(initial_queryset)
        serializer = ProductSerializer(
            products, many=True, context={'request': request})
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Exception as ex:
        return Response({"error": "Đã có lỗi xảy ra: " + str(ex)}, status=status.HTTP_404_NOT_FOUND)


"""QR Code Payment"""


def generate_vietqr(account_number, bank_code, amount, description):
    vietqr_url = f"https://img.vietqr.io/image/{bank_code}-{account_number}-compact.png?amount={amount}&addInfo={description}"
    return vietqr_url


def get_payment_qr(request, order_id, price):
    try:
        # Lấy thông tin đơn hàng từ DB
        order = Cart.objects.get(id=order_id)

        # Thông tin tài khoản nhận
        account_number = "***********"  # Số tài khoản của bạn
        bank_code = "TPbank"  # Mã ngân hàng (Vietcombank)
        amount = price  # Tổng số tiền
        description = f"BeE - Thanh toán đơn hàng {order.short_id()}]"

        # Tạo URL VietQR
        qr_url = generate_vietqr(
            account_number, bank_code, amount, description)

        return JsonResponse({"qr_url": qr_url})
    except Exception as ex:
        return JsonResponse({"qr_url": ""})
