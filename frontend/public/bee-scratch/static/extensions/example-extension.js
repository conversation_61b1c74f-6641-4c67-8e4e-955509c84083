var ExampleExtension=function(){};ExampleExtension.prototype.getInfo=function(){return{id:"someBlocks",name:"Some Blocks",iconURI:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAFCAAAAACyOJm3AAAAFklEQVQYV2P4DwMMEMgAI/+DEUIMBgAEWB7i7uidhAAAAABJRU5ErkJggg==",docsURI:"https://....",blocks:[{opcode:"example-noop",blockType:Scratch.BlockType.COMMAND,blockAllThreads:!1,text:"do nothing",func:"noop"},{opcode:"example-conditional",blockType:Scratch.BlockType.CONDITIONAL,branchCount:4,isTerminal:!0,blockAllThreads:!1,text:"choose [BRANCH]",arguments:{BRANCH:{type:Scratch.ArgumentType.NUMBER,defaultValue:1}},func:"noop"},{opcode:"myReporter",blockType:Scratch.BlockType.REPORTER,branchCount:0,isTerminal:!0,blockAllThreads:!1,text:"letter [LETTER_NUM] of [TEXT]",arguments:{LETTER_NUM:{type:Scratch.ArgumentType.NUMBER,defaultValue:1},TEXT:{type:Scratch.ArgumentType.STRING,defaultValue:"text"}},func:"myReporter",filter:["someBlocks.wedo2","sprite","stage"]},{opcode:"example-Boolean",blockType:Scratch.BlockType.BOOLEAN,text:"return true",func:"returnTrue"},{opcode:"example-hat",blockType:Scratch.BlockType.HAT,text:"after forever",func:"returnFalse"},{}],menus:{menuA:[{value:"itemId1",text:"Item One"},"itemId2"],menuB:"getItemsForMenuB"},translation_map:{de:{extensionName:"Einige Blöcke",myReporter:"Buchstabe [LETTER_NUM] von [TEXT]","myReporter.TEXT_default":"Text",menuA_item1:"Artikel eins",menuB_example:"Beispiel","myReporter.result":"Buchstabe {LETTER_NUM} von {TEXT} ist {LETTER}."},it:{}},targetTypes:["wedo2","speech"]}},ExampleExtension.prototype.myReporter=function(e){const t=e.TEXT.charAt(e.LETTER_NUM);return["Letter ",e.LETTER_NUM," of ",e.TEXT," is ",t,"."].join("")},ExampleExtension.prototype.noop=function(){},ExampleExtension.prototype.returnTrue=function(){return!0},ExampleExtension.prototype.returnFalse=function(){return!1},Scratch.extensions.register(new ExampleExtension);