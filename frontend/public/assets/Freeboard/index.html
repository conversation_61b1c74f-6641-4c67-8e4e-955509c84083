<!doctype html>
<html>

<head>
    <meta charset="utf-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <meta name="description" content="" />
    <meta name="author" content="" />

    <title>BeeIoT</title>
    <link rel="icon" href="/beeIco.svg" type="image/icon type" />
    <!-- MDB -->
    <link rel="stylesheet" href="https://use.fontawesome.com/releases/v5.7.2/css/all.css"
        integrity="sha384-fnmOCqbTlWIlj8LyTjo7mOUStjsKC4pOpQbqyi7RrhN7udi9RwhKkMHpvLbHG9Sr" crossorigin="anonymous" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@mdi/font@7.4.47/css/materialdesignicons.min.css" />

    <!-- Custom fonts for this template-->
    <link href="/assets/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css" />
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet" />
    <!-- Custom styles for this template-->
    <link href="/assets/Freeboard/css/sb-admin-2-custom-simply.css" rel="stylesheet" />
    <link rel="stylesheet" href="/assets/Freeboard/css/bootstrap-select.min.css" />
    <script src="https://code.jquery.com/jquery-3.5.1.min.js"></script>

    <link href="/assets/Freeboard/css/freeboard.css" rel="stylesheet" />
    <script src="/assets/Freeboard/plugins/thirdparty/beeiot/widget/widget_init.js"></script>
    <script src="/assets/Freeboard/js/freeboard.thirdparty.min.js"></script>
    <script type="text/javascript">
        head.js(
            "/assets/Freeboard/js/freeboard_plugins.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/datasource/mqtt.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/datasource/tensorflow.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/button.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/slider.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/roundslider.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/switch.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/gauge1.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/gauge2.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/gauge-horizontal.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/waterlevel.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/pointer.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/chart.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/text.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/sparkline.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/html.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/picture.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/weather.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-camera.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/teachable-machine.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-simple.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-display.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/tensorflow-processor.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/debug-tensorflow.js",
            "/assets/Freeboard/plugins/thirdparty/beeiot/widget/debug-libraries.js",
            //"/assets/Freeboard/plugins/thirdparty/beeiot/widget/calendar.js",
            // *** Load more plugins here ***
            function () {
                $(function () {
                    //DOM Ready
                    freeboard.initialize(true);
                    // {% if request.user.profile.dashboard != "" %}
                    // freeboard.loadDashboard({{ request.user.profile.dashboard | safe }});
                    // {% endif %}
                });
            }
        );
    </script>
</head>
<style>
    /* The Modal (background) */
    .modal-overlay {
        display: none;
        position: absolute;
        z-index: 100;
        top: 0px;
        left: 0px;
        height: 100%;
        width: 100%;
        background: rgba(0, 0, 0, 0.8);
        overflow-y: auto;
    }

    @media screen and (max-width: 600px) {
        .display-sm-none {
            visibility: hidden;
            display: none;
        }
    }

    .nav-text {
        font-size: 16px !important;
        color: #fff !important;
    }
</style>
<div class="modal-overlay" id="datasource-modal">
    <div class="modal">
        <div id="datasources">
            <header>
                <h2 class="title">Device</h2>
            </header>
            <section>
                <div class="datasource-list-container">
                    <table class="table table-condensed sub-table" id="datasources-list"
                        data-bind="if: datasources().length">
                        <thead>
                            <tr>
                                <th style="font-size: 14px">Name</th>
                                <th style="font-size: 14px">Update</th>
                                <th style="font-size: 14px">&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody data-bind="foreach: datasources">
                            <tr>
                                <td>
                                    <span class="text-button datasource-name" style="font-size: 14px"
                                        data-bind="text: name, pluginEditor: {operation: 'edit', type: 'datasource'}"></span>
                                </td>
                                <td data-bind="text: last_updated" style="font-size: 14px"></td>
                                <td>
                                    <ul class="board-toolbar">
                                        <li data-bind="click: updateNow"><i class="icon-refresh"></i></li>
                                        <li data-bind="pluginEditor: {operation: 'delete', type: 'datasource'}">
                                            <i class="icon-trash"></i>
                                        </li>
                                    </ul>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <span class="text-button table-operation"
                    data-bind="pluginEditor: {operation: 'add', type: 'datasource'}" style="font-size: 14px">ADD</span>
            </section>
            <footer>
                <span onclick="close_datasource()" class="text-button"> Close </span>
            </footer>
        </div>
    </div>
</div>

<body onbeforeunload="unload_dashboard()" onload="onload_dashboard()">
    <div id="board-content">
        <img id="dash-logo" data-bind="attr:{src: header_image}, visible:header_image()" />
        <div class="gridster responsive-column-width">
            <ul data-bind="grid: true"></ul>
        </div>
    </div>

    <header id="main-header" data-bind="if:allow_edit" onload="load_dashboard()" style="z-index: 10">
        <div class="d-flex justify-content-between" id="admin-bar">
            <div id="admin-menu">
                <div id="board-tools" style="display: flex">
                    <a style="color: white; margin-right: 1rem" href="https://beeblock.vn/studio" target="_parent"
                        role="button">
                        <img src="/beeIco.svg" width="50px;" />
                    </a>
                    <div id="board-actions">
                        <ul class="board-toolbar vertical">
                            <li data-bind="click: loadDashboardFromLocalFile"
                                style="color: #fff !important; font-size: 16px !important">
                                <i id="full-screen-icon" class="fas fa-folder-open"></i>
                                <label id="full-screen" class="display-sm-none nav-text"
                                    style="text-transform: none">Load</label>
                            </li>
                            <li style="color: #fff !important; font-size: 16px !important">
                                <i class="fas fa-save"></i>
                                <label class="display-sm-none nav-text" data-bind="click: saveDashboardClicked"
                                    style="text-transform: none">Save</label>
                                <label style="display: none" class="nav-text" data-bind="click: saveDashboard"
                                    data-pretty="true">[Pretty]</label>
                                <label style="display: none" class="nav-text" data-bind="click: saveDashboard"
                                    data-pretty="false">[Minified]</label>
                            </li>
                            <li id="add-pane" data-bind="click: createPane"
                                style="color: #fff !important; font-size: 16px !important">
                                <i class="fas fa-plus"></i>
                                <label style="text-transform: none" class="display-sm-none nav-text">Add Pane</label>
                            </li>
                            <li id="add-datasource" onclick="open_datasource()"
                                style="color: #fff !important; font-size: 16px !important">
                                <i class="fas fa-plug"></i>
                                <label style="text-transform: none" class="display-sm-none nav-text">Datasource</label>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <h2 class="display-sm-none" style="width: 100%; text-align: center; color: white; margin-top: 12px">
                BeeIoT Dashboard
            </h2>
        </div>

        <div id="column-tools" class="responsive-column-width">
            <ul class="board-toolbar left-columns">
                <li class="column-tool add" data-bind="click: addGridColumnLeft">
                    <i class="fas fa-sign-out-alt" style="font-size: 20px; transform: rotate(180deg); color: black"></i>
                </li>
                <li class="column-tool sub" data-bind="click: subGridColumnLeft">
                    <i class="fas fa-sign-out-alt" style="font-size: 20px; color: black"></i>
                </li>
            </ul>
            <ul class="board-toolbar right-columns">
                <li class="column-tool sub" data-bind="click: subGridColumnRight">
                    <i class="fas fa-sign-out-alt" style="font-size: 20px; transform: rotate(180deg); color: black"></i>
                </li>
                <li class="column-tool add" data-bind="click: addGridColumnRight">
                    <i class="fas fa-sign-out-alt" style="font-size: 20px; color: black"></i>
                </li>
            </ul>
        </div>
        <div id="toggle-header" style="background-color: #1a237e" data-bind="click: toggleEditing">
            <i id="toggle-header-icon" class="fas fa-pen" style="font-size: 20px; margin-top: 15px"></i>
        </div>
    </header>

    <div style="display: hidden">
        <ul data-bind="template: { name: 'pane-template', foreach: panes}"></ul>
    </div>

    <script type="text/html" id="pane-template">
            <li data-bind="pane: true">
                <header>
                    <h1 data-bind="text: title"></h1>
                    <ul class="board-toolbar pane-tools">
                        <li data-bind="pluginEditor: {operation: 'add', type: 'widget'}">
                            <i class="icon-plus icon-white"></i>
                        </li>
                        <li data-bind="pluginEditor: {operation: 'edit', type: 'pane'}">
                            <i class="icon-wrench icon-white"></i>
                        </li>
                        <li data-bind="pluginEditor: {operation: 'delete', type: 'pane'}">
                            <i class="icon-trash icon-white"></i>
                        </li>
                    </ul>
                </header>
                <section data-bind="foreach: widgets">
                    <div class="sub-section" data-bind="css: 'sub-section-height-' + height()">
                        <div class="widget" data-bind="widget: true, css:{fillsize:fillSize}"></div>
                        <div class="sub-section-tools">
                            <ul class="board-toolbar">
                                <!-- ko if:$parent.widgetCanMoveUp($data) -->
                                <li data-bind="click:$parent.moveWidgetUp"><i class="icon-chevron-up"></i></li>
                                <!-- /ko -->
                                <!-- ko if:$parent.widgetCanMoveDown($data) -->
                                <li data-bind="click:$parent.moveWidgetDown"><i class="icon-chevron-down"></i></li>
                                <!-- /ko -->
                                <li data-bind="pluginEditor: {operation: 'edit', type: 'widget'}">
                                    <i class="icon-wrench"></i>
                                </li>
                                <li data-bind="pluginEditor: {operation: 'delete', type: 'widget'}">
                                    <i class="icon-trash"></i>
                                </li>
                            </ul>
                        </div>
                    </div>
                </section>
            </li>
        </script>
    <script>
        // function unload_dashboard() {
        //     $.post(document.location.pathname, {
        //         autosave_str: JSON.stringify(freeboard.serialize()),
        //         csrfmiddlewaretoken: "{{ csrf_token }}",
        //     }, (payload) => {
        //     });
        // };
        function onload_dashboard() {
            freeboard.setEditing(false);
        }

        function open_datasource() {
            $("#datasource-modal").show();
        }
        function close_datasource() {
            $("#datasource-modal").hide();
        }
    </script>
    <script src="/assets/Freeboard/js/bootstrap.bundle.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>
</body>

</html>