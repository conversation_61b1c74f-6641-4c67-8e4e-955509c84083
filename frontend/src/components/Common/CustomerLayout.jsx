import HomeAppBar from "./HomeAppBar";
import Footer from "./Footer";
import { Box } from "@mui/material";

function CustomerLayout({ cartItem, setCartItem, user, setUser, children, maxWidth }) {
    const align_center = { display: "flex", justifyContent: "center", padding: "0.75rem" };

    const w_1200 = { maxWidth: maxWidth || "1200px", width: "100%" };

    return (
        <div className="App" style={{ background: "white" }}>
            <HomeAppBar cartItem={cartItem} setCartItem={setCartItem} user={user} setUser={setUser} />
            <Box sx={{ width: "100%", flexGrow: 1, mt: "64px", minHeight: "100vh" }}>
                <section style={align_center}>
                    <div style={w_1200}>{children}</div>
                </section>
            </Box>
            <Footer />
        </div>
    );
}

export default CustomerLayout;
