import Grid from "@mui/material/Grid2";
import BusinessIcon from "@mui/icons-material/Business";
import PhoneIcon from "@mui/icons-material/Phone";
import EmailIcon from "@mui/icons-material/Email";
import FacebookIcon from "@mui/icons-material/Facebook";
import * as React from "react";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";
import Container from "@mui/material/Container";
import Link from "@mui/material/Link";

function Copyright() {
    return (
        <Typography variant="body2">
            {"Copyright © "}
            <Link color="inherit" href="https://beeblock.vn/">
                beeblock
            </Link>{" "}
            {new Date().getFullYear()}
            {"."}
        </Typography>
    );
}

export default function Footer() {
    return (
        <>
            <Box
                component="footer"
                sx={{
                    background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
                    color: "white",
                    mt: "20px",
                    width: "100%",
                    display: "flex",
                    justifyContent: "center",
                }}
            >
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        maxWidth: "1200px",
                        width: "100%",
                        flexDirection: "column",
                    }}
                >
                    <Grid container spacing={2} sx={{ width: "100%", padding: "20px" }}>
                        <Grid size={{ xs: 12, md: 4 }}>
                            <Box>
                                <Typography variant="h4" sx={{ mb: "15px" }}>
                                    BeE - Be Engineer
                                </Typography>
                                <i>
                                    <Typography sx={{ mb: "5px" }}>Lắp ghép sáng tạo</Typography>
                                    <Typography sx={{ mb: "5px" }}>Khám phá đam mê</Typography>
                                    <Typography sx={{ mb: "5px" }}>Làm chủ tương lai</Typography>
                                </i>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 6, md: 2 }}>
                            <Box>
                                <Typography variant="h6" sx={{ mb: "20px" }}>
                                    Chính sách
                                </Typography>
                                <Typography sx={{ mb: "5px" }}>
                                    <Link href="/chinh-sach/bao-mat" sx={{ textDecoration: "none", color: "white" }}>
                                        Bảo mật
                                    </Link>
                                </Typography>
                                <Typography sx={{ mb: "5px" }}>
                                    <Link href="/chinh-sach/bao-hanh" sx={{ textDecoration: "none", color: "white" }}>
                                        Bảo hành
                                    </Link>
                                </Typography>
                                <Typography sx={{ mb: "5px" }}>
                                    <Link href="/chinh-sach/doi-tra" sx={{ textDecoration: "none", color: "white" }}>
                                        Đổi trả
                                    </Link>
                                </Typography>
                                <Typography sx={{ mb: "5px" }}>
                                    <Link href="/chinh-sach/van-chuyen" sx={{ textDecoration: "none", color: "white" }}>
                                        Vận chuyển
                                    </Link>
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 6, md: 2 }}>
                            <Box>
                                <Typography variant="h6" sx={{ mb: "20px" }}>
                                    Hỗ trợ
                                </Typography>
                                <Typography sx={{ mb: "5px" }}>
                                    <Link
                                        href="/ho-tro/mua-hang-online"
                                        sx={{ textDecoration: "none", color: "white" }}
                                    >
                                        Mua hàng Online
                                    </Link>
                                </Typography>
                                {/* <Typography sx={{ mb: "5px" }}>
                                    <Link href="/ho-tro/cap-nhat-firmware" sx={{ textDecoration: "none", color: "white" }} >
                                        Cập nhật Firmware
                                    </Link>
                                </Typography> */}
                                {/* <Typography sx={{ mb: "5px" }} href="/De-Xuat-Khoa-Hoc">Đề xuất khóa học</Typography> */}
                                <Typography sx={{ mb: "5px" }}>
                                    <Link href="/lien-he" sx={{ textDecoration: "none", color: "white" }}>
                                        Liên hệ BeE
                                    </Link>
                                </Typography>
                            </Box>
                        </Grid>
                        <Grid size={{ xs: 12, md: 4 }}>
                            <Box>
                                <Typography variant="h6" sx={{ mb: "20px" }}>
                                    Liên hệ
                                </Typography>
                                <Box sx={{ mb: "5px", display: "flex" }}>
                                    <BusinessIcon sx={{ mr: "5px" }} />
                                    <Typography>257 Nguyễn Tất Thành, TP. Huế</Typography>
                                </Box>
                                <Box sx={{ mb: "5px", display: "flex" }}>
                                    <PhoneIcon sx={{ mr: "5px" }} />
                                    <Typography>
                                        <Link href="tel:+***********" sx={{ textDecoration: "none", color: "white" }}>
                                            (+84) 987.845.231
                                        </Link>
                                    </Typography>
                                </Box>
                                <Box sx={{ mb: "5px", display: "flex" }}>
                                    <EmailIcon sx={{ mr: "5px" }} />
                                    <Typography>
                                        <Link
                                            href="mailto:<EMAIL>"
                                            sx={{ textDecoration: "none", color: "white" }}
                                        >
                                            <EMAIL>
                                        </Link>
                                    </Typography>
                                </Box>
                                <Box sx={{ mb: "5px", display: "flex" }}>
                                    <FacebookIcon sx={{ mr: "5px" }} />
                                    <Typography>
                                        <Link
                                            href="https://fb.com/beeblock-vn"
                                            sx={{ textDecoration: "none", color: "white" }}
                                        >
                                            https://fb.com/beeblock-vn
                                        </Link>
                                    </Typography>
                                </Box>
                            </Box>
                        </Grid>
                    </Grid>
                    <Container sx={{ display: "flex", justifyContent: "center", padding: "5px" }}>
                        <Copyright />
                    </Container>
                </Box>
            </Box>
        </>
    );
}
