import PropTypes from "prop-types";
import { useLocation, useNavigate } from "react-router-dom";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Card, CardContent, CardMedia, Box, Chip } from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import { grey, red } from "@mui/material/colors";
import axiosInstance from "../../services/axiosInstance";

ProductCard.propTypes = {
    item: PropTypes.object.isRequired,
    setOpenSnackbar: PropTypes.func.isRequired,
    setCartItem: PropTypes.func.isRequired,
};

export default function ProductCard({ item, setOpenSnackbar, setCartItem }) {
    const navigate = useNavigate();
    const location = useLocation();

    function handleRedirect(href) {
        navigate(href);
        window.location.reload();
    }

    function formatPrice(number) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "VND",
        }).format(number);
    }

    function addCartItem(item) {
        setCartItem((prevItemList) => [
            ...prevItemList,
            {
                id: item.id,
                sku: item.item.sku,
                name: item.item.name,
                image: item.item.images.length > 0 ? item.item.images[0].image_url : null,
                sale_price: item.item.sale_price,
                saleOff: 0,
                quantity: item.quantity,
            },
        ]);
    }

    function handleAddToCart() {
        const formData = new FormData();
        formData.append("sku", item.sku);
        formData.append("quantity", 1);
        formData.append("price", item.sale_price);

        axiosInstance
            .post("api/shopping/cart/add", formData)
            .then((response) => {
                if (response.status === 201) {
                    setCartItem([]);
                    for (var i = 0; i < response.data.length; i++) addCartItem(response.data[i]);
                    setOpenSnackbar(true);
                }
            })
            .catch((error) => {
                if (error?.response?.status === 401) {
                    navigate("/login", { state: { from: location } });
                } else {
                    console.error("Error adding to cart:", error);
                }
            });
    }

    return (
        <Card
            sx={{
                width: "100%",
                height: "100%",
                mr: "10px",
                borderRadius: "20px",
                display: "flex",
                flexDirection: "column",
                transition: "transform 0.2s ease-in-out, background-color 0.2s ease-in-out",
                boxShadow: "0px 1px 3px rgba(0, 0, 0, 0.2)",
                "&:hover": {
                    transform: "scale(1.05)",
                    boxShadow: "0px 4px 8px rgba(0, 0, 0, 0.3)",
                    // backgroundColor: '#f0f0f0',
                },
            }}
            onClick={() => handleRedirect("/product/item/" + item.sku)}
        >
            <Box
                sx={{
                    height: "100%",
                    position: "relative",
                    cursor: "pointer",
                    display: "flex",
                    flexDirection: "column",
                }}
            >
                {item.discount_value > 0 && (
                    <Chip
                        label={
                            item.discount_type === "FIXED_AMOUNT"
                                ? `-${formatPrice(item.discount_value / 1000)}K`
                                : `-${item.discount_value}%`
                        }
                        color="error"
                        variant="contained"
                        sx={{
                            fontSize: "15px",
                            position: "absolute",
                            right: "10px",
                            top: "10px",
                        }}
                    />
                )}
                {/* <CardActionArea> */}
                <CardMedia
                    component={"img"}
                    sx={{ width: "100%", height: 200, objectFit: "contain" }}
                    image={item.images.length > 0 ? item.images[0].image_url : "/beeIco.svg"}
                    title={item.name}
                />
                <Box sx={{ display: "flex", flexDirection: "column", padding: "15px", flexGrow: 1 }}>
                    <Typography
                        sx={{
                            fontSize: "15px",
                            fontWeight: 500,
                            maxWidth: "250px",
                            whiteSpace: "nowrap",
                            overflow: "hidden",
                            textOverflow: "ellipsis",
                        }}
                    >
                        {/* {item.name.length > 35 ? item.name.substring(0, 35) + "..." : item.name} */}
                        {item.name}
                    </Typography>
                    <Box sx={{ flexGrow: 1 }} />
                    <Box sx={{ display: "flex", justifyContent: "space-between", mb: "10px" }}>
                        <Box sx={{ display: "flex", alignItems: "baseline" }}>
                            {item.discount_value > 0 ? (
                                <>
                                    <Typography
                                        sx={{
                                            fontSize: "16px",
                                            fontWeight: 500,
                                            color: red[500],
                                            mr: "5px",
                                        }}
                                    >
                                        {formatPrice(item.final_price)}
                                    </Typography>
                                    <Typography sx={{ fontSize: "13px", fontWeight: 100, color: grey[500] }}>
                                        <del>{formatPrice(item.sale_price)}</del>
                                    </Typography>
                                </>
                            ) : (
                                <Typography
                                    sx={{
                                        fontSize: "16px",
                                        fontWeight: 500,
                                        color: red[500],
                                    }}
                                >
                                    {formatPrice(item.sale_price)}
                                </Typography>
                            )}
                        </Box>
                    </Box>

                    <Button
                        sx={{
                            backgroundColor: "#1a237e",
                            color: "white",
                            width: "100%",
                            borderRadius: "10px",
                            display: { xs: "none", sm: "flex" },
                            "&:hover": {
                                backgroundColor: "#0d47a1",
                                color: "white",
                            },
                        }}
                        startIcon={<ShoppingCartIcon />}
                        onClick={(event) => {
                            event.stopPropagation();
                            handleAddToCart();
                        }}
                    >
                        Thêm vào giỏ
                    </Button>
                    <Button
                        sx={{
                            backgroundColor: "#1a237e",
                            color: "white",
                            width: "100%",
                            borderRadius: "10px",
                            display: { xs: "flex", sm: "none" },
                            "&:hover": {
                                backgroundColor: "#0d47a1",
                                color: "white",
                            },
                        }}
                        startIcon={<ShoppingCartIcon />}
                        onClick={(event) => {
                            event.stopPropagation();
                            handleAddToCart();
                        }}
                    >
                        Thêm
                    </Button>
                </Box>
                {/* </CardActionArea> */}
            </Box>
        </Card>
    );
}
