import { Alert, Snackbar } from "@mui/material";
import PropTypes from "prop-types";
import { forwardRef } from "react";
import * as React from "react";

function BeeNotification({ openSnackbar, setOpenSnackbar, status, message }) {
    const handleClose = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }

        setOpenSnackbar(false);
    };

    return (
        <div>
            <Snackbar
                anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
                open={openSnackbar}
                onClose={handleClose}
                autoHideDuration={6000}
                message={message}
                sx={{
                    "& .MuiSnackbarContent-root": { borderRadius: "10px" },
                }}
            >
                <Alert onClose={handleClose} severity={status} sx={{ borderRadius: "10px" }}>
                    {message}
                </Alert>
            </Snackbar>
        </div>
    );
}

export default BeeNotification;
