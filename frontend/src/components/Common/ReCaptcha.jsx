import React, { useRef } from "react";
import ReCAP<PERSON><PERSON> from "react-google-recaptcha";

const ReCaptcha = ({ onChange }) => {
    const recaptchaRef = useRef(null);

    const handleRecaptchaChange = (value) => {
        // G<PERSON><PERSON> hàm onChange và truyền giá trị reCAPTCHA
        onChange(value);
    };

    return <ReCAPTCHA ref={recaptchaRef} sitekey={import.meta.env.VITE_CAPTCHA_KEY} onChange={handleRecaptchaChange} />;
};

export default ReCaptcha;
