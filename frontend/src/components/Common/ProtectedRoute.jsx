import * as React from "react";
import PropTypes from "prop-types";

import { Navigate, Outlet, useLocation } from "react-router-dom";
import Loading from "./Loading";
import axiosInstance from "../../services/axiosInstance";

ProtectedRoute.propTypes = {
    isAuthenticated: PropTypes.bool.isRequired,
    setCartItem: PropTypes.func.isRequired,
    loading: PropTypes.bool.isRequired,
};

function ProtectedRoute({ isAuthenticated, setCartItem, loading }) {
    const location = useLocation();
    const [loadCart, setLoadCart] = React.useState(true);

    React.useEffect(() => {
        async function fetchData() {
            try {
                const token = localStorage.getItem("access_token") || sessionStorage.getItem("access_token");
                if (!token) {
                    setCartItem([]);
                    setLoadCart(false);
                    return;
                }

                const cartResponse = await axiosInstance.get("/api/shopping/cart/detail/view");
                if (cartResponse.status === 200)
                    setCartItem(
                        cartResponse.data.map((item) => ({
                            id: item.id,
                            sku: item.item.sku,
                            name: item.item.name,
                            image: item.item.images.length > 0 ? item.item.images[0].image_url : null,
                            sale_price: item.item.sale_price,
                            discount_value: item.item.discount_value,
                            discount_type: item.item.discount_type,
                            final_price: item.item.final_price,
                            quantity: item.quantity,
                            inventory_quantity: item.item.quantity,
                            attributes: item.item.attributes,
                            visible: item.item.visible,
                        }))
                    );
            } catch (err) {
                // console.error(err.response.data)
            } finally {
                setLoadCart(false);
            }
        }

        fetchData();
    }, []);

    if (loading) return <Loading />;
    if (loadCart) return <Loading />;

    if (!isAuthenticated) {
        // Redirect them to the login page, but save the current location they were trying to go to
        return <Navigate to="/login" state={{ from: location }} />;
    }

    return <Outlet />;
}

export default ProtectedRoute;
