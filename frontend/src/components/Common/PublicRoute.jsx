import * as React from "react";
import PropTypes from "prop-types";

import { Outlet } from "react-router-dom";
import Loading from "./Loading";
import axiosInstance from "../../services/axiosInstance";

PublicRoute.propTypes = {
    setCartItem: PropTypes.func.isRequired,
    loading: PropTypes.bool.isRequired,
};

function PublicRoute({ setCartItem, loading }) {
    const [loadCart, setLoadCart] = React.useState(true);

    React.useEffect(() => {
        async function fetchData() {
            try {
                const token = localStorage.getItem("access_token") || sessionStorage.getItem("access_token");
                if (!token) {
                    setCartItem([]);
                    setLoadCart(false);
                    return;
                }

                const cartResponse = await axiosInstance.get("/api/shopping/cart/detail/view");
                if (cartResponse.status === 200)
                    setCartItem(
                        cartResponse.data.map((item) => ({
                            id: item.id,
                            sku: item.item.sku,
                            name: item.item.name,
                            image: item.item.images.length > 0 ? item.item.images[0].image_url : null,
                            sale_price: item.item.sale_price,
                            saleOff: 0,
                            quantity: item.quantity,
                        }))
                    );
            } catch (err) {
                // console.error(JSON.stringify(err.response.data))
            } finally {
                setLoadCart(false);
            }
        }

        fetchData();
    }, []);

    if (loading) return <Loading />;
    if (loadCart) return <Loading />;

    return <Outlet />;
}

export default PublicRoute;
