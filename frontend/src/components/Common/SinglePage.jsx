import * as React from "react";
import { useNavigate, useParams } from "react-router-dom";
import CustomerLayout from "./CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import Loading from "./Loading";
import { Typography } from "@mui/material";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

function SinglePage({ user, setUser, cartItem, setCartItem }) {
    const { query_slug } = useParams();
    const navigate = useNavigate();
    const [loading, setLoading] = React.useState(true);

    const [policyContent, setPolicyContent] = React.useState({
        title: "",
        slug: "",
        content: "",
    });

    useDocumentTitle(policyContent?.title ? policyContent.title : "BeE");

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get(`/api/admin/public-single-page/${query_slug}`);
                setPolicyContent({
                    title: response.data["title"],
                    slug: response.data["slug"],
                    content: response.data["content"],
                });
            } catch (err) {
                console.error(err);
                navigate("/page-not-found");
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    if (loading) {
        <Loading />;
    }

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            <Typography
                sx={{
                    mb: 8,
                    width: { xs: "100%", md: "100%" },
                    overflowWrap: "break-word",
                    wordWrap: "break-word",
                    hyphens: "auto",
                    maxWidth: "100%",
                    overflow: "hidden",
                    whiteSpace: "normal",
                    textAlign: "justify",
                    "& img": {
                        maxWidth: "100%",
                        height: "auto",
                        display: "block",
                        margin: "auto",
                    },
                    "& img[style]": {
                        width: { sm: "100% !important", md: "75% !important" },
                        height: "auto !important",
                    },
                }}
                dangerouslySetInnerHTML={{ __html: policyContent.content }}
            />
        </CustomerLayout>
    );
}

export default SinglePage;
