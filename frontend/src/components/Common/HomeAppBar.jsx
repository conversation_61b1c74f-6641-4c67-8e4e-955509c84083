import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import Container from "@mui/material/Container";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import { Badge, Divider, Drawer, List, ListItem, ListItemButton, ListItemIcon, ListItemText } from "@mui/material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import LogoutIcon from "@mui/icons-material/Logout";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import { deepOrange, grey } from "@mui/material/colors";
import authService from "../../services/authService";
import { useNavigate } from "react-router-dom";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import FeedIcon from "@mui/icons-material/Feed";
import DashboardIcon from "@mui/icons-material/Dashboard";
import PointOfSaleIcon from "@mui/icons-material/PointOfSale";
import WidgetsIcon from "@mui/icons-material/Widgets";

const pages = [
    // {
    //     'title': 'Dự án của tôi',
    //     'link': '/MyProject'
    // },
    // {
    // 'title': 'Khóa học',
    // 'link': '/BeeCourse'
    // },
    {
        title: "Cửa hàng",
        link: "/shop",
        icon: <ShoppingCartIcon />,
    },
    {
        title: "Bài viết",
        link: "/blog",
        icon: <FeedIcon />,
    },
    {
        title: "BeE Studio",
        link: "/studio",
        icon: <WidgetsIcon />,
    },
    {
        title: "E-Learning",
        link: "/e-learning",
        icon: <WidgetsIcon />,
    },
];
const settings = [
    {
        title: "Cá nhân",
        link: "/profile",
        icon: <AccountCircleIcon />,
    },
    {
        title: "Đơn hàng",
        link: "/profile?target=MyCarts",
        icon: <ShoppingCartIcon />,
    },
];

function HomeAppBar(props) {
    const navigate = useNavigate();

    const [anchorElUser, setAnchorElUser] = React.useState(null);
    const [open, setOpen] = React.useState(false);

    const toggleDrawer = (newOpen) => () => {
        setOpen(newOpen);
    };

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const BeEBlock = (
        <Typography
            variant="h6"
            noWrap
            component="a"
            href="/"
            sx={{
                ml: 1,
                mr: 2,
                display: { xs: "none", md: "flex" },
                color: "inherit",
                textDecoration: "none",
                textAlign: "center",
                verticalAlign: "center",
            }}
        >
            BeE STEM Solutions
        </Typography>
    );

    const DrawerList = (
        <Box
            sx={{
                width: "100%",
                background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
            }}
            role="presentation"
            onClick={toggleDrawer(false)}
        >
            <List>
                <ListItem
                    disablePadding
                    secondaryAction={
                        <IconButton edge="end" aria-label="delete" sx={{ color: "white" }}>
                            <CloseIcon />
                        </IconButton>
                    }
                >
                    <ListItemButton href="/">
                        <ListItemIcon>
                            <img
                                alt="beeblock"
                                src="/beeIco.svg"
                                style={{
                                    width: "40px",
                                    cursor: "pointer",
                                    objectFit: "contain",
                                }}
                                onClick={() => navigate("/")}
                            />
                        </ListItemIcon>
                        <ListItemText sx={{ color: "#F1C40F" }} primary="BeE STEM Solutions" />
                    </ListItemButton>
                </ListItem>
                <Divider sx={{ backgroundColor: "white" }} />
                {pages.map((page, index) => (
                    <ListItem key={index} disablePadding>
                        <ListItemButton href={page.link}>
                            <ListItemIcon sx={{ color: "white" }}>{page.icon}</ListItemIcon>
                            <ListItemText primary={page.title} sx={{ color: "white" }} />
                        </ListItemButton>
                    </ListItem>
                ))}
                {props.user && (props.user.is_supervisor || props.user.is_admin) && (
                    <ListItem disablePadding>
                        <ListItemButton href="/admin/dashboard">
                            <ListItemIcon sx={{ color: "white" }}>
                                <DashboardIcon />
                            </ListItemIcon>
                            <ListItemText primary="Dashboard" sx={{ color: "white" }} />
                        </ListItemButton>
                    </ListItem>
                )}
                {props.user && (props.user.is_supervisor || props.user.is_admin || props.user.is_saler) && (
                    <ListItem disablePadding>
                        <ListItemButton href="/pos">
                            <ListItemIcon sx={{ color: "white" }}>
                                <PointOfSaleIcon />
                            </ListItemIcon>
                            <ListItemText primary="POS" sx={{ color: "white" }} />
                        </ListItemButton>
                    </ListItem>
                )}
            </List>
        </Box>
    );

    return (
        <AppBar
            position="fixed"
            sx={{
                background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
                width: "100%",
                left: 0,
                right: 0,
            }}
        >
            <Container
                maxWidth="xl"
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    paddingLeft: 1,
                    width: "100%",
                }}
            >
                <Toolbar disableGutters sx={{ maxWidth: "1200px", width: "100%" }}>
                    <Box
                        sx={{
                            display: "flex",
                            width: "100%",
                            justifyContent: "space-between",
                        }}
                    >
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <IconButton
                                onClick={toggleDrawer(true)}
                                sx={{
                                    borderRadius: "20px",
                                    color: "white",
                                    mr: 1,
                                    display: { xs: "flex", md: "none" },
                                }}
                            >
                                <MenuIcon />
                            </IconButton>
                            <Drawer open={open} onClose={toggleDrawer(false)} anchor="top">
                                {DrawerList}
                            </Drawer>
                            <img
                                alt="beeblock"
                                src="/beeIco.svg"
                                style={{ width: "56px", cursor: "pointer" }}
                                onClick={() => navigate("/")}
                            />
                            <Typography
                                variant="h6"
                                noWrap
                                component="a"
                                href="/"
                                sx={{
                                    ml: 1,
                                    mr: 2,
                                    display: { xs: "none", md: "flex" },
                                    color: "inherit",
                                    textDecoration: "none",
                                    textAlign: "center",
                                    verticalAlign: "center",
                                }}
                            >
                                BeE STEM Solutions
                            </Typography>
                            <Box sx={{ display: { xs: "none", md: "flex" } }}>
                                {pages.map((page, index) => (
                                    <Button
                                        disableRipple
                                        key={index}
                                        href={page.link}
                                        sx={{
                                            minWidth: "20px !important",
                                            color: "white",
                                            display: "block",
                                            textTransform: "none",
                                            "&:hover": {
                                                backgroundColor: "transparent",
                                            },
                                        }}
                                    >
                                        {page.title}
                                    </Button>
                                ))}
                                {props.user && (props.user.is_supervisor || props.user.is_admin) && (
                                    <Button
                                        disableRipple
                                        href="/admin/dashboard"
                                        sx={{
                                            color: "white",
                                            display: "block",
                                            textTransform: "none",
                                            "&:hover": {
                                                backgroundColor: "transparent",
                                            },
                                        }}
                                    >
                                        Dashboard
                                    </Button>
                                )}
                                {props.user &&
                                    (props.user.is_supervisor || props.user.is_admin || props.user.is_saler) && (
                                        <Button
                                            disableRipple
                                            href="/pos"
                                            sx={{
                                                color: "white",
                                                display: "block",
                                                textTransform: "none",
                                                "&:hover": {
                                                    backgroundColor: "transparent",
                                                },
                                            }}
                                        >
                                            POS
                                        </Button>
                                    )}
                            </Box>
                        </Box>
                        <Box sx={{ display: "flex" }}>
                            <IconButton
                                sx={{ color: "white", mr: "5px" }}
                                aria-label="shopping-cart"
                                href={props.user ? "/cart" : "/login"}
                            >
                                <Badge badgeContent={props.cartItem.length} color="error">
                                    <ShoppingCartIcon />
                                </Badge>
                            </IconButton>
                            <Divider orientation="vertical" sx={{ mr: "15px" }} flexItem />
                            {props.user ? (
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                    }}
                                >
                                    <Typography variant="h4" sx={{ fontSize: 14, mr: "10px" }}>
                                        Chào, {props.user.last_name}
                                    </Typography>
                                    <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                                        {props.user.avatar_url ? (
                                            <Avatar
                                                alt={props.user.username}
                                                src={props.user.avatar_url}
                                                sx={{
                                                    bgcolor: "none",
                                                    border: "1px solid #ebebeb",
                                                }}
                                            />
                                        ) : (
                                            <Avatar
                                                alt={props.user.username}
                                                sx={{
                                                    bgcolor: deepOrange[500],
                                                }}
                                            />
                                        )}
                                    </IconButton>
                                </Box>
                            ) : (
                                <Button
                                    href="/login"
                                    sx={{
                                        color: "white",
                                        display: "block",
                                        mr: "10px",
                                        border: "1px solid white",
                                        borderRadius: "10px",
                                        "&:hover": {
                                            backgroundColor: "transparent",
                                        },
                                    }}
                                >
                                    Đăng nhập
                                </Button>
                            )}
                        </Box>
                    </Box>
                </Toolbar>
            </Container>
            <Menu
                sx={{
                    "& .MuiPaper-root": {
                        borderRadius: "10px",
                    },
                    width: "200px",
                }}
                id="menu-appbar"
                anchorEl={anchorElUser}
                slotProps={{
                    paper: {
                        elevation: 0,
                        sx: {
                            overflow: "visible",
                            filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                            mt: 1.5,
                            "& .MuiAvatar-root": {
                                width: 32,
                                height: 32,
                                ml: -0.5,
                                mr: 1,
                            },
                            "&::before": {
                                content: '""',
                                display: "block",
                                position: "absolute",
                                top: 0,
                                right: 14,
                                width: 10,
                                height: 10,
                                bgcolor: "background.paper",
                                transform: "translateY(-50%) rotate(45deg)",
                                zIndex: 0,
                            },
                        },
                    },
                }}
                transformOrigin={{ horizontal: "right", vertical: "top" }}
                anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                keepMounted
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
            >
                {settings.map((setting) => (
                    <MenuItem key={setting.title} onClick={handleCloseUserMenu}>
                        <Button
                            sx={{
                                color: grey[600],
                                textTransform: "none",
                            }}
                            href={setting.link}
                            size="small"
                            startIcon={setting.icon}
                        >
                            {setting.title}
                        </Button>
                    </MenuItem>
                ))}
                <Divider></Divider>
                <MenuItem>
                    <Button
                        onClick={() => {
                            authService.logout();
                            props.setUser(null);
                            props.setCartItem([]);
                            setAnchorElUser(false);
                            navigate("/");
                        }}
                        size="small"
                        sx={{
                            color: grey[600],
                            textTransform: "none",
                        }}
                        startIcon={<LogoutIcon />}
                    >
                        Đăng xuất
                    </Button>
                </MenuItem>
            </Menu>
        </AppBar>
    );
}
export default HomeAppBar;
