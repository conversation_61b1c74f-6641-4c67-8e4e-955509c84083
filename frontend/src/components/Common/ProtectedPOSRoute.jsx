import React from "react";
import { Navigate, Outlet, useLocation } from "react-router-dom";
import Loading from "./Loading";
import PageNotFound from "../404";

function ProtectedPOSRoute({ user, loading }) {
    const location = useLocation();

    if (loading) return <Loading />;

    if (!user) {
        // Redirect them to the login page, but save the current location they were trying to go to
        return <Navigate to="/login" state={{ from: location }} />;
    }

    if (!(user.is_staff || user.is_admin || user.is_supervisor)) {
        return <PageNotFound />;
    }

    return <Outlet />;
}

export default ProtectedPOSRoute;
