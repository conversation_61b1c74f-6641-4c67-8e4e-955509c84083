import React from "react";
import "react-date-range/dist/styles.css"; // main css file
import "react-date-range/dist/theme/default.css"; // theme css file
import vi from "date-fns/locale/vi"; // Import Vietnamese locale
import { DateRangePicker, createStaticRanges } from "react-date-range";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import { Box, Button, Menu } from "@mui/material";

function CustomDatePicker({ dateFrom, setDateFrom, dateTo, setDateTo }) {
    const [state, setState] = React.useState([
        {
            startDate: dateFrom, // subDays(new Date(), 7),
            endDate: dateTo, // addDays(new Date(), 1),
            key: "selection",
        },
    ]);

    const customStaticRanges = createStaticRanges([
        {
            label: "Hôm nay",
            range: () => ({ startDate: new Date(), endDate: new Date() }),
        },
        {
            label: "Hôm qua",
            range: () => {
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                return { startDate: yesterday, endDate: yesterday };
            },
        },
        {
            label: "Tuần này",
            range: () => {
                const today = new Date();
                const start = new Date(today.setDate(today.getDate() - today.getDay() + 1));
                return { startDate: start, endDate: new Date() };
            },
        },
        {
            label: "Tuần trước",
            range: () => {
                const today = new Date();
                const end = new Date(today.setDate(today.getDate() - today.getDay()));
                const start = new Date(today.setDate(end.getDate() - 6));
                return { startDate: start, endDate: end };
            },
        },
        {
            label: "Tháng này",
            range: () => {
                const today = new Date();
                const start = new Date(today.getFullYear(), today.getMonth(), 1);
                const end = new Date(today.getFullYear(), today.getMonth() + 1, 0);
                return { startDate: start, endDate: end };
            },
        },
        {
            label: "Tháng trước",
            range: () => {
                const today = new Date();
                const start = new Date(today.getFullYear(), today.getMonth() - 1, 1);
                const end = new Date(today.getFullYear(), today.getMonth(), 0);
                return { startDate: start, endDate: end };
            },
        },
    ]);

    const handleOnChange = (ranges) => {
        const { selection } = ranges;
        // onChange(selection);
        setState([selection]);
    };

    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);
    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleClose = () => {
        setAnchorEl(null);
    };

    const handleConfirm = () => {
        setDateFrom(state[0].startDate);
        setDateTo(state[0].endDate);
        setAnchorEl(null);
    };

    const formatDate = (date) => {
        return date.toLocaleDateString("vi-VN", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
        });
    };

    return (
        <Box className="lg:col-span-3 mb-4 flex items-center gap-2">
            <Button
                variant="outlined"
                onClick={handleClick}
                // size="small"
                sx={{ borderRadius: "10px" }}
                endIcon={<CalendarMonthIcon />}
            >
                {`${formatDate(dateFrom)} - ${formatDate(dateTo)}`}
            </Button>
            <Menu
                id="basic-menu"
                anchorEl={anchorEl}
                open={open}
                onClose={handleClose}
                MenuListProps={{
                    "aria-labelledby": "basic-button",
                }}
                sx={{
                    "& .MuiPaper-root": {
                        borderRadius: "10px",
                    },
                }}
                slotProps={{
                    paper: {
                        elevation: 0,
                        sx: {
                            // width: "200px",
                            overflow: "visible",
                            filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                            mt: 1.5,
                            "& .MuiAvatar-root": {
                                width: 32,
                                height: 32,
                                ml: -0.5,
                                mr: 1,
                            },
                            "&::before": {
                                content: '""',
                                display: "block",
                                position: "absolute",
                                top: 0,
                                right: 14,
                                width: 10,
                                height: 10,
                                bgcolor: "background.paper",
                                transform: "translateY(-50%) rotate(45deg)",
                                zIndex: 0,
                            },
                        },
                    },
                }}
                transformOrigin={{
                    horizontal: "right",
                    vertical: "top",
                }}
                anchorOrigin={{
                    horizontal: "right",
                    vertical: "bottom",
                }}
            >
                <Box sx={{ display: "flex", flexDirection: "column" }}>
                    <DateRangePicker
                        onChange={handleOnChange}
                        staticRanges={customStaticRanges}
                        showSelectionPreview={true}
                        moveRangeOnFirstSelection={false}
                        months={1}
                        ranges={state}
                        direction="horizontal"
                        locale={vi} // Set Vietnamese locale
                    />
                    <Button
                        sx={{
                            ml: "10px",
                            mr: "10px",
                            mt: "5px",
                            borderRadius: "10px",
                        }}
                        onClick={handleConfirm}
                    >
                        Đồng ý
                    </Button>
                </Box>
            </Menu>
        </Box>
    );
}

export default CustomDatePicker;
