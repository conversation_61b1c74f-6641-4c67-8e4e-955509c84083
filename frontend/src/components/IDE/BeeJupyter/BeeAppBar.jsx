import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import Container from "@mui/material/Container";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import MenuItem from "@mui/material/MenuItem";
import {
    Divider,
    Drawer,
    List,
    ListItem,
    ListItemButton,
    ListItemIcon,
    ListItemText,
    ToggleButton,
    ToggleButtonGroup,
    Tooltip,
} from "@mui/material";
import LogoutIcon from "@mui/icons-material/Logout";
import { deepOrange, grey } from "@mui/material/colors";
import { useNavigate } from "react-router-dom";
import MenuIcon from "@mui/icons-material/Menu";
import CloseIcon from "@mui/icons-material/Close";
import authService from "../../../services/authService";
import CustomTextField from "../../Common/CustomTextField";
import SaveIcon from "@mui/icons-material/Save";
import FolderOpenIcon from "@mui/icons-material/FolderOpen";
import UsbIcon from "@mui/icons-material/Usb";
import UsbOffIcon from "@mui/icons-material/UsbOff";
import MemoryIcon from "@mui/icons-material/Memory";
import FullscreenIcon from "@mui/icons-material/Fullscreen";
import FullscreenExitIcon from "@mui/icons-material/FullscreenExit";

function BeeAppBar({
    user,
    setUser,
    project,
    setProject,
    handleConnectSerial,
    handleSave,
    handleLoad,
    platform,
    setPlatform,
}) {
    const navigate = useNavigate();
    const fileInputRef = React.useRef(null);

    const [anchorElUser, setAnchorElUser] = React.useState(null);
    const [open, setOpen] = React.useState(false);
    const [fullScreen, setFullScreen] = React.useState(false);

    const handlePlatformChange = (event, newPlatform) => {
        setPlatform(newPlatform);
    };

    // Add event listener for fullscreen changes
    React.useEffect(() => {
        const handleFullscreenChange = () => {
            setFullScreen(!!document.fullscreenElement);
        };

        document.addEventListener("fullscreenchange", handleFullscreenChange);
        return () => {
            document.removeEventListener("fullscreenchange", handleFullscreenChange);
        };
    }, []);

    const handleFullScreen = () => {
        if (!fullScreen) {
            // Request fullscreen
            if (document.documentElement.requestFullscreen) {
                document.documentElement.requestFullscreen();
            } else if (document.documentElement.mozRequestFullScreen) {
                // Firefox
                document.documentElement.mozRequestFullScreen();
            } else if (document.documentElement.webkitRequestFullscreen) {
                // Chrome, Safari
                document.documentElement.webkitRequestFullscreen();
            } else if (document.documentElement.msRequestFullscreen) {
                // IE/Edge
                document.documentElement.msRequestFullscreen();
            }
        } else {
            // Exit fullscreen
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) {
                // Firefox
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) {
                // Chrome, Safari
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) {
                // IE/Edge
                document.msExitFullscreen();
            }
        }
    };

    const toggleDrawer = (newOpen) => () => {
        setOpen(newOpen);
    };

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const BeEBlock = (
        <Typography
            variant="h6"
            noWrap
            component="a"
            href="/studio"
            sx={{
                ml: 1,
                mr: 2,
                display: { xs: "none", md: "flex" },
                color: "inherit",
                textDecoration: "none",
                textAlign: "center",
                verticalAlign: "center",
            }}
        >
            BeE IDE
        </Typography>
    );

    const DrawerList = (
        <Box sx={{ width: "100%" }} role="presentation" onClick={toggleDrawer(false)}>
            <List>
                <ListItem
                    disablePadding
                    secondaryAction={
                        <IconButton edge="end" aria-label="delete">
                            <CloseIcon />
                        </IconButton>
                    }
                >
                    <ListItemButton href="/studio">
                        <ListItemIcon>
                            <img
                                alt="beeblock"
                                src="/beeIco.svg"
                                style={{ width: "40px", cursor: "pointer", objectFit: "contain" }}
                                onClick={() => navigate("/studio")}
                            />
                        </ListItemIcon>
                        <ListItemText sx={{ color: "#F1C40F" }} primary="BeE IDE" />
                    </ListItemButton>
                </ListItem>
                <Divider />
                {/* TODO: Add something */}
            </List>
        </Box>
    );

    return (
        <AppBar
            position="fixed"
            sx={{ background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)", width: "100%", left: 0, right: 0 }}
        >
            <Container
                maxWidth="100%"
                sx={{ display: "flex", justifyContent: "center", paddingLeft: 1, width: "100%" }}
            >
                <Toolbar disableGutters sx={{ width: "100%", height: "50px" }}>
                    <Box sx={{ display: "flex", width: "100%", justifyContent: "space-between" }}>
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <IconButton
                                onClick={toggleDrawer(true)}
                                sx={{
                                    borderRadius: "20px",
                                    color: "white",
                                    mr: 1,
                                    display: { xs: "none", md: "none" },
                                }}
                            >
                                <MenuIcon />
                            </IconButton>
                            <Drawer open={open} onClose={toggleDrawer(false)} anchor="top">
                                {DrawerList}
                            </Drawer>
                            <img
                                alt="beeblock"
                                src="/beeIco.svg"
                                style={{ width: "56px", cursor: "pointer" }}
                                onClick={() => navigate("/studio")}
                            />
                            <Typography
                                variant="h6"
                                noWrap
                                component="a"
                                href="/studio"
                                sx={{
                                    ml: 1,
                                    mr: 2,
                                    display: { xs: "none", md: "flex" },
                                    color: "inherit",
                                    textDecoration: "none",
                                    textAlign: "center",
                                    verticalAlign: "center",
                                }}
                            >
                                BeE IDE
                            </Typography>
                            <Box sx={{ display: { xs: "none", md: "flex" } }}>
                                <CustomTextField
                                    placeholder="Project name"
                                    size="small"
                                    value={project.name}
                                    onChange={(event) =>
                                        setProject((prev) => ({
                                            ...prev,
                                            name: event.target.value,
                                        }))
                                    }
                                    sx={{ mr: 1 }}
                                    slotProps={{
                                        input: {
                                            style: { backgroundColor: "white", paddingLeft: "5px" },
                                        },
                                    }}
                                />
                                <Tooltip title="Save as">
                                    <IconButton onClick={handleSave} sx={{ color: "white", mr: 1 }}>
                                        <SaveIcon />
                                    </IconButton>
                                </Tooltip>
                                <Tooltip title="Open from file...">
                                    <IconButton
                                        onClick={() => fileInputRef.current?.click()}
                                        sx={{ color: "white", mr: 1 }}
                                    >
                                        <FolderOpenIcon />
                                    </IconButton>
                                </Tooltip>
                                <input
                                    type="file"
                                    ref={fileInputRef}
                                    style={{ display: "none" }}
                                    accept=".py"
                                    onChange={handleLoad}
                                />
                                {/* <Tooltip title="Select Board...">
                                    <IconButton sx={{ color: "white", mr: 1 }}>
                                        <MemoryIcon />
                                    </IconButton>
                                </Tooltip> */}
                                {/* <Tooltip title="Connect">
                                    <IconButton sx={{ color: "white" }} onClick={handleConnectSerial}>
                                        {project.serialConnected ? <UsbIcon /> : <UsbOffIcon />}
                                    </IconButton>
                                </Tooltip> */}
                                <ToggleButtonGroup
                                    // color="primary"
                                    value={platform}
                                    exclusive
                                    onChange={handlePlatformChange}
                                    aria-label="Platform"
                                    size="small"
                                >
                                    <ToggleButton
                                        value="python"
                                        sx={{
                                            borderRadius: "10px",
                                            width: "80px",
                                            color: platform === "python" ? "white !important" : "#1a237e !important",
                                            backgroundColor: platform === "python" ? "#666" : "white",
                                            border: "1px solid white",
                                            "&:hover": {
                                                backgroundColor: platform === "python" ? "#555" : "#f5f5f5",
                                                scale: 1.05,
                                                animation: "pulse 1s infinite",
                                            },
                                            fontWeight: platform === "python" ? 600 : 400,
                                        }}
                                    >
                                        Python
                                    </ToggleButton>{" "}
                                    d
                                    <ToggleButton
                                        value="board"
                                        sx={{
                                            borderRadius: "10px",
                                            width: "80px",
                                            color: platform === "board" ? "white !important" : "#1a237e !important",
                                            backgroundColor: platform === "board" ? "#666" : "white",
                                            border: "1px solid white",
                                            "&:hover": {
                                                backgroundColor: platform === "board" ? "#555" : "#f5f5f5",
                                                scale: 1.05,
                                                animation: "pulse 1s infinite",
                                            },
                                            fontWeight: platform === "board" ? 600 : 400,
                                        }}
                                        onClick={handleConnectSerial}
                                    >
                                        Board
                                    </ToggleButton>
                                </ToggleButtonGroup>
                            </Box>
                        </Box>
                        <Box sx={{ display: "flex" }}>
                            <Tooltip title={fullScreen ? "Exit Fullscreen" : "Fullscreen"}>
                                <IconButton sx={{ color: "white", mr: 2 }} onClick={handleFullScreen}>
                                    {fullScreen ? <FullscreenExitIcon /> : <FullscreenIcon />}
                                </IconButton>
                            </Tooltip>
                            {user ? (
                                <>
                                    <Divider orientation="vertical" sx={{ mr: "10px" }} flexItem />
                                    <Box sx={{ display: "flex", alignItems: "center" }}>
                                        <Typography variant="h4" sx={{ fontSize: 14, mr: "10px" }}>
                                            Chào, {user.last_name}
                                        </Typography>
                                        <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                                            {user.avatar_url ? (
                                                <Avatar
                                                    alt={user.username}
                                                    src={user.avatar_url}
                                                    sx={{
                                                        bgcolor: "none",
                                                        border: "1px solid #ebebeb",
                                                    }}
                                                />
                                            ) : (
                                                <Avatar
                                                    alt={user.username}
                                                    sx={{
                                                        bgcolor: deepOrange[500],
                                                    }}
                                                />
                                            )}
                                        </IconButton>
                                    </Box>
                                </>
                            ) : (
                                <Button
                                    href="/login"
                                    sx={{
                                        color: "white",
                                        display: "block",
                                        mr: "10px",
                                        border: "1px solid white",
                                        borderRadius: "10px",
                                        "&:hover": {
                                            backgroundColor: "transparent",
                                        },
                                    }}
                                >
                                    Đăng nhập
                                </Button>
                            )}
                        </Box>
                    </Box>
                </Toolbar>
            </Container>
            <Menu
                sx={{
                    "& .MuiPaper-root": {
                        borderRadius: "10px",
                    },
                    width: "200px",
                }}
                id="menu-appbar"
                anchorEl={anchorElUser}
                slotProps={{
                    paper: {
                        elevation: 0,
                        sx: {
                            overflow: "visible",
                            filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                            mt: 1.5,
                            "& .MuiAvatar-root": {
                                width: 32,
                                height: 32,
                                ml: -0.5,
                                mr: 1,
                            },
                            "&::before": {
                                content: '""',
                                display: "block",
                                position: "absolute",
                                top: 0,
                                right: 14,
                                width: 10,
                                height: 10,
                                bgcolor: "background.paper",
                                transform: "translateY(-50%) rotate(45deg)",
                                zIndex: 0,
                            },
                        },
                    },
                }}
                transformOrigin={{ horizontal: "right", vertical: "top" }}
                anchorOrigin={{ horizontal: "right", vertical: "bottom" }}
                keepMounted
                open={Boolean(anchorElUser)}
                onClose={handleCloseUserMenu}
            >
                {/* TODO: add something */}
                {/* <Divider></Divider> */}
                <MenuItem>
                    <Button
                        onClick={() => {
                            authService.logout();
                            setUser(null);
                            navigate("/");
                        }}
                        size="small"
                        sx={{
                            color: grey[600],
                            textTransform: "none",
                        }}
                        startIcon={<LogoutIcon />}
                    >
                        Đăng xuất
                    </Button>
                </MenuItem>
            </Menu>
        </AppBar>
    );
}
export default BeeAppBar;
