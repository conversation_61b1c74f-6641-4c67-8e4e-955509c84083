import { Avatar, Box, Chip, Divider, Paper, Typography, Container, IconButton, Button, Tooltip } from "@mui/material";
import React from "react";
import { grey } from "@mui/material/colors";
import Grid from "@mui/material/Grid2";
import Loading from "../Common/Loading";
import BlogCard from "./BlogCard";
import axiosInstance from "../../services/axiosInstance";

import NavigateNextIcon from "@mui/icons-material/NavigateNext";
import NavigateBeforeIcon from "@mui/icons-material/NavigateBefore";

function BlogComponent({ blog, maxWidth, viewOnly }) {
    const [relatedBlogs, setRelatedBlogs] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchData = async () => {
            if (viewOnly) {
                setLoading(false);
                return;
            }

            try {
                const response = await axiosInstance.get(`/api/shopping/home/<USER>/related/${blog.id}`);
                setRelatedBlogs(response.data);
            } catch (error) {
                console.error("Lỗi:", error);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    function getDate(date) {
        const dateObject = new Date(date);
        return dateObject.toISOString().split("T")[0];
    }

    return (
        <Box
            sx={{
                backgroundColor: "#f5f5f5",
                minHeight: "100vh",
                borderRadius: "30px",
                width: { md: maxWidth, xs: "100%" },
            }}
        >
            {/* Hero Section with Full-width Image */}
            <Box
                sx={{
                    position: "relative",
                    width: "100%",
                    height: "70vh",
                    overflow: "hidden",
                    "&::before": {
                        content: '""',
                        position: "absolute",
                        top: 0,
                        left: 0,
                        width: "100%",
                        height: "100%",
                        background: "linear-gradient(to bottom, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.7) 100%)",
                        backdropFilter: "blur(5px)",
                        zIndex: 1,
                    },
                    borderRadius: "20px",
                }}
            >
                <img
                    src={blog.cover_image_url || "/smart-home.jpeg"}
                    alt={blog.title}
                    style={{
                        width: "100%",
                        height: "100%",
                        objectFit: "cover",
                        position: "absolute",
                        top: 0,
                        left: 0,
                        borderRadius: "20px",
                    }}
                />
                <Container maxWidth="lg" sx={{ height: "100%", position: "relative", zIndex: 2 }}>
                    <Box
                        sx={{
                            position: "absolute",
                            bottom: "15%",
                            color: "white",
                            maxWidth: "800px",
                        }}
                    >
                        <Typography
                            // variant="h3"
                            sx={{
                                typography: {
                                    xs: "h4",
                                    md: "h3",
                                },
                                fontWeight: 500,
                                textShadow: "2px 2px 4px rgba(0,0,0,0.3)",
                                mb: 3,
                            }}
                        >
                            {blog.title}
                        </Typography>
                        <Chip
                            label={blog.category.name}
                            sx={{
                                mb: 2,
                                backgroundColor: "rgba(255,255,255,0.9)",
                                fontWeight: 500,
                            }}
                        />
                        <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                            <Avatar
                                src={blog.avatar_url}
                                alt={blog.author_name}
                                sx={{
                                    width: 56,
                                    height: 56,
                                    border: "2px solid white",
                                }}
                            />
                            <Box>
                                <Typography variant="h6" sx={{ fontWeight: 500 }}>
                                    {blog.author_name || "BeE Team"}
                                </Typography>
                                <Typography variant="body2">
                                    {getDate(blog.created_at || new Date())} · 5 phút đọc
                                </Typography>
                            </Box>
                        </Box>
                    </Box>
                </Container>
            </Box>

            <Container maxWidth="lg" sx={{ mt: -8, position: "relative", zIndex: 3, padding: 2, paddingBottom: 3 }}>
                <Grid container spacing={4}>
                    {/* Main Content */}
                    <Grid size={{ md: 12, xs: 12 }}>
                        <Paper
                            sx={{
                                p: 5,
                                borderRadius: 4,
                                boxShadow: "0 4px 24px rgba(0,0,0,0.1)",
                            }}
                        >
                            {/* Social Share Buttons */}
                            {/* <Box
								sx={{
									position: 'sticky',
									top: 100,
									left: -80,
									display: { xs: 'none', lg: 'flex' },
									flexDirection: 'column',
									gap: 1,
									float: 'left',
									ml: -10
								}}
							>
								<IconButton sx={{ backgroundColor: '#f5f5f5' }}>
									<FacebookIcon />
								</IconButton>
								<IconButton sx={{ backgroundColor: '#f5f5f5' }}>
									<TwitterIcon />
								</IconButton>
								<IconButton sx={{ backgroundColor: '#f5f5f5' }}>
									<LinkedInIcon />
								</IconButton>
								<IconButton sx={{ backgroundColor: '#f5f5f5' }}>
									<BookmarkBorderIcon />
								</IconButton>
							</Box> */}

                            {/* Article Content */}
                            <Typography
                                component="article"
                                sx={{
                                    fontSize: "1.2rem",
                                    lineHeight: 1.8,
                                    color: "#2c3e50",
                                    "& h2": {
                                        fontSize: "2rem",
                                        fontWeight: 700,
                                        mt: 6,
                                        mb: 3,
                                    },
                                    "& p": {
                                        mb: 3,
                                    },
                                    "& img": {
                                        maxWidth: "100%",
                                        height: "auto",
                                        display: "block",
                                        margin: "auto",
                                    },
                                    "& img[style]": {
                                        width: "100% !important",
                                        height: "auto !important",
                                    },
                                }}
                                dangerouslySetInnerHTML={{ __html: blog.content }}
                            />

                            {/* Tags */}
                            {blog.tags.length > 0 && (
                                <Box sx={{ mt: 6, display: "flex", alignItems: "center" }}>
                                    <Typography sx={{ mr: 1, fontWeight: 600 }}>Tag:</Typography>
                                    {blog.tags.map((tag, index) => (
                                        <Chip
                                            key={index}
                                            label={`#${tag.name}`}
                                            sx={{
                                                mr: 1,
                                                // mb: 1,
                                                backgroundColor: "#f0f0f0",
                                                "&:hover": {
                                                    backgroundColor: "#e0e0e0",
                                                },
                                            }}
                                        />
                                    ))}
                                </Box>
                            )}
                        </Paper>
                    </Grid>

                    {/* Sidebar
					<Grid xs={12} md={4}>
						<Paper
							sx={{
								p: 3,
								borderRadius: 4,
								position: 'sticky',
								top: 20,
								boxShadow: '0 4px 24px rgba(0,0,0,0.1)'
							}}
						>
							<Typography variant="h5" sx={{ mb: 3 }}>Về tác giả</Typography>
							<Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
								<Avatar
									src={blog.avatar_url}
									sx={{ width: 80, height: 80, mr: 2 }}
								/>
								<Box>
									<Typography variant="h6">{blog.author_name}</Typography>
									<Typography variant="body2" color="text.secondary">
										Coffee Enthusiast & Writer
									</Typography>
								</Box>
							</Box>
							<Typography variant="body2" sx={{ mb: 3 }}>
								Passionate about sharing knowledge and experiences in the coffee industry.
							</Typography>
						</Paper>
					</Grid> */}
                    {(blog.previous_blog || blog.next_blog) && (
                        <Box sx={{ display: "flex", justifyContent: "space-between", width: "100%" }}>
                            {blog.previous_blog ? (
                                <Tooltip title={blog.previous_blog.title}>
                                    <Button
                                        variant="outlined"
                                        sx={{
                                            borderRadius: "10px",
                                            maxWidth: "200px",
                                        }}
                                        startIcon={<NavigateBeforeIcon />}
                                    >
                                        <Typography
                                            noWrap
                                            sx={{
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                width: "100%",
                                                textTransform: "none",
                                            }}
                                        >
                                            {blog.previous_blog.title}
                                        </Typography>
                                    </Button>
                                </Tooltip>
                            ) : (
                                <div />
                            )}
                            {blog.next_blog ? (
                                <Tooltip title={blog.next_blog.title}>
                                    <Button
                                        variant="contained"
                                        sx={{
                                            borderRadius: "10px",
                                            maxWidth: "200px",
                                        }}
                                        endIcon={<NavigateNextIcon />}
                                    >
                                        <Typography
                                            noWrap
                                            sx={{
                                                overflow: "hidden",
                                                textOverflow: "ellipsis",
                                                width: "100%",
                                                textTransform: "none",
                                            }}
                                        >
                                            {blog.next_blog.title}
                                        </Typography>
                                    </Button>
                                </Tooltip>
                            ) : (
                                <div />
                            )}
                        </Box>
                    )}
                </Grid>

                {/* Related Posts */}
                {relatedBlogs.length > 0 && (
                    <Box sx={{ mt: 8, mb: 8 }}>
                        <Typography variant="h4" sx={{ mb: 4 }}>
                            Bài viết liên quan
                        </Typography>
                        <Grid container spacing={4}>
                            {loading ? (
                                <Loading />
                            ) : (
                                relatedBlogs.map((_blog, index) => (
                                    <Grid size={{ xs: 6, md: 4 }} key={index}>
                                        <BlogCard post={_blog} />
                                    </Grid>
                                ))
                            )}
                        </Grid>
                    </Box>
                )}
            </Container>
        </Box>
    );
}

export default BlogComponent;
