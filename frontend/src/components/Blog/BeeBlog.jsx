import React from "react";
import CustomerLayout from "../Common/CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import Loading from "../Common/Loading";
import {
    Box,
    Container,
    Typography,
    Chip,
    Paper,
    Stack,
    useTheme,
    alpha,
    styled,
    InputAdornment,
    TextField,
    Card
} from "@mui/material";
import CustomTextField from "../Common/CustomTextField";
import Grid from "@mui/material/Grid2";
import BlogCard from "./BlogCard";
import { motion } from "framer-motion";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";
import {
    Search,
    TrendingUp,
    Schedule,
    Visibility,
    Article,
    AutoStories,
    MenuBook
} from "@mui/icons-material";

// Styled Components
const HeroSection = styled(Box)(({ theme }) => ({
    position: 'relative',
    minHeight: '80vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `linear-gradient(135deg,
        ${alpha(theme.palette.primary.main, 0.1)} 0%,
        ${alpha(theme.palette.secondary.main, 0.08)} 25%,
        ${alpha(theme.palette.info.main, 0.06)} 50%,
        ${alpha(theme.palette.success.main, 0.08)} 75%,
        ${alpha(theme.palette.primary.main, 0.12)} 100%)`,
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
            radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.08)} 0%, transparent 40%),
            radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.06)} 0%, transparent 40%),
            radial-gradient(circle at 40% 60%, ${alpha(theme.palette.info.main, 0.04)} 0%, transparent 40%)
        `,
        pointerEvents: 'none'
    },
    '&::after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${theme.palette.primary.main.slice(1)}' fill-opacity='0.02'%3E%3Ccircle cx='30' cy='30' r='1.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        pointerEvents: 'none'
    }
}));

const SearchSection = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(4),
    marginBottom: theme.spacing(4),
    borderRadius: theme.spacing(4),
    background: `linear-gradient(145deg,
        ${theme.palette.background.paper} 0%,
        ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    boxShadow: `
        0 8px 32px ${alpha(theme.palette.common.black, 0.04)},
        0 2px 8px ${alpha(theme.palette.common.black, 0.02)}
    `,
    position: 'relative',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        // background: `linear-gradient(90deg,
        //     ${theme.palette.primary.main},
        //     ${theme.palette.secondary.main},
        //     ${theme.palette.info.main})`,
        borderRadius: `${theme.spacing(4)} ${theme.spacing(4)} 0 0`
    }
}));

const StatsCard = styled(Card)(({ theme }) => ({
    padding: theme.spacing(3),
    textAlign: 'center',
    borderRadius: theme.spacing(3),
    background: `linear-gradient(145deg,
        ${theme.palette.background.paper} 0%,
        ${alpha(theme.palette.background.paper, 0.8)} 100%)`,
    border: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
    backdropFilter: 'blur(10px)',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(135deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
        opacity: 0,
        transition: 'opacity 0.3s ease'
    },
    '&:hover': {
        transform: 'translateY(-8px) scale(1.02)',
        boxShadow: `
            0 20px 40px ${alpha(theme.palette.common.black, 0.1)},
            0 8px 16px ${alpha(theme.palette.common.black, 0.06)}
        `,
        '&::before': {
            opacity: 1
        }
    }
}));

function BeeBlog({ user, setUser, cartItem, setCartItem }) {
    const theme = useTheme();
    useDocumentTitle("Bài viết | BeE");

    // Animation variants

    const slideUp = {
        hidden: { y: 50, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
            transition: { duration: 0.6 },
        },
    };

    const staggerContainer = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const [selectedCategory, setSelectedCategory] = React.useState("");
    const [categories, setCategories] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [loadingFilter, setLoadingFilter] = React.useState(true);
    const [posts, setPosts] = React.useState([]);
    const [searchTerm, setSearchTerm] = React.useState("");
    const [filteredPosts, setFilteredPosts] = React.useState([]);

    // Filter posts based on search term
    React.useEffect(() => {
        if (searchTerm) {
            const filtered = posts.filter(post =>
                post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                post.content.toLowerCase().includes(searchTerm.toLowerCase())
            );
            setFilteredPosts(filtered);
        } else {
            setFilteredPosts(posts);
        }
    }, [posts, searchTerm]);

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const responseCategories = await axiosInstance.get("/api/blog/categories");
                setCategories(responseCategories.data);
                setLoading(false);
            } catch (error) {
                console.error("Lỗi:", error);
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    React.useEffect(() => {
        const fetchPosts = async () => {
            setLoadingFilter(true);
            try {
                const response = await axiosInstance.get(
                    selectedCategory
                        ? `/api/shopping/home/<USER>/category/${selectedCategory}`
                        : "/api/shopping/home/<USER>"
                );
                setPosts(response.data);
            } catch (error) {
                console.error("Lỗi:", error);
            } finally {
                setLoadingFilter(false);
            }
        };
        fetchPosts();
    }, [selectedCategory]);

    return (
        <CustomerLayout cartItem={cartItem} user={user} setUser={setUser} setCartItem={setCartItem} maxWidth={false}>
            {loading ? (
                <Box
                    sx={{
                        minHeight: "70vh",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <Loading />
                </Box>
            ) : (
                <>
                    {/* Hero Section */}
                    <HeroSection>
                        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, py: 8 }}>
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                style={{ textAlign: 'center' }}
                            >
                                {/* Main Title */}
                                <Box sx={{ mb: 6 }}>
                                    <Typography
                                        variant="h1"
                                        sx={{
                                            fontSize: { xs: '2.8rem', md: '4.5rem', lg: '5rem' },
                                            fontWeight: 900,
                                            background: `linear-gradient(135deg,
                                                ${theme.palette.primary.main} 0%,
                                                ${theme.palette.secondary.main} 50%,
                                                ${theme.palette.info.main} 100%)`,
                                            backgroundClip: 'text',
                                            WebkitBackgroundClip: 'text',
                                            WebkitTextFillColor: 'transparent',
                                            mb: 3,
                                            letterSpacing: '-0.02em',
                                            lineHeight: 0.9
                                        }}
                                    >
                                        BeE Blog
                                    </Typography>
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            color: 'text.primary',
                                            mb: 2,
                                            fontWeight: 400,
                                            maxWidth: '700px',
                                            mx: 'auto',
                                            lineHeight: 1.4,
                                            fontSize: { xs: '1.2rem', md: '1.5rem' }
                                        }}
                                    >
                                        📚 Khám phá kiến thức STEM
                                    </Typography>
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            color: 'text.secondary',
                                            maxWidth: '600px',
                                            mx: 'auto',
                                            lineHeight: 1.6,
                                            fontSize: { xs: '1rem', md: '1.1rem' }
                                        }}
                                    >
                                        Chia sẻ kiến thức, kinh nghiệm và xu hướng công nghệ mới nhất
                                    </Typography>
                                </Box>

                                {/* Hero Stats */}
                                <Grid container spacing={4} justifyContent="center" sx={{ mt: 6 }}>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.2, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`
                                                    }}
                                                >
                                                    <Article sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'primary.main', mb: 0.5 }}>
                                                    {posts.length}+
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Bài viết
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.3, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.success.main, 0.3)}`
                                                    }}
                                                >
                                                    <AutoStories sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'success.main', mb: 0.5 }}>
                                                    {categories.length}+
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Chủ đề
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.4, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.info.main, 0.3)}`
                                                    }}
                                                >
                                                    <TrendingUp sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'info.main', mb: 0.5 }}>
                                                    24/7
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Cập nhật
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.5, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.warning.main, 0.3)}`
                                                    }}
                                                >
                                                    <MenuBook sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'warning.main', mb: 0.5 }}>
                                                    100%
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Miễn phí
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                </Grid>
                            </motion.div>
                        </Container>
                    </HeroSection>

                    <Container maxWidth="xl" sx={{ py: 3 }}>
                        <motion.div initial="hidden" animate="visible" variants={staggerContainer}>
                            {/* Search and Filter Section */}
                            <SearchSection elevation={0}>
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6 }}
                                >
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            fontWeight: 700,
                                            mb: 3,
                                            textAlign: 'center',
                                            background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                                            backgroundClip: 'text',
                                            WebkitBackgroundClip: 'text',
                                            WebkitTextFillColor: 'transparent',
                                        }}
                                    >
                                        🔍 Tìm kiếm bài viết
                                    </Typography>

                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: { xs: "column", md: "row" },
                                            gap: 3,
                                            alignItems: { xs: "stretch", md: "center" },
                                            justifyContent: "center",
                                            mb: 4
                                        }}
                                    >
                                        {/* Search Bar */}
                                        <Box sx={{ flex: 1, maxWidth: { md: "500px" } }}>
                                            <CustomTextField
                                                fullWidth
                                                placeholder="Nhập từ khóa tìm kiếm..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                slotProps={{
                                                    input: {
                                                        startAdornment: (
                                                            <InputAdornment position="start">
                                                                <Search color="primary" />
                                                            </InputAdornment>
                                                        ),
                                                    },
                                                }}
                                            // sx={{
                                            //     '& .MuiOutlinedInput-root': {
                                            //         borderRadius: 4,
                                            //         backgroundColor: 'background.paper',
                                            //         fontSize: '1.1rem',
                                            //         padding: '4px 0',
                                            //         border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                                            //         transition: 'all 0.3s ease',
                                            //         '&:hover': {
                                            //             borderColor: alpha(theme.palette.primary.main, 0.3),
                                            //             boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.15)}`
                                            //         },
                                            //         '&.Mui-focused': {
                                            //             borderColor: theme.palette.primary.main,
                                            //             boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.25)}`
                                            //         }
                                            //     }
                                            // }}
                                            />
                                        </Box>
                                    </Box>

                                    {/* Categories Section */}
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            fontWeight: 600,
                                            mb: 2,
                                            color: 'text.primary'
                                        }}
                                    >
                                        📂 Danh mục bài viết
                                    </Typography>
                                    <Stack direction="row" spacing={2} sx={{ mb: 2, flexWrap: "wrap", gap: 2 }}>
                                        <Chip
                                            sx={{
                                                height: "44px",
                                                fontWeight: 600,
                                                fontSize: '1rem',
                                                borderRadius: 3,
                                                transition: 'all 0.3s ease',
                                                "&:hover": {
                                                    backgroundColor: "primary.main",
                                                    color: "white",
                                                    transform: 'translateY(-2px)',
                                                    boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`
                                                },
                                            }}
                                            variant={selectedCategory === "" ? "filled" : "outlined"}
                                            label="📚 Tất cả"
                                            color={selectedCategory === "" ? "primary" : "default"}
                                            onClick={() => setSelectedCategory("")}
                                        />
                                        {categories.map((category) => (
                                            <motion.div key={category.id} variants={slideUp}>
                                                <Chip
                                                    variant={category.slug === selectedCategory ? "filled" : "outlined"}
                                                    label={`📖 ${category.name}`}
                                                    color={category.slug === selectedCategory ? "primary" : "default"}
                                                    onClick={() => setSelectedCategory(category.slug)}
                                                    sx={{
                                                        height: "44px",
                                                        fontWeight: 600,
                                                        fontSize: '1rem',
                                                        borderRadius: 3,
                                                        transition: 'all 0.3s ease',
                                                        "&:hover": {
                                                            backgroundColor: "primary.main",
                                                            color: "white",
                                                            transform: 'translateY(-2px)',
                                                            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.3)}`
                                                        },
                                                    }}
                                                />
                                            </motion.div>
                                        ))}
                                    </Stack>
                                </motion.div>
                            </SearchSection>

                            {/* Blog Posts Section */}
                            <Box sx={{ mb: 6 }}>
                                {/* Results Header */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.2 }}
                                >
                                    <Paper
                                        sx={{
                                            p: 3,
                                            mb: 4,
                                            borderRadius: 3,
                                            background: `linear-gradient(135deg,
                                                ${alpha(theme.palette.background.paper, 0.9)} 0%,
                                                ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
                                            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.02)}`
                                        }}
                                    >
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                                            <Box>
                                                <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                                                    {filteredPosts.length > 0 ? (
                                                        <>📖 Tìm thấy <span style={{ color: theme.palette.primary.main }}>{filteredPosts.length}</span> bài viết</>
                                                    ) : (
                                                        '❌ Không tìm thấy bài viết nào'
                                                    )}
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary">
                                                    {selectedCategory
                                                        ? `Danh mục: ${categories.find((cat) => cat.slug === selectedCategory)?.name || 'Tất cả'}`
                                                        : searchTerm
                                                            ? `Kết quả cho "${searchTerm}"`
                                                            : 'Tất cả bài viết mới nhất'
                                                    }
                                                </Typography>
                                            </Box>

                                            {/* Quick Actions */}
                                            <Box sx={{ display: 'flex', gap: 1.5, flexWrap: 'wrap' }}>
                                                <Chip
                                                    icon={<TrendingUp />}
                                                    label="🔥 Xu hướng"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.error.main, 0.1),
                                                            borderColor: theme.palette.error.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                                <Chip
                                                    icon={<Schedule />}
                                                    label="⏰ Mới nhất"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.info.main, 0.1),
                                                            borderColor: theme.palette.info.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                                <Chip
                                                    icon={<Visibility />}
                                                    label="👁️ Phổ biến"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.success.main, 0.1),
                                                            borderColor: theme.palette.success.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                            </Box>
                                        </Box>
                                    </Paper>
                                </motion.div>

                                {/* Blog Posts Grid */}
                                {loadingFilter ? (
                                    <Box
                                        sx={{
                                            minHeight: "40vh",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                    >
                                        <Loading />
                                    </Box>
                                ) : filteredPosts.length === 0 ? (
                                    <Box
                                        sx={{
                                            textAlign: 'center',
                                            py: 8,
                                            color: 'text.secondary'
                                        }}
                                    >
                                        <Article sx={{ fontSize: 80, mb: 2, opacity: 0.3 }} />
                                        <Typography variant="h6" sx={{ mb: 1 }}>
                                            Không tìm thấy bài viết
                                        </Typography>
                                        <Typography variant="body2">
                                            Thử thay đổi từ khóa tìm kiếm hoặc danh mục
                                        </Typography>
                                    </Box>
                                ) : (
                                    <motion.div initial="hidden" animate="visible" variants={staggerContainer}>
                                        <Grid container spacing={4} sx={{ mb: 8 }}>
                                            {filteredPosts.map((post, index) => (
                                                <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                                                    <motion.div
                                                        variants={slideUp}
                                                        whileHover={{
                                                            y: -8,
                                                            transition: {
                                                                duration: 0.3,
                                                                type: "spring",
                                                                stiffness: 300
                                                            },
                                                        }}
                                                    >
                                                        <BlogCard post={post} />
                                                    </motion.div>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </motion.div>
                                )}
                            </Box>
                        </motion.div>
                    </Container>
                </>
            )}
        </CustomerLayout>
    );
}

export default BeeBlog;
