import {
    Avatar,
    Box,
    Button,
    Card,
    CardActions,
    CardContent,
    CardHeader,
    CardMedia,
    IconButton,
    Portal,
    Typography,
} from "@mui/material";
import React from "react";
import FavoriteIcon from "@mui/icons-material/Favorite";
import ShareIcon from "@mui/icons-material/Share";
import { red } from "@mui/material/colors";
import MoreVertIcon from "@mui/icons-material/MoreVert";
import { useNavigate } from "react-router-dom";

function BlogCard({ post }) {
    const navigate = useNavigate();

    function getDate(date) {
        const dateObject = new Date(date);
        return dateObject.toISOString().split("T")[0];
    }

    function handleViewPost() {
        navigate(`/blog/${post.slug}`);
    }

    return (
        <Card
            sx={{
                width: "100%",
                borderRadius: "20px",
                cursor: "pointer",
                transition: "transform 0.2s ease-in-out, background-color 0.2s ease-in-out",
                "&:hover": {
                    transform: "scale(1.05)",
                },
                height: "100%"
            }}
            onClick={handleViewPost}
        >
            <Box
                sx={{
                    height: "100%",
                }}
            >
                {/* <CardHeader
                    avatar={
                        post.avatar_url === null ? (
                            <Avatar sx={{ bgcolor: red[500] }} aria-label="recipe">
                                {post.author[0]}
                            </Avatar>
                        ) : (
                            <Avatar
                                sx={{ mr: "10px" }}
                                alt={post.author}
                                src={post.avatar_url}
                            />
                        )
                    }
                    title={post.author}
                    subheader={getDate(post.created_at)}
                /> */}
                <CardMedia
                    component="img"
                    height="194"
                    image={post.cover_image_url}
                    alt={post.title}
                    sx={{
                        objectFit: "contain",
                    }}
                />
                <CardContent>
                    <Typography variant="h6">{post.title}</Typography>
                </CardContent>
                {/* <CardActions disableSpacing>
                <Button>Xem thêm</Button>
            </CardActions> */}
            </Box>
        </Card>
    );
}

export default BlogCard;
