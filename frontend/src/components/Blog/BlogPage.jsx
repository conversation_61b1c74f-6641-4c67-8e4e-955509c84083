import React from "react";
import CustomerLayout from "../Common/CustomerLayout";
import { Box, Chip, Divider, Paper, Typography } from "@mui/material";
import { useParams } from "react-router-dom";
import Loading from "../Common/Loading";
import axiosInstance from "../../services/axiosInstance";
import BlogComponent from "./BlogComponent";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

function BlogPage({ user, setUser, cartItem, setCartItem }) {
    const { blog_slug } = useParams();

    const [blog, setBlog] = React.useState({});
    const [loading, setLoading] = React.useState(true);

    useDocumentTitle(blog?.title || "Blog | BeE");

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const responseBlog = await axiosInstance.get(`/api/shopping/blog/${blog_slug}`);
                setBlog(responseBlog.data);
            } catch (error) {
                console.error("Lỗi:", error);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    return (
        <CustomerLayout cartItem={cartItem} user={user} setUser={setUser} setCartItem={setCartItem} maxWidth={"900px"}>
            {loading ? (
                <Loading />
            ) : (
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <BlogComponent blog={blog} maxWidth="900px" viewOnly={false} />
                </Box>
            )}
        </CustomerLayout>
    );
}

export default BlogPage;
