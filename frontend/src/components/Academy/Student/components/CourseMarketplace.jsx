import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
	Box,
	Card,
	CardContent,
	CardMedia,
	Typography,
	Button,
	Chip,
	Avatar,
	IconButton,
	TextField,
	FormControl,
	InputLabel,
	Select,
	MenuItem,
	Alert,
	CircularProgress,
	Rating,
	Container,
	Paper,
	Divider,
	Badge,
	Stack,
	Snackbar
} from '@mui/material';
import Grid from '@mui/material/Grid2';
import {
	School as SchoolIcon,
	ShoppingCart as CartIcon,
	Star as StarIcon,
	AccessTime as TimeIcon,
	People as PeopleIcon,
	TrendingUp as TrendingUpIcon,
	Search as SearchIcon,
	FilterList as FilterIcon,
	PlayArrow as PlayIcon,
	LocalOffer as OfferIcon,
	Verified as VerifiedIcon,
	FavoriteOutlined as FavoriteIcon,
	Share as ShareIcon
} from '@mui/icons-material';
import StyleIcon from '@mui/icons-material/Style';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import { elearningAPI } from '../../../../services';

const CourseMarketplace = ({ user }) => {
	const navigate = useNavigate();
	const [courses, setCourses] = useState([]);
	const [featuredCourses, setFeaturedCourses] = useState([]);
	const [categories, setCategories] = useState([]);
	const [subjects, setSubjects] = useState([]);
	const [grades, setGrades] = useState([]);
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState(null);
	const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
	const [filters, setFilters] = useState({
		subject: '',
		grade: '',
		difficulty: '',
		price_range: '',
		search: '',
		category: ''
	});

	useEffect(() => {
		loadInitialData();
	}, []);

	useEffect(() => {
		loadCourses();
	}, [filters]);

	const loadInitialData = async () => {
		try {
			await Promise.all([
				loadFeaturedCourses(),
				loadCategories(),
				loadSubjects(),
				loadGrades(),
				loadCourses()
			]);
		} catch (err) {
			setError('Không thể tải dữ liệu');
			console.error('Initial data error:', err);
		} finally {
			setLoading(false);
		}
	};

	const loadFeaturedCourses = async () => {
		try {
			const data = await elearningAPI.studentAPI.getFeaturedCourses();
			setFeaturedCourses(data.slice(0, 6)); // Lấy 6 khóa học nổi bật
		} catch (err) {
			console.error('Featured courses error:', err);
		}
	};

	const loadCourses = async () => {
		try {
			const data = await elearningAPI.studentAPI.getAvailableCourses(filters);
			// Handle both array format (mock) and paginated format (backend)
			if (Array.isArray(data)) {
				setCourses(data);
			} else if (data.results) {
				setCourses(data.results);
			} else {
				setCourses([]);
			}
		} catch (err) {
			setError('Không thể tải khóa học');
			console.error('Courses error:', err);
		}
	};

	const loadCategories = async () => {
		try {
			const data = await elearningAPI.studentAPI.getCategories();
			setCategories(data);
		} catch (err) {
			console.error('Categories error:', err);
		}
	};

	const loadSubjects = async () => {
		try {
			const data = await elearningAPI.getSubjects();
			setSubjects(data);
		} catch (err) {
			console.error('Subjects error:', err);
		}
	};

	const loadGrades = async () => {
		try {
			const data = await elearningAPI.getGrades();
			setGrades(data);
		} catch (err) {
			console.error('Grades error:', err);
		}
	};

	const handlePurchaseCourse = async (courseId, course) => {
		try {
			setLoading(true);

			// Call API to purchase/enroll course
			const result = await elearningAPI.studentAPI.purchaseCourse(courseId, {
				payment_method: parseInt(course.price) === 0 ? 'free' : 'credit_card'
			});

			// Show success message
			setError(null);

			// Navigate to learning page
			setTimeout(() => {
				navigate(`/e-learning/course/${courseId}/learn`);
			}, 1500);

			// Show success notification
			const message = parseInt(course.price) === 0
				? 'Đăng ký khóa học miễn phí thành công! Đang chuyển đến trang học...'
				: 'Mua khóa học thành công! Đang chuyển đến trang học...';

			setSnackbar({
				open: true,
				message: message,
				severity: 'success'
			});

		} catch (err) {
			const errorMessage = parseInt(course.price) === 0
				? 'Không thể đăng ký khóa học miễn phí. Vui lòng thử lại!'
				: 'Không thể mua khóa học. Vui lòng kiểm tra thông tin thanh toán!';

			setSnackbar({
				open: true,
				message: errorMessage,
				severity: 'error'
			});

			console.error('Purchase course error:', err);
		} finally {
			setLoading(false);
		}
	};

	const formatPrice = (price) => {
		if (price === 0) return 'Miễn phí';
		return new Intl.NumberFormat('vi-VN', {
			style: 'currency',
			currency: 'VND'
		}).format(price);
	};

	const getDifficultyColor = (difficulty) => {
		const colors = {
			'beginner': 'success',
			'intermediate': 'warning',
			'advanced': 'error',
			'expert': 'secondary'
		};
		return colors[difficulty] || 'default';
	};

	const getDifficultyLabel = (difficulty) => {
		const labels = {
			'beginner': 'Cơ bản',
			'intermediate': 'Trung bình',
			'advanced': 'Nâng cao',
			'expert': 'Chuyên sâu'
		};
		return labels[difficulty] || difficulty;
	};

	if (loading) {
		return (
			<Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
				<CircularProgress size={60} />
			</Box>
		);
	}

	return (
		<Box sx={{ bgcolor: '#f8fafc', minHeight: '100vh' }}>
			{error && (
				<Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
					{error}
				</Alert>
			)}

			{/* BeE STEM Hero Section */}
			<Box
				sx={{
					background: 'linear-gradient(135deg, #1a237e 0%, #3949ab 50%, #5c6bc0 100%)',
					color: 'white',
					py: 10,
					mb: 6,
					position: 'relative',
					overflow: 'hidden'
				}}
			>
				{/* Background Pattern */}
				<Box
					sx={{
						position: 'absolute',
						top: 0,
						left: 0,
						right: 0,
						bottom: 0,
						backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
						opacity: 0.3
					}}
				/>

				<Container maxWidth="lg" sx={{ position: 'relative', zIndex: 1 }}>
					<Grid container spacing={4} alignItems="center">
						<Grid size={{ xs: 12, md: 7 }}>
							<Box sx={{ mb: 3 }}>
								<Chip
									label="🚀 Nền tảng STEM hàng đầu Việt Nam"
									sx={{
										bgcolor: 'rgba(255,193,7,0.2)',
										color: '#ffc107',
										fontWeight: 'bold',
										mb: 3
									}}
								/>
							</Box>
							<Typography variant="h1" fontWeight="bold" gutterBottom sx={{
								fontSize: { xs: '2.5rem', md: '3.5rem' },
								background: 'linear-gradient(45deg, #ffffff, #ffc107)',
								backgroundClip: 'text',
								WebkitBackgroundClip: 'text',
								WebkitTextFillColor: 'transparent',
								mb: 3
							}}>
								Khám phá thế giới STEM
							</Typography>
							<Typography variant="h5" sx={{ mb: 4, opacity: 0.9, fontSize: { xs: '1.2rem', md: '1.4rem' }, lineHeight: 1.6 }}>
								Học Khoa học, Công nghệ, Kỹ thuật và Toán học từ các chuyên gia hàng đầu.
								Xây dựng tương lai với kiến thức STEM vững chắc.
							</Typography>
							<Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
								<Button
									variant="contained"
									size="large"
									sx={{
										bgcolor: '#ffc107',
										color: '#1a237e',
										py: 1.5,
										px: 4,
										fontSize: '1.1rem',
										fontWeight: 'bold',
										borderRadius: 3,
										'&:hover': { bgcolor: '#ffb300', transform: 'translateY(-2px)' },
										transition: 'all 0.3s ease'
									}}
									startIcon={<PlayArrowIcon />}
								>
									Bắt đầu hành trình STEM
								</Button>
								<Button
									variant="outlined"
									size="large"
									sx={{
										borderColor: 'white',
										color: 'white',
										py: 1.5,
										px: 4,
										fontSize: '1.1rem',
										borderRadius: 3,
										'&:hover': {
											borderColor: '#ffc107',
											bgcolor: 'rgba(255,193,7,0.1)',
											transform: 'translateY(-2px)'
										},
										transition: 'all 0.3s ease'
									}}
									startIcon={<StyleIcon />}
								>
									Tìm hiểu thêm
								</Button>
							</Stack>
						</Grid>
						<Grid size={{ xs: 12, md: 5 }}>
							<Box sx={{ textAlign: 'center', position: 'relative' }}>
								{/* STEM Icons Animation */}
								<Box sx={{
									display: 'grid',
									gridTemplateColumns: 'repeat(3, 1fr)',
									gap: 3,
									p: 4
								}}>
									{['🔬', '⚙️', '🔧', '📊', '💻', '🤖'].map((icon, index) => (
										<Box
											key={index}
											sx={{
												fontSize: '3rem',
												animation: `float ${2 + index * 0.5}s ease-in-out infinite`,
												animationDelay: `${index * 0.2}s`,
												'@keyframes float': {
													'0%, 100%': { transform: 'translateY(0px)' },
													'50%': { transform: 'translateY(-10px)' }
												}
											}}
										>
											{icon}
										</Box>
									))}
								</Box>
							</Box>
						</Grid>
					</Grid>
				</Container>
			</Box>

			{/* STEM Categories */}
			<Container maxWidth="lg" sx={{ mb: 8 }}>
				<Box sx={{ textAlign: 'center', mb: 6 }}>
					<Typography variant="h3" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
						Khám phá các lĩnh vực STEM
					</Typography>
					<Typography variant="h6" color="textSecondary" sx={{ mb: 4 }}>
						Chọn lĩnh vực bạn quan tâm để bắt đầu hành trình học tập
					</Typography>
				</Box>

				<Grid container spacing={3} sx={{ mb: 6 }}>
					{[
						{ name: '🔬 Khoa học', color: '#1a237e', desc: 'Vật lý, Hóa học, Sinh học' },
						{ name: '⚙️ Công nghệ', color: '#2e7d32', desc: 'AI, Machine Learning, IoT' },
						{ name: '🔧 Kỹ thuật', color: '#ed6c02', desc: 'Robotics, Cơ khí, Điện tử' },
						{ name: '📊 Toán học', color: '#9c27b0', desc: 'Đại số, Hình học, Thống kê' },
						{ name: '💻 Lập trình', color: '#d32f2f', desc: 'Python, Java, Web Development' },
						{ name: '🤖 AI & Robotics', color: '#1976d2', desc: 'Machine Learning, Deep Learning' }
					].map((category, index) => (
						<Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
							<Card
								sx={{
									p: 3,
									textAlign: 'center',
									cursor: 'pointer',
									border: '2px solid transparent',
									transition: 'all 0.3s ease',
									'&:hover': {
										borderColor: category.color,
										transform: 'translateY(-4px)',
										boxShadow: `0 8px 25px ${category.color}20`
									}
								}}
							>
								<Typography variant="h5" fontWeight="bold" sx={{ color: category.color, mb: 1 }}>
									{category.name}
								</Typography>
								<Typography variant="body2" color="textSecondary">
									{category.desc}
								</Typography>
							</Card>
						</Grid>
					))}
				</Grid>
			</Container>

			{/* STEM Stats Section */}
			<Box sx={{ bgcolor: 'white', py: 8, mb: 8 }}>
				<Container maxWidth="lg">
					<Box sx={{ textAlign: 'center', mb: 6 }}>
						<Typography variant="h3" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
							Cộng đồng STEM BeE Learning
						</Typography>
						<Typography variant="h6" color="textSecondary">
							Tham gia cùng hàng nghìn học viên đam mê STEM
						</Typography>
					</Box>

					<Grid container spacing={4}>
						<Grid size={{ xs: 12, sm: 6, md: 3 }}>
							<Box sx={{
								textAlign: 'center',
								p: 4,
								borderRadius: 4,
								background: 'linear-gradient(135deg, #1a237e, #3949ab)',
								color: 'white',
								transition: 'transform 0.3s ease',
								'&:hover': { transform: 'translateY(-8px)' }
							}}>
								<SchoolIcon sx={{ fontSize: 60, mb: 2 }} />
								<Typography variant="h3" fontWeight="bold" gutterBottom>
									500+
								</Typography>
								<Typography variant="h6" sx={{ opacity: 0.9 }}>
									Khóa học STEM
								</Typography>
							</Box>
						</Grid>
						<Grid size={{ xs: 12, sm: 6, md: 3 }}>
							<Box sx={{
								textAlign: 'center',
								p: 4,
								borderRadius: 4,
								background: 'linear-gradient(135deg, #2e7d32, #4caf50)',
								color: 'white',
								transition: 'transform 0.3s ease',
								'&:hover': { transform: 'translateY(-8px)' }
							}}>
								<PeopleIcon sx={{ fontSize: 60, mb: 2 }} />
								<Typography variant="h3" fontWeight="bold" gutterBottom>
									25K+
								</Typography>
								<Typography variant="h6" sx={{ opacity: 0.9 }}>
									Học viên tích cực
								</Typography>
							</Box>
						</Grid>
						<Grid size={{ xs: 12, sm: 6, md: 3 }}>
							<Box sx={{
								textAlign: 'center',
								p: 4,
								borderRadius: 4,
								background: 'linear-gradient(135deg, #ed6c02, #ff9800)',
								color: 'white',
								transition: 'transform 0.3s ease',
								'&:hover': { transform: 'translateY(-8px)' }
							}}>
								<VerifiedIcon sx={{ fontSize: 60, mb: 2 }} />
								<Typography variant="h3" fontWeight="bold" gutterBottom>
									200+
								</Typography>
								<Typography variant="h6" sx={{ opacity: 0.9 }}>
									Chuyên gia STEM
								</Typography>
							</Box>
						</Grid>
						<Grid size={{ xs: 12, sm: 6, md: 3 }}>
							<Box sx={{
								textAlign: 'center',
								p: 4,
								borderRadius: 4,
								background: 'linear-gradient(135deg, #9c27b0, #e91e63)',
								color: 'white',
								transition: 'transform 0.3s ease',
								'&:hover': { transform: 'translateY(-8px)' }
							}}>
								<StarIcon sx={{ fontSize: 60, mb: 2 }} />
								<Typography variant="h3" fontWeight="bold" gutterBottom>
									4.9
								</Typography>
								<Typography variant="h6" sx={{ opacity: 0.9 }}>
									Đánh giá trung bình
								</Typography>
							</Box>
						</Grid>
					</Grid>
				</Container>
			</Box>

			<Container maxWidth="lg">
				{/* Featured STEM Courses Section */}
				{featuredCourses.length > 0 && (
					<Box sx={{ mb: 8 }}>
						<Box sx={{ textAlign: 'center', mb: 6 }}>
							<Typography variant="h3" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
								🌟 Khóa học STEM nổi bật
							</Typography>
							<Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
								Những khóa học được học viên yêu thích nhất
							</Typography>
							<Box sx={{
								width: 80,
								height: 4,
								bgcolor: '#ffc107',
								mx: 'auto',
								borderRadius: 2
							}} />
						</Box>
						<Grid container spacing={3}>
							{featuredCourses.map((course) => (
								<Grid size={{ xs: 12, sm: 6, md: 4 }} key={course.id}>
									<Card
										sx={{
											height: '100%',
											border: '1px solid #e0e0e0',
											borderRadius: 1,
											transition: 'transform 0.2s, box-shadow 0.2s',
											cursor: 'pointer',
											'&:hover': {
												transform: 'translateY(-2px)',
												boxShadow: '0 4px 16px rgba(0,0,0,0.15)'
											}
										}}
										onClick={() => navigate(`/e-learning/course/${course.id}`)}
									>
										<Box sx={{ position: 'relative' }}>
											{course.thumbnail && (
												<CardMedia
													component="img"
													height="200"
													image={course.thumbnail}
													alt={course.title}
												/>
											)}
											{course.discount_percentage > 0 && (
												<Chip
													label={`-${course.discount_percentage}%`}
													color="error"
													sx={{
														position: 'absolute',
														top: 12,
														left: 12,
														fontWeight: 'bold'
													}}
												/>
											)}
											<IconButton
												sx={{
													position: 'absolute',
													top: 12,
													right: 12,
													bgcolor: 'rgba(255,255,255,0.9)',
													'&:hover': { bgcolor: 'white' }
												}}
											>
												<FavoriteIcon />
											</IconButton>
										</Box>
										<CardContent sx={{ p: 2 }}>
											<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ fontSize: '1rem', lineHeight: 1.4 }}>
												{course.title}
											</Typography>
											<Typography variant="body2" color="textSecondary" sx={{ mb: 1, fontSize: '0.875rem' }}>
												{course.instructor?.name || 'BeE Learning'}
											</Typography>

											<Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
												<Typography variant="body2" fontWeight="bold" sx={{ mr: 1 }}>
													{course.average_rating || 4.5}
												</Typography>
												<Rating value={course.average_rating || 4.5} readOnly size="small" />
												<Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
													({course.total_reviews || 0})
												</Typography>
											</Box>

											<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
												<Typography variant="h6" fontWeight="bold" sx={{ color: '#1c1d1f' }}>
													{formatPrice(course.price || 0)}
												</Typography>
												{course.discount_percentage > 0 && (
													<Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
														{formatPrice(course.original_price || course.price * 1.5)}
													</Typography>
												)}
											</Box>

											<Button
												fullWidth
												variant="contained"
												size="small"
												startIcon={parseInt(course.price) === 0 ? <PlayIcon /> : <CartIcon />}
												onClick={(e) => {
													e.stopPropagation(); // Prevent card click
													handlePurchaseCourse(course.id, course);
												}}
												sx={{
													borderRadius: 3,
													background: parseInt(course.price) === 0
														? 'linear-gradient(45deg, #2e7d32, #4caf50)'
														: 'linear-gradient(45deg, #1a237e, #3949ab)',
													color: 'white',
													fontWeight: 'bold',
													py: 1.2,
													'&:hover': {
														transform: 'translateY(-2px)',
														boxShadow: '0 6px 20px rgba(26, 35, 126, 0.3)'
													},
													transition: 'all 0.3s ease'
												}}
											>
												{parseInt(course.price) === 0 ? 'Học miễn phí' : 'Đăng ký ngay'}
											</Button>
										</CardContent>
									</Card>
								</Grid>
							))}
						</Grid>
					</Box>
				)}



				{/* All STEM Courses */}
				<Box sx={{ mb: 4 }}>
					<Box sx={{ textAlign: 'center', mb: 6 }}>
						<Typography variant="h3" fontWeight="bold" gutterBottom sx={{ color: '#1a237e' }}>
							Thư viện khóa học STEM
						</Typography>
						<Typography variant="h6" color="textSecondary" sx={{ mb: 2 }}>
							Khám phá hàng trăm khóa học chất lượng cao
						</Typography>
						<Box sx={{
							width: 80,
							height: 4,
							bgcolor: '#ffc107',
							mx: 'auto',
							borderRadius: 2
						}} />
					</Box>
					<Grid container spacing={3}>
						{courses.map((course) => (
							<Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={course.id}>
								<Card
									sx={{
										height: '100%',
										borderRadius: 3,
										border: '1px solid rgba(26, 35, 126, 0.1)',
										transition: 'all 0.3s ease',
										cursor: 'pointer',
										'&:hover': {
											transform: 'translateY(-8px)',
											boxShadow: '0 12px 40px rgba(26, 35, 126, 0.15)',
											borderColor: '#1a237e'
										}
									}}
									onClick={() => navigate(`/e-learning/course/${course.id}`)}
								>
									<Box sx={{ position: 'relative' }}>
										{course.thumbnail && (
											<CardMedia
												component="img"
												height="160"
												image={course.thumbnail}
												alt={course.title}
											/>
										)}
										{course.discount_percentage > 0 && (
											<Chip
												label={`-${course.discount_percentage}%`}
												color="error"
												size="small"
												sx={{
													position: 'absolute',
													top: 8,
													left: 8,
													fontWeight: 'bold'
												}}
											/>
										)}
									</Box>
									<CardContent sx={{ p: 2 }}>
										<Typography variant="h6" fontWeight="bold" gutterBottom sx={{ fontSize: '1rem', lineHeight: 1.4, height: 48, overflow: 'hidden' }}>
											{course.title}
										</Typography>
										<Typography variant="body2" color="textSecondary" sx={{ mb: 1, fontSize: '0.875rem' }}>
											{course.instructor?.name || 'BeE Learning'}
										</Typography>

										<Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
											<Typography variant="body2" fontWeight="bold" sx={{ mr: 1 }}>
												{course.average_rating || 4.5}
											</Typography>
											<Rating value={course.average_rating || 4.5} readOnly size="small" />
											<Typography variant="body2" color="textSecondary" sx={{ ml: 1 }}>
												({course.total_reviews || 0})
											</Typography>
										</Box>

										<Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
											<Typography variant="h6" fontWeight="bold" sx={{ color: '#1c1d1f' }}>
												{formatPrice(course.price || 0)}
											</Typography>
											{course.discount_percentage > 0 && (
												<Typography variant="body2" sx={{ textDecoration: 'line-through', color: 'text.secondary' }}>
													{formatPrice(course.original_price || course.price * 1.5)}
												</Typography>
											)}
										</Box>

										<Button
											fullWidth
											variant="contained"
											size="small"
											startIcon={parseInt(course.price) === 0 ? <PlayIcon /> : <CartIcon />}
											onClick={(e) => {
												e.stopPropagation(); // Prevent card click
												handlePurchaseCourse(course.id, course);
											}}
											sx={{
												borderRadius: 3,
												background: parseInt(course.price) === 0
													? 'linear-gradient(45deg, #2e7d32, #4caf50)'
													: 'linear-gradient(45deg, #1a237e, #3949ab)',
												color: 'white',
												fontWeight: 'bold',
												py: 1.2,
												'&:hover': {
													transform: 'translateY(-2px)',
													boxShadow: '0 6px 20px rgba(26, 35, 126, 0.3)'
												},
												transition: 'all 0.3s ease'
											}}
										>
											{parseInt(course.price) === 0 ? 'Học miễn phí' : 'Đăng ký ngay'}
										</Button>
									</CardContent>
								</Card>
							</Grid>
						))}
					</Grid>
				</Box>
			</Container>

			{/* STEM Learning CTA Section */}
			<Box sx={{
				background: 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)',
				color: 'white',
				py: 8,
				mt: 8
			}}>
				<Container maxWidth="lg">
					<Box sx={{ textAlign: 'center' }}>
						<Typography variant="h3" fontWeight="bold" gutterBottom>
							🚀 Sẵn sàng bắt đầu hành trình STEM?
						</Typography>
						<Typography variant="h6" sx={{ mb: 4, opacity: 0.9 }}>
							Tham gia cùng hàng nghìn học viên đang xây dựng tương lai với kiến thức STEM
						</Typography>
						<Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} justifyContent="center">
							<Button
								variant="contained"
								size="large"
								onClick={() => navigate('/e-learning/dashboard')}
								sx={{
									bgcolor: '#ffc107',
									color: '#1a237e',
									py: 1.5,
									px: 4,
									fontSize: '1.1rem',
									fontWeight: 'bold',
									borderRadius: 3,
									'&:hover': { bgcolor: '#ffb300' }
								}}
							>
								Xem khóa học của tôi
							</Button>
						</Stack>
					</Box>
				</Container>
			</Box>

			{/* Snackbar for notifications */}
			<Snackbar
				open={snackbar.open}
				autoHideDuration={6000}
				onClose={() => setSnackbar({ ...snackbar, open: false })}
			>
				<Alert
					onClose={() => setSnackbar({ ...snackbar, open: false })}
					severity={snackbar.severity}
					sx={{ width: '100%' }}
				>
					{snackbar.message}
				</Alert>
			</Snackbar>
		</Box>
	);
};

export default CourseMarketplace;
