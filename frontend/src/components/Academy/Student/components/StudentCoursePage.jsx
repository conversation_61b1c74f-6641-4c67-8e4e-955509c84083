import React, { useState, useEffect } from 'react';
import {
    Box,
    Typo<PERSON>,
    Button,
    Paper,
    Drawer,
    AppBar,
    Toolbar,
    List,
    ListItem,
    ListItemText,
    ListItemIcon,
    Divider,
    Chip,
    Card,
    CardContent,
    IconButton,
    Alert,
    LinearProgress,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    CircularProgress
} from '@mui/material';
import {
    ArrowBack as BackIcon,
    MenuBook as LessonIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    VideoLibrary as VideoIcon,
    AttachFile as FileIcon,
    Create as TextIcon,
    Schedule as ScheduleIcon,
    PlayArrow as PlayIcon,
    CheckCircle as CheckIcon,
    Download as DownloadIcon,
    Timer as TimerIcon,
    Pause as PauseIcon,
    Stop as StopIcon,
    Lock as LockIcon
} from '@mui/icons-material';

const drawerWidth = 320;

function StudentCoursePage({ course, onClose, onProgress }) {
    const [courseContent, setCourseContent] = useState([]);
    const [selectedContent, setSelectedContent] = useState(null);
    const [completedItems, setCompletedItems] = useState(new Set());
    const [currentProgress, setCurrentProgress] = useState(0);

    // Timer states
    const [timeRemaining, setTimeRemaining] = useState(0);
    const [isTimerActive, setIsTimerActive] = useState(false);
    const [isPaused, setIsPaused] = useState(false);
    const [showCompleteDialog, setShowCompleteDialog] = useState(false);
    const [canComplete, setCanComplete] = useState(false);

    useEffect(() => {
        // Giả lập dữ liệu nội dung khóa học
        const mockContent = [
            {
                id: 1,
                type: 'lesson',
                title: 'Giới thiệu phương trình bậc hai',
                description: 'Tìm hiểu khái niệm và dạng tổng quát của phương trình bậc hai',
                contentType: 'text',
                content: `
                    <h2 style="color: #2e7d32;">Phương trình bậc hai</h2>
                    <p>Phương trình bậc hai là phương trình có dạng tổng quát:</p>
                    <p style="text-align: center; font-size: 20px; font-weight: bold; color: #2e7d32; background: #e8f5e8; padding: 15px; border-radius: 10px; margin: 20px 0;">
                        ax² + bx + c = 0 (với a ≠ 0)
                    </p>
                    <h3 style="color: #1976d2;">Các thành phần:</h3>
                    <ul style="line-height: 1.8;">
                        <li><strong style="color: #d32f2f;">a, b, c</strong>: là các hệ số (a ≠ 0)</li>
                        <li><strong style="color: #d32f2f;">x</strong>: là ẩn số cần tìm</li>
                    </ul>
                    <h3 style="color: #1976d2;">Công thức nghiệm:</h3>
                    <p style="background: #fff3e0; padding: 15px; border-radius: 10px; border-left: 4px solid #ff9800;">
                        <strong>Δ = b² - 4ac</strong>
                    </p>
                    <ul style="line-height: 1.8;">
                        <li>Nếu <strong style="color: #388e3c;">Δ > 0</strong>: phương trình có 2 nghiệm phân biệt</li>
                        <li>Nếu <strong style="color: #f57c00;">Δ = 0</strong>: phương trình có nghiệm kép</li>
                        <li>Nếu <strong style="color: #d32f2f;">Δ < 0</strong>: phương trình vô nghiệm</li>
                    </ul>
                    <div style="background: #e3f2fd; padding: 20px; border-radius: 10px; margin: 15px 0;">
                        <h4 style="color: #1976d2;">Ví dụ minh họa:</h4>
                        <p><strong>Giải phương trình:</strong> x² - 5x + 6 = 0</p>
                        <p>Ta có: a = 1, b = -5, c = 6</p>
                        <p>Δ = (-5)² - 4.1.6 = 25 - 24 = 1 > 0</p>
                        <p style="color: #2e7d32;"><strong>Vậy phương trình có 2 nghiệm phân biệt:</strong></p>
                        <p>x₁ = (5 + √1)/2 = 3</p>
                        <p>x₂ = (5 - √1)/2 = 2</p>
                    </div>
                `,
                duration: 10, // 10 phút cho demo, thực tế sẽ là 45
                order: 1,
                isLocked: false
            },
            {
                id: 2,
                type: 'lesson',
                title: 'Video hướng dẫn giải phương trình',
                description: 'Video minh họa cách giải phương trình bậc hai',
                contentType: 'video',
                videoUrl: 'https://www.youtube.com/embed/dQw4w9WgXcQ',
                duration: 8, // 8 phút cho demo
                order: 2,
                isLocked: true
            },
            {
                id: 3,
                type: 'quiz',
                title: 'Kiểm tra phương trình bậc hai',
                description: 'Bài kiểm tra kiến thức về phương trình bậc hai',
                questions: [
                    {
                        question: 'Phương trình x² - 5x + 6 = 0 có nghiệm là?',
                        options: ['x = 2, x = 3', 'x = 1, x = 6', 'x = -2, x = -3', 'Vô nghiệm'],
                        correctAnswer: 0
                    }
                ],
                duration: 5, // 5 phút cho demo
                order: 3,
                isLocked: true
            }
        ];

        setCourseContent(mockContent);

        // Chọn bài đầu tiên mặc định
        if (mockContent.length > 0) {
            setSelectedContent(mockContent[0]);
        }
    }, []);

    // Timer effect
    useEffect(() => {
        let interval = null;
        if (isTimerActive && !isPaused && timeRemaining > 0) {
            interval = setInterval(() => {
                setTimeRemaining(time => {
                    if (time <= 1) {
                        setIsTimerActive(false);
                        setCanComplete(true);
                        return 0;
                    }
                    return time - 1;
                });
            }, 1000);
        } else if (!isTimerActive || timeRemaining === 0) {
            clearInterval(interval);
        }
        return () => clearInterval(interval);
    }, [isTimerActive, isPaused, timeRemaining]);

    const handleSelectContent = (content) => {
        if (content.isLocked && !completedItems.has(content.id - 1)) {
            return; // Không cho phép chọn bài bị khóa
        }

        setSelectedContent(content);
        setTimeRemaining(content.duration * 60); // Convert to seconds
        setIsTimerActive(false);
        setIsPaused(false);
        setCanComplete(false);
    };

    const handleStartTimer = () => {
        setIsTimerActive(true);
        setIsPaused(false);
    };

    const handlePauseTimer = () => {
        setIsPaused(!isPaused);
    };

    const handleStopTimer = () => {
        setIsTimerActive(false);
        setIsPaused(false);
        if (selectedContent) {
            setTimeRemaining(selectedContent.duration * 60);
        }
    };

    const handleCompleteLesson = () => {
        if (selectedContent && canComplete) {
            setCompletedItems(prev => new Set([...prev, selectedContent.id]));

            // Unlock next lesson
            const nextContent = courseContent.find(c => c.order === selectedContent.order + 1);
            if (nextContent) {
                setCourseContent(prev => prev.map(c =>
                    c.id === nextContent.id ? { ...c, isLocked: false } : c
                ));
            }

            // Update progress
            const newProgress = Math.round(((completedItems.size + 1) / courseContent.length) * 100);
            setCurrentProgress(newProgress);

            if (onProgress) {
                onProgress({
                    progress: newProgress,
                    completedLessons: completedItems.size + 1,
                    nextLesson: nextContent ? nextContent.title : 'Hoàn thành khóa học'
                });
            }

            setShowCompleteDialog(true);
        }
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    };

    const getContentIcon = (type, contentType) => {
        if (type === 'lesson') {
            switch (contentType) {
                case 'video': return <VideoIcon sx={{ color: '#f44336' }} />;
                case 'file': return <FileIcon sx={{ color: '#ff9800' }} />;
                default: return <TextIcon sx={{ color: '#2196f3' }} />;
            }
        } else if (type === 'quiz') {
            return <QuizIcon sx={{ color: '#9c27b0' }} />;
        } else if (type === 'turbowarp') {
            return <TurboWarpIcon sx={{ color: '#4caf50' }} />;
        }
        return <LessonIcon />;
    };

    const renderContent = () => {
        if (!selectedContent) return null;

        switch (selectedContent.type) {
            case 'lesson':
                if (selectedContent.contentType === 'text') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <div
                                dangerouslySetInnerHTML={{ __html: selectedContent.content }}
                                style={{ lineHeight: 1.6 }}
                            />
                        </Box>
                    );
                } else if (selectedContent.contentType === 'video') {
                    return (
                        <Box sx={{ p: 3 }}>
                            <Typography variant="body1" sx={{ mb: 3 }}>
                                {selectedContent.description}
                            </Typography>
                            <Box sx={{
                                position: 'relative',
                                paddingBottom: '56.25%',
                                height: 0,
                                borderRadius: '10px',
                                overflow: 'hidden'
                            }}>
                                <iframe
                                    src={selectedContent.videoUrl}
                                    style={{
                                        position: 'absolute',
                                        top: 0,
                                        left: 0,
                                        width: '100%',
                                        height: '100%',
                                        border: 'none'
                                    }}
                                    allowFullScreen
                                    title={selectedContent.title}
                                />
                            </Box>
                        </Box>
                    );
                }
                break;

            case 'quiz':
                return (
                    <Box sx={{ p: 3 }}>
                        <Typography variant="body1" sx={{ mb: 3 }}>
                            {selectedContent.description}
                        </Typography>
                        <Alert severity="info" sx={{ mb: 3, borderRadius: '10px' }}>
                            Bài kiểm tra gồm {selectedContent.questions.length} câu hỏi - Thời gian: {selectedContent.duration} phút
                        </Alert>

                        {selectedContent.questions.map((question, index) => (
                            <Card key={index} sx={{ mb: 2, borderRadius: '10px' }}>
                                <CardContent>
                                    <Typography variant="h6" gutterBottom>
                                        Câu {index + 1}: {question.question}
                                    </Typography>
                                    {question.options.map((option, optIndex) => (
                                        <Typography
                                            key={optIndex}
                                            variant="body2"
                                            sx={{ ml: 2, mb: 1 }}
                                        >
                                            {String.fromCharCode(65 + optIndex)}. {option}
                                        </Typography>
                                    ))}
                                </CardContent>
                            </Card>
                        ))}
                    </Box>
                );

            default:
                return null;
        }
    };

    return (
        <Box sx={{ display: 'flex', height: '100vh' }}>
            {/* AppBar */}
            <AppBar
                position="fixed"
                sx={{
                    zIndex: (theme) => theme.zIndex.drawer + 1,
                    bgcolor: '#2e7d32',
                    borderRadius: '0 0 10px 10px'
                }}
            >
                <Toolbar>
                    <IconButton
                        color="inherit"
                        onClick={onClose}
                        sx={{ mr: 2 }}
                    >
                        <BackIcon />
                    </IconButton>
                    <Typography variant="h6" noWrap component="div" sx={{ flexGrow: 1 }}>
                        {selectedContent?.title || course.title}
                    </Typography>

                    {/* Timer Display */}
                    {selectedContent && (
                        <Box sx={{ display: 'flex', alignItems: 'center', mr: 2 }}>
                            <TimerIcon sx={{ mr: 1 }} />
                            <Typography variant="h6" sx={{ mr: 2 }}>
                                {formatTime(timeRemaining)}
                            </Typography>

                            {!isTimerActive && timeRemaining > 0 && (
                                <Button
                                    variant="outlined"
                                    color="inherit"
                                    startIcon={<PlayIcon />}
                                    onClick={handleStartTimer}
                                    sx={{ mr: 1, borderRadius: '10px' }}
                                >
                                    Bắt đầu
                                </Button>
                            )}

                            {isTimerActive && (
                                <>
                                    <IconButton
                                        color="inherit"
                                        onClick={handlePauseTimer}
                                        sx={{ mr: 1 }}
                                    >
                                        <PauseIcon />
                                    </IconButton>
                                    <IconButton
                                        color="inherit"
                                        onClick={handleStopTimer}
                                    >
                                        <StopIcon />
                                    </IconButton>
                                </>
                            )}
                        </Box>
                    )}

                    <Chip
                        label={`${completedItems.size}/${courseContent.length} hoàn thành`}
                        color="secondary"
                        sx={{ borderRadius: '10px' }}
                    />
                </Toolbar>
            </AppBar>

            {/* Sidebar */}
            <Drawer
                variant="permanent"
                sx={{
                    width: drawerWidth,
                    flexShrink: 0,
                    '& .MuiDrawer-paper': {
                        width: drawerWidth,
                        boxSizing: 'border-box',
                        borderRadius: '0 10px 10px 0'
                    },
                }}
            >
                <Toolbar />
                <Box sx={{ p: 2 }}>
                    <Typography variant="h6" gutterBottom>
                        {course.title}
                    </Typography>
                    <Typography variant="body2" color="textSecondary" gutterBottom>
                        {course.teacher}
                    </Typography>

                    {/* Progress */}
                    <Box sx={{ mt: 2, mb: 3 }}>
                        <Typography variant="body2" gutterBottom>
                            Tiến độ: {currentProgress}%
                        </Typography>
                        <LinearProgress
                            variant="determinate"
                            value={currentProgress}
                            sx={{ height: 8, borderRadius: 4 }}
                        />
                    </Box>
                </Box>

                <Divider />

                <List sx={{ flexGrow: 1, overflow: 'auto' }}>
                    {courseContent
                        .sort((a, b) => a.order - b.order)
                        .map((content) => (
                            <ListItem
                                key={content.id}
                                button
                                selected={selectedContent?.id === content.id}
                                onClick={() => handleSelectContent(content)}
                                disabled={content.isLocked && !completedItems.has(content.id - 1)}
                                sx={{
                                    borderRadius: '10px',
                                    mx: 1,
                                    mb: 1,
                                    position: 'relative',
                                    border: selectedContent?.id === content.id ? '2px solid #2e7d32' : '2px solid transparent',
                                    boxShadow: selectedContent?.id === content.id ? '0 4px 12px rgba(46, 125, 50, 0.2)' : 'none',
                                    transform: selectedContent?.id === content.id ? 'scale(1.02)' : 'scale(1)',
                                    transition: 'all 0.2s ease-in-out',
                                    opacity: content.isLocked && !completedItems.has(content.id - 1) ? 0.5 : 1,
                                    '&.Mui-selected': {
                                        bgcolor: '#e8f5e8',
                                        '&:hover': {
                                            bgcolor: '#c8e6c9',
                                        },
                                    },
                                    '&:hover': {
                                        transform: content.isLocked && !completedItems.has(content.id - 1) ? 'scale(1)' : 'scale(1.01)',
                                        boxShadow: content.isLocked && !completedItems.has(content.id - 1) ? 'none' : '0 2px 8px rgba(0, 0, 0, 0.1)',
                                    },
                                    '&::before': selectedContent?.id === content.id ? {
                                        content: '""',
                                        position: 'absolute',
                                        left: '-8px',
                                        top: '50%',
                                        transform: 'translateY(-50%)',
                                        width: '4px',
                                        height: '60%',
                                        bgcolor: '#2e7d32',
                                        borderRadius: '2px',
                                        transition: 'all 0.2s ease-in-out'
                                    } : {}
                                }}
                            >
                                <ListItemIcon>
                                    <Box sx={{ position: 'relative' }}>
                                        {getContentIcon(content.type, content.contentType)}
                                        {content.isLocked && !completedItems.has(content.id - 1) && (
                                            <Box sx={{
                                                position: 'absolute',
                                                top: -4,
                                                right: -4,
                                                bgcolor: 'white',
                                                borderRadius: '50%',
                                                p: 0.2
                                            }}>
                                                <LockIcon sx={{ fontSize: 12, color: '#757575' }} />
                                            </Box>
                                        )}
                                    </Box>
                                </ListItemIcon>
                                <ListItemText
                                    primary={
                                        <Typography
                                            variant="body2"
                                            sx={{
                                                fontWeight: selectedContent?.id === content.id ? 600 : 400,
                                                color: selectedContent?.id === content.id ? '#2e7d32' : 'inherit',
                                                transition: 'all 0.2s ease-in-out'
                                            }}
                                        >
                                            {content.title}
                                        </Typography>
                                    }
                                    secondary={
                                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 0.5 }}>
                                            <ScheduleIcon sx={{
                                                fontSize: 14,
                                                mr: 0.5,
                                                color: selectedContent?.id === content.id ? '#2e7d32' : 'inherit'
                                            }} />
                                            <Typography
                                                variant="caption"
                                                sx={{
                                                    color: selectedContent?.id === content.id ? '#2e7d32' : 'inherit',
                                                    fontWeight: selectedContent?.id === content.id ? 500 : 400
                                                }}
                                            >
                                                {content.duration} phút
                                            </Typography>
                                            {completedItems.has(content.id) && (
                                                <CheckIcon sx={{
                                                    fontSize: 16,
                                                    ml: 1,
                                                    color: '#4caf50'
                                                }} />
                                            )}
                                        </Box>
                                    }
                                />
                            </ListItem>
                        ))}
                </List>

                {selectedContent && canComplete && !completedItems.has(selectedContent.id) && (
                    <Box sx={{ p: 2 }}>
                        <Button
                            variant="contained"
                            startIcon={<CheckIcon />}
                            fullWidth
                            onClick={handleCompleteLesson}
                            sx={{ borderRadius: '10px', bgcolor: '#2e7d32' }}
                        >
                            Hoàn thành bài học
                        </Button>
                    </Box>
                )}
            </Drawer>

            {/* Main content */}
            <Box
                component="main"
                sx={{
                    flexGrow: 1,
                    bgcolor: 'background.default',
                    overflow: 'auto'
                }}
            >
                <Toolbar />
                <Paper sx={{ m: 2, borderRadius: '10px', minHeight: 'calc(100vh - 120px)' }}>
                    {selectedContent && !isTimerActive && timeRemaining > 0 && (
                        <Alert severity="warning" sx={{ m: 2, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Typography>
                                    Bạn cần xem hết {selectedContent.duration} phút để hoàn thành bài học này
                                </Typography>
                                <Button
                                    variant="contained"
                                    startIcon={<PlayIcon />}
                                    onClick={handleStartTimer}
                                    sx={{ borderRadius: '10px' }}
                                >
                                    Bắt đầu học
                                </Button>
                            </Box>
                        </Alert>
                    )}

                    {isTimerActive && (
                        <Alert severity="info" sx={{ m: 2, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Typography>
                                    Đang học bài... Thời gian còn lại: {formatTime(timeRemaining)}
                                </Typography>
                                <Box>
                                    <IconButton onClick={handlePauseTimer} color="primary">
                                        <PauseIcon />
                                    </IconButton>
                                    <IconButton onClick={handleStopTimer} color="error">
                                        <StopIcon />
                                    </IconButton>
                                </Box>
                            </Box>
                        </Alert>
                    )}

                    {canComplete && !completedItems.has(selectedContent?.id) && (
                        <Alert severity="success" sx={{ m: 2, borderRadius: '10px' }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                                <Typography>
                                    Bạn đã xem hết bài học! Hãy đánh dấu hoàn thành.
                                </Typography>
                                <Button
                                    variant="contained"
                                    startIcon={<CheckIcon />}
                                    onClick={handleCompleteLesson}
                                    sx={{ borderRadius: '10px' }}
                                >
                                    Hoàn thành
                                </Button>
                            </Box>
                        </Alert>
                    )}

                    {renderContent()}
                </Paper>
            </Box>

            {/* Complete Dialog */}
            <Dialog open={showCompleteDialog} onClose={() => setShowCompleteDialog(false)}>
                <DialogTitle>
                    Chúc mừng!
                </DialogTitle>
                <DialogContent>
                    <Typography>
                        Bạn đã hoàn thành bài học "{selectedContent?.title}" thành công!
                    </Typography>
                    {courseContent.find(c => c.order === (selectedContent?.order || 0) + 1) && (
                        <Typography sx={{ mt: 2 }}>
                            Bài học tiếp theo đã được mở khóa.
                        </Typography>
                    )}
                </DialogContent>
                <DialogActions>
                    <Button onClick={() => setShowCompleteDialog(false)} sx={{ borderRadius: '10px' }}>
                        Tiếp tục
                    </Button>
                </DialogActions>
            </Dialog>
        </Box>
    );
}

export default StudentCoursePage;
