import { useState } from 'react';
import { useNavigate, Routes, Route, useLocation } from 'react-router-dom';
import {
    Box,
    AppBar,
    Toolbar,
    Typography,
    IconButton,
    Badge,
    Avatar,
    Menu,
    MenuItem,
    ListItemIcon,
    Divider,
    Container,
    TextField,
    InputAdornment,
    Button,
    Stack,
    Drawer,
    List,
    ListItemText,
    ListItemButton,
} from '@mui/material';
import {
    Dashboard as DashboardIcon,
    Notifications as NotificationIcon,
    AccountCircle as AccountIcon,
    Settings as SettingsIcon,
    Logout as LogoutIcon,
    Search as SearchIcon,
    Category as CategoryIcon,
    ExpandMore as ExpandMoreIcon,
    Menu as MenuIcon,
    School as SchoolIcon,
    Assignment as AssignmentIcon,
    Quiz as QuizIcon,
    Games as TurboWarpIcon,
    Person as PersonIcon
} from '@mui/icons-material';
// Import các component con
import StudentDashboard from './components/StudentDashboard';
import CourseMarketplace from './components/CourseMarketplace';
import CourseDetail from './components/CourseDetail';
import CourseLearning from './components/CourseLearning';
import { useDocumentTitle } from '../../../hooks/useDocumentTitle';

function Student({ user }) {
    useDocumentTitle("BeE E-Learning");
    const navigate = useNavigate();
    const location = useLocation();
    const [anchorEl, setAnchorEl] = useState(null);
    const [categoryAnchorEl, setCategoryAnchorEl] = useState(null);
    const [searchQuery, setSearchQuery] = useState('');
    const [sidebarOpen, setSidebarOpen] = useState(true);

    // Check if current page should show sidebar
    const shouldShowSidebar = location.pathname === '/e-learning/dashboard';

    const handleProfileMenuOpen = (event) => {
        setAnchorEl(event.currentTarget);
    };

    const handleProfileMenuClose = () => {
        setAnchorEl(null);
    };

    const handleCategoryMenuOpen = (event) => {
        setCategoryAnchorEl(event.currentTarget);
    };

    const handleCategoryMenuClose = () => {
        setCategoryAnchorEl(null);
    };

    const handleSearch = (event) => {
        if (event.key === 'Enter') {
            // Implement search functionality
        }
    };

    const renderContent = () => {
        return (
            <Routes>
                <Route path="/" element={<CourseMarketplace user={user} />} />
                <Route path="/course/:courseId" element={<CourseDetail user={user} />} />
                <Route path="/course/:courseId/learn" element={<CourseLearning user={user} />} />
                <Route path="/dashboard" element={<StudentDashboard user={user} />} />
            </Routes>
        );
    };

    return (
        <Box>
            {/* BeE STEM Header */}
            <AppBar
                position="fixed"
                sx={{
                    bgcolor: '#1a237e', // Deep blue for STEM
                    color: 'white',
                    boxShadow: '0 4px 20px rgba(26, 35, 126, 0.3)',
                }}
            >
                <Container maxWidth="xl">
                    <Toolbar sx={{ px: 0 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
                            <Box sx={{ display: 'flex' }}>
                                {/* Menu Button - only show on dashboard */}
                                {shouldShowSidebar && (
                                    <IconButton
                                        color="inherit"
                                        onClick={() => setSidebarOpen(!sidebarOpen)}
                                        sx={{ mr: 2 }}
                                    >
                                        <MenuIcon />
                                    </IconButton>
                                )}

                                {/* Logo with STEM styling */}
                                <Box sx={{ display: 'flex', alignItems: 'center', mr: 4 }}>
                                    <Box
                                        sx={{
                                            width: 54,
                                            height: 54,
                                            // borderRadius: '40px',
                                            // background: beeColors.background.gradient,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            mr: 2,
                                            // p: 0.5,
                                            // border: `1px solid white`,
                                            cursor: "pointer"
                                        }}
                                        onClick={() => navigate('/')}
                                    >
                                        <img
                                            src="/beeIco.svg"
                                            alt="BeE STEM Logo"
                                            style={{
                                                width: '100%',
                                                height: '100%',
                                                objectFit: 'contain'
                                            }}
                                        />
                                    </Box>
                                    <Typography
                                        variant="h5"
                                        component="div"
                                        sx={{
                                            fontWeight: 'bold',
                                            color: 'white',
                                            cursor: 'pointer'
                                        }}
                                        onClick={() => navigate('/e-learning')}
                                    >
                                        BeE E-Learning
                                    </Typography>
                                </Box>

                                {/* STEM Categories */}
                                <Button
                                    startIcon={<CategoryIcon />}
                                    endIcon={<ExpandMoreIcon />}
                                    onClick={handleCategoryMenuOpen}
                                    sx={{
                                        mr: 2,
                                        color: 'white',
                                        textTransform: 'none',
                                        fontWeight: 'normal',
                                        '&:hover': {
                                            bgcolor: 'rgba(255,255,255,0.1)'
                                        }
                                    }}
                                >
                                    Môn học STEM
                                </Button>


                                {/* Search Bar with STEM styling */}
                                <TextField
                                    placeholder="Tìm kiếm khóa học STEM..."
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                    onKeyDown={(e) => e.key === 'Enter' && handleSearch(e)}
                                    sx={{
                                        flexGrow: 1,
                                        maxWidth: 600,
                                        minWidth: 400,
                                        mr: 3,
                                        '& .MuiOutlinedInput-root': {
                                            height: 48,
                                            borderRadius: '25px',
                                            bgcolor: 'rgba(255,255,255,0.1)',
                                            '& fieldset': {
                                                borderColor: 'rgba(255,255,255,0.3)',
                                            },
                                            '&:hover fieldset': {
                                                borderColor: 'rgba(255,255,255,0.5)',
                                            },
                                            '&.Mui-focused fieldset': {
                                                borderColor: '#ffc107',
                                            },
                                            '& input': {
                                                color: 'white',
                                                '&::placeholder': {
                                                    color: 'rgba(255,255,255,0.7)'
                                                }
                                            }
                                        }
                                    }}
                                    slotProps={{
                                        input: {
                                            startAdornment: (
                                                <InputAdornment position="start">
                                                    <SearchIcon sx={{ color: 'rgba(255,255,255,0.7)' }} />
                                                </InputAdornment>
                                            ),
                                        }
                                    }}
                                />
                            </Box>

                            <Box sx={{ display: 'flex' }}>

                                {/* Notifications */}
                                <IconButton sx={{ color: 'white', mr: 2 }}>
                                    <Badge badgeContent={3} sx={{ '& .MuiBadge-badge': { bgcolor: '#ffc107', color: '#1a237e' } }}>
                                        <NotificationIcon />
                                    </Badge>
                                </IconButton>

                                {/* User Avatar */}
                                <IconButton
                                    onClick={handleProfileMenuOpen}
                                    sx={{ p: 0 }}
                                >
                                    <Avatar sx={{
                                        width: 36,
                                        height: 36,
                                        bgcolor: '#ffc107',
                                        color: '#1a237e',
                                        fontWeight: 'bold',

                                    }}
                                        src={user?.avatar_url}
                                    >

                                    </Avatar>
                                </IconButton>
                            </Box>
                        </Box>
                    </Toolbar>
                </Container>
            </AppBar>

            {/* STEM Categories Menu */}
            <Menu
                anchorEl={categoryAnchorEl}
                open={Boolean(categoryAnchorEl)}
                onClose={handleCategoryMenuClose}
                slotProps={{
                    paper: {
                        sx: {
                            mt: 1,
                            minWidth: 320,
                            maxHeight: 400,
                            borderRadius: 2,
                            boxShadow: '0 8px 32px rgba(26, 35, 126, 0.2)'
                        }
                    }
                }}
            >
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#1a237e', mr: 2 }} />
                        <Typography fontWeight="medium">🔬 Khoa học (Science)</Typography>
                    </Box>
                </MenuItem>
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#2e7d32', mr: 2 }} />
                        <Typography fontWeight="medium">⚙️ Công nghệ (Technology)</Typography>
                    </Box>
                </MenuItem>
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#ed6c02', mr: 2 }} />
                        <Typography fontWeight="medium">🔧 Kỹ thuật (Engineering)</Typography>
                    </Box>
                </MenuItem>
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#9c27b0', mr: 2 }} />
                        <Typography fontWeight="medium">📊 Toán học (Mathematics)</Typography>
                    </Box>
                </MenuItem>
                <Divider sx={{ my: 1 }} />
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#d32f2f', mr: 2 }} />
                        <Typography fontWeight="medium">💻 Lập trình</Typography>
                    </Box>
                </MenuItem>
                <MenuItem onClick={handleCategoryMenuClose} sx={{ py: 1.5 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: 8, height: 8, borderRadius: '50%', bgcolor: '#1976d2', mr: 2 }} />
                        <Typography fontWeight="medium">🤖 Robotics & AI</Typography>
                    </Box>
                </MenuItem>
            </Menu>

            <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleProfileMenuClose}
            >
                <MenuItem onClick={() => {
                    handleProfileMenuClose();
                    navigate('/e-learning/dashboard');
                }}>
                    <ListItemIcon>
                        <DashboardIcon fontSize="small" />
                    </ListItemIcon>
                    Dashboard
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <AccountIcon fontSize="small" />
                    </ListItemIcon>
                    Hồ sơ cá nhân
                </MenuItem>
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <SettingsIcon fontSize="small" />
                    </ListItemIcon>
                    Cài đặt
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    Đăng xuất
                </MenuItem>
            </Menu>

            {/* User Profile Menu */}
            <Menu
                anchorEl={anchorEl}
                open={Boolean(anchorEl)}
                onClose={handleProfileMenuClose}
            >
                <MenuItem onClick={() => {
                    handleProfileMenuClose();
                    navigate('/e-learning/dashboard');
                }}>
                    <ListItemIcon>
                        <DashboardIcon fontSize="small" />
                    </ListItemIcon>
                    Dashboard
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <AccountIcon fontSize="small" />
                    </ListItemIcon>
                    Hồ sơ cá nhân
                </MenuItem>
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <SettingsIcon fontSize="small" />
                    </ListItemIcon>
                    Cài đặt
                </MenuItem>
                <Divider />
                <MenuItem onClick={handleProfileMenuClose}>
                    <ListItemIcon>
                        <LogoutIcon fontSize="small" />
                    </ListItemIcon>
                    Đăng xuất
                </MenuItem>
            </Menu>

            {/* Sidebar - only show on dashboard */}
            {
                shouldShowSidebar && (
                    <Drawer
                        variant="persistent"
                        anchor="left"
                        open={sidebarOpen}
                        sx={{
                            width: 280,
                            flexShrink: 0,
                            '& .MuiDrawer-paper': {
                                width: 280,
                                boxSizing: 'border-box',
                                bgcolor: 'white',
                                borderRight: '1px solid rgba(26, 35, 126, 0.1)',
                                mt: 8
                            },
                        }}
                    >
                        <Box sx={{ p: 3 }}>
                            <Typography variant="h6" fontWeight="bold" sx={{ color: '#1a237e', mb: 2 }}>
                                🎓 BeE STEM Learning
                            </Typography>
                            <Typography variant="body2" color="textSecondary" gutterBottom>
                                Chào {user?.first_name || 'bạn'}!
                            </Typography>
                        </Box>

                        <List sx={{ px: 2 }}>
                            <ListItemButton
                                onClick={() => navigate('/e-learning/dashboard')}
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <DashboardIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Dashboard" />
                            </ListItemButton>

                            <ListItemButton
                                onClick={() => navigate('/e-learning')}
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <SchoolIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Khóa học" />
                            </ListItemButton>

                            <ListItemButton
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <AssignmentIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Bài tập" />
                            </ListItemButton>

                            <ListItemButton
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <QuizIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Quiz" />
                            </ListItemButton>

                            <ListItemButton
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <TurboWarpIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="TurboWarp" />
                            </ListItemButton>

                            <Divider sx={{ my: 2 }} />

                            <ListItemButton
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <PersonIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Hồ sơ" />
                            </ListItemButton>

                            <ListItemButton
                                sx={{
                                    mb: 1,
                                    borderRadius: 2,
                                    '&:hover': { bgcolor: 'rgba(26, 35, 126, 0.05)' }
                                }}
                            >
                                <ListItemIcon sx={{ minWidth: 40 }}>
                                    <SettingsIcon sx={{ color: '#1a237e' }} />
                                </ListItemIcon>
                                <ListItemText primary="Cài đặt" />
                            </ListItemButton>
                        </List>
                    </Drawer>
                )
            }

            {/* Main Content */}
            <Box
                component="main"
                sx={{
                    minHeight: '100vh',
                    pt: 8, // Space for fixed AppBar
                    ml: shouldShowSidebar && sidebarOpen ? '280px' : 0,
                    transition: 'margin-left 0.3s ease'
                }}
            >
                {renderContent()}
            </Box>
        </Box >
    );
}

export default Student;
