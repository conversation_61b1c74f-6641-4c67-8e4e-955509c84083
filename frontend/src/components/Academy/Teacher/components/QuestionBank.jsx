import { useState, useEffect, useRef } from 'react';
import { elearningAPI } from '../../../../services';
import * as XLSX from 'xlsx';
import { saveAs } from 'file-saver';
import {
    convertQuestionsToExcelFormat,
    generateTemplateData,
    getExcelColumnWidths,
    getTemplateColumnWidths,
    validateQuestionRow,
    convertRowToQuestion
} from '../../../../utils/excelUtils';
import {
    Box,
    Typography,
    Button,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TablePagination,
    IconButton,
    Chip,
    Dialog,
    DialogTitle,
    DialogContent,
    DialogActions,
    TextField,
    FormControl,
    FormControlLabel,
    InputLabel,
    Select,
    MenuItem,
    Checkbox,
    Card,
    CardContent,
    CardActions,
    Fab,
    Tooltip,
    Alert,
    Snackbar,
    Stack,
    Avatar,
    alpha,
    LinearProgress,
    Divider
} from '@mui/material';
import Grid from "@mui/material/Grid2";
import {
    Add as AddIcon,
    Edit as EditIcon,
    Delete as DeleteIcon,
    Search as SearchIcon,
    FilterList as FilterIcon,
    QuestionAnswer as QuestionIcon,
    TrendingUp as TrendingUpIcon,
    School as SchoolIcon,
    EmojiEvents as EmojiEventsIcon,
    Assignment as AssignmentIcon,
    Visibility as VisibilityIcon,
    ViewList as ViewListIcon,
    SpaceDashboard as SpaceDashboardIcon,
    FileDownload as FileDownloadIcon,
    FileUpload as FileUploadIcon,
    GetApp as GetAppIcon,
    Help as HelpIcon
} from '@mui/icons-material';
import CustomButton, { beeColors } from '../../../Common/CustomButton';
import CustomTextField from '../../../Common/CustomTextField';

function QuestionBank() {
    const [questions, setQuestions] = useState([]);
    const [filteredQuestions, setFilteredQuestions] = useState([]);
    const [page, setPage] = useState(0);
    const [rowsPerPage, setRowsPerPage] = useState(12);
    const [openDialog, setOpenDialog] = useState(false);
    const [editingQuestion, setEditingQuestion] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [filterSubject, setFilterSubject] = useState('');
    const [filterDifficulty, setFilterDifficulty] = useState('');
    const [snackbar, setSnackbar] = useState({ open: false, message: '', severity: 'success' });
    const [subjects, setSubjects] = useState([]);
    const [grades, setGrades] = useState([]);
    const [viewMode, setViewMode] = useState('table'); // 'table' or 'cards'

    // Import/Export states
    const [importDialog, setImportDialog] = useState(false);
    const [importLoading, setImportLoading] = useState(false);
    const [importProgress, setImportProgress] = useState(0);
    const [importResults, setImportResults] = useState(null);
    const [helpDialog, setHelpDialog] = useState(false);
    const fileInputRef = useRef(null);

    // Form state
    const [formData, setFormData] = useState({
        question: '',
        options: ['', '', '', ''],
        correctAnswers: [], // Changed to array for multiple correct answers
        subject: '',
        grade: '',
        difficulty: 'medium',
        type: 'multiple_choice',
        points: 1,
        explanation: '',
        tags: ''
    });

    const difficulties = [
        { value: 'easy', label: 'Dễ', color: 'success' },
        { value: 'medium', label: 'Trung bình', color: 'warning' },
        { value: 'hard', label: 'Khó', color: 'error' }
    ];

    useEffect(() => {
        loadQuestions();
        loadSubjectsAndGrades();
    }, []);

    const loadSubjectsAndGrades = async () => {
        try {
            const [subjectsData, gradesData] = await Promise.all([
                elearningAPI.teacherAPI.getSubjects(),
                elearningAPI.teacherAPI.getGrades()
            ]);
            setSubjects(subjectsData);
            setGrades(gradesData);
        } catch (error) {
            console.error('Error loading subjects and grades:', error);
            // Fallback to some default subjects if API fails
            setSubjects([]);
            setGrades([]);
        }
    };

    const loadQuestions = async () => {
        try {
            setLoading(true);
            const questionsData = await elearningAPI.teacherAPI.getQuestions();
            setQuestions(questionsData);
            setFilteredQuestions(questionsData);
        } catch (error) {
            console.error('Error loading questions:', error);
            showSnackbar('Không thể tải ngân hàng câu hỏi', 'error');
            // Fallback to empty array
            setQuestions([]);
            setFilteredQuestions([]);
        } finally {
            setLoading(false);
        }
    };

    const showSnackbar = (message, severity = 'success') => {
        setSnackbar({
            open: true,
            message,
            severity
        });
    };

    useEffect(() => {
        let filtered = questions;

        if (searchTerm) {
            filtered = filtered.filter(q =>
                q.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
                q.tags.toLowerCase().includes(searchTerm.toLowerCase())
            );
        }

        if (filterSubject) {
            filtered = filtered.filter(q => q.subject === filterSubject);
        }

        if (filterDifficulty) {
            filtered = filtered.filter(q => q.difficulty === filterDifficulty);
        }

        setFilteredQuestions(filtered);
        setPage(0);
    }, [questions, searchTerm, filterSubject, filterDifficulty]);

    const handleChangePage = (_, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    const handleOpenDialog = (question = null) => {
        if (question) {
            setEditingQuestion(question);
            setFormData({
                question: question.question_text,
                options: [...question.options],
                correctAnswers: question.correct_answers || [],
                subject: question.subject?.id || question.subject || '',
                grade: question.grade?.id || question.grade || '',
                difficulty: question.difficulty,
                type: question.question_type || 'multiple_choice',
                points: question.points || 1,
                explanation: question.explanation,
                tags: question.tags
            });
        } else {
            setEditingQuestion(null);
            setFormData({
                question: '',
                options: ['', '', '', ''],
                correctAnswers: [],
                subject: '',
                grade: '',
                difficulty: 'medium',
                type: 'multiple_choice',
                points: 1,
                explanation: '',
                tags: ''
            });
        }
        setOpenDialog(true);
    };

    const handleCloseDialog = () => {
        setOpenDialog(false);
        setEditingQuestion(null);
    };

    const handleFormChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
    };

    const handleOptionChange = (index, value) => {
        const newOptions = [...formData.options];
        newOptions[index] = value;
        setFormData(prev => ({
            ...prev,
            options: newOptions
        }));
    };

    const handleSaveQuestion = async () => {
        if (!formData.question || formData.options.some(opt => !opt)) {
            setSnackbar({
                open: true,
                message: 'Vui lòng điền đầy đủ thông tin câu hỏi và các đáp án!',
                severity: 'error'
            });
            return;
        }

        if (formData.type === 'multiple_choice' && formData.correctAnswers.length === 0) {
            setSnackbar({
                open: true,
                message: 'Vui lòng chọn ít nhất một đáp án đúng!',
                severity: 'error'
            });
            return;
        }

        try {
            const questionData = {
                question_text: formData.question,
                question_type: formData.type,
                difficulty: formData.difficulty,
                points: formData.points,
                options: formData.options,
                subject: formData.subject ? parseInt(formData.subject) : null,
                grade: formData.grade ? parseInt(formData.grade) : null,
                correct_answers: formData.correctAnswers,
                explanation: formData.explanation || '',
                tags: Array.isArray(formData.tags) ? formData.tags :
                    (formData.tags ? formData.tags.split(',').map(tag => tag.trim()) : [])
            };
            if (editingQuestion) {
                const updatedQuestion = await elearningAPI.teacherAPI.updateQuestion(
                    editingQuestion.id, questionData
                );
                setQuestions(prev => prev.map(q => q.id === editingQuestion.id ? updatedQuestion : q));
                setSnackbar({
                    open: true,
                    message: 'Cập nhật câu hỏi thành công!',
                    severity: 'success'
                });
            } else {
                const newQuestion = await elearningAPI.teacherAPI.createQuestion(questionData);
                setQuestions(prev => [newQuestion, ...prev]);
                setSnackbar({
                    open: true,
                    message: 'Thêm câu hỏi thành công!',
                    severity: 'success'
                });
            }

            handleCloseDialog();
        } catch (error) {
            console.error('Error saving question:', error);
            setSnackbar({
                open: true,
                message: 'Có lỗi xảy ra khi lưu câu hỏi',
                severity: 'error'
            });
        }
    };

    const handleDeleteQuestion = async (id) => {
        if (window.confirm('Bạn có chắc chắn muốn xóa câu hỏi này?')) {
            try {
                await elearningAPI.teacherAPI.deleteQuestion(id);
                setQuestions(prev => prev.filter(q => q.id !== id));
                setSnackbar({
                    open: true,
                    message: 'Xóa câu hỏi thành công!',
                    severity: 'success'
                });
            } catch (error) {
                console.error('Error deleting question:', error);
                setSnackbar({
                    open: true,
                    message: 'Có lỗi xảy ra khi xóa câu hỏi',
                    severity: 'error'
                });
            }
        }
    };

    const getDifficultyChip = (difficulty) => {
        const difficultyInfo = difficulties.find(d => d.value === difficulty);
        return (
            <Chip
                label={difficultyInfo?.label || difficulty}
                color={difficultyInfo?.color || 'default'}
                size="small"
            />
        );
    };

    // Export to Excel function
    const handleExportToExcel = () => {
        try {
            // Prepare data for export using utility function
            const exportData = convertQuestionsToExcelFormat(filteredQuestions, subjects, grades, difficulties);

            // Create workbook and worksheet
            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(exportData);

            // Set column widths
            ws['!cols'] = getExcelColumnWidths();

            // Add worksheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Câu hỏi trắc nghiệm');

            // Generate Excel file and download
            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

            const fileName = `cau-hoi-trac-nghiem-${new Date().toISOString().split('T')[0]}.xlsx`;
            saveAs(data, fileName);

            showSnackbar('Xuất file Excel thành công!', 'success');
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            showSnackbar('Có lỗi xảy ra khi xuất file Excel', 'error');
        }
    };

    // Download template function
    const handleDownloadTemplate = () => {
        try {
            const templateData = generateTemplateData();

            const wb = XLSX.utils.book_new();
            const ws = XLSX.utils.json_to_sheet(templateData);

            // Set column widths
            ws['!cols'] = getTemplateColumnWidths();

            XLSX.utils.book_append_sheet(wb, ws, 'Template');

            const excelBuffer = XLSX.write(wb, { bookType: 'xlsx', type: 'array' });
            const data = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });

            saveAs(data, 'template-cau-hoi-trac-nghiem.xlsx');
            showSnackbar('Tải template thành công!', 'success');
        } catch (error) {
            console.error('Error downloading template:', error);
            showSnackbar('Có lỗi xảy ra khi tải template', 'error');
        }
    };

    // Handle file selection for import
    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            handleImportFromExcel(file);
        }
        // Reset file input
        event.target.value = '';
    };

    // Import from Excel function
    const handleImportFromExcel = async (file) => {
        try {
            setImportLoading(true);
            setImportProgress(0);
            setImportResults(null);
            setImportDialog(true);

            const reader = new FileReader();
            reader.onload = async (e) => {
                try {
                    const data = new Uint8Array(e.target.result);
                    const workbook = XLSX.read(data, { type: 'array' });
                    const sheetName = workbook.SheetNames[0];
                    const worksheet = workbook.Sheets[sheetName];
                    const jsonData = XLSX.utils.sheet_to_json(worksheet);

                    if (jsonData.length === 0) {
                        throw new Error('File Excel không có dữ liệu');
                    }

                    setImportProgress(20);

                    // Validate and process data
                    const processedQuestions = [];
                    const errors = [];

                    for (let i = 0; i < jsonData.length; i++) {
                        const row = jsonData[i];
                        const rowNumber = i + 2; // +2 because Excel starts from 1 and has header

                        try {
                            // Validate row using utility function
                            const validation = validateQuestionRow(row, rowNumber);

                            if (!validation.isValid) {
                                errors.push(...validation.errors);
                                continue;
                            }

                            // Convert row to question data using utility function
                            const questionData = convertRowToQuestion(row);
                            processedQuestions.push(questionData);
                        } catch (error) {
                            errors.push(`Dòng ${rowNumber}: ${error.message}`);
                        }
                    }

                    setImportProgress(60);

                    // Import questions to server
                    const importedQuestions = [];

                    if (processedQuestions.length > 0) {
                        for (let i = 0; i < processedQuestions.length; i++) {
                            try {
                                const newQuestion = await elearningAPI.teacherAPI.createQuestion(processedQuestions[i]);
                                importedQuestions.push(newQuestion);
                                setImportProgress(60 + (i + 1) / processedQuestions.length * 30);
                            } catch (error) {
                                errors.push(`Không thể tạo câu hỏi: ${processedQuestions[i].question_text.substring(0, 50)}...`);
                            }
                        }
                    }

                    setImportProgress(100);

                    // Update questions list
                    if (importedQuestions.length > 0) {
                        setQuestions(prev => [...importedQuestions, ...prev]);
                    }

                    // Set results
                    setImportResults({
                        total: jsonData.length,
                        success: importedQuestions.length,
                        failed: jsonData.length - importedQuestions.length,
                        errors: errors
                    });

                    if (importedQuestions.length > 0) {
                        showSnackbar(`Import thành công ${importedQuestions.length} câu hỏi!`, 'success');
                    } else {
                        showSnackbar('Không có câu hỏi nào được import', 'warning');
                    }

                } catch (error) {
                    console.error('Error processing Excel file:', error);
                    setImportResults({
                        total: 0,
                        success: 0,
                        failed: 0,
                        errors: [error.message || 'Có lỗi xảy ra khi xử lý file Excel']
                    });
                    showSnackbar('Có lỗi xảy ra khi xử lý file Excel', 'error');
                } finally {
                    setImportLoading(false);
                }
            };

            reader.readAsArrayBuffer(file);
        } catch (error) {
            console.error('Error importing from Excel:', error);
            showSnackbar('Có lỗi xảy ra khi import file Excel', 'error');
            setImportLoading(false);
        }
    };

    const handleCloseImportDialog = () => {
        setImportDialog(false);
        setImportResults(null);
        setImportProgress(0);
    };

    return (
        <Box>
            {/* Header */}
            <Box sx={{
                background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.1)} 100%)`,
                borderRadius: '20px',
                p: 4,
                mb: 4,
                position: 'relative',
                overflow: 'hidden'
            }}>
                <Box sx={{
                    position: 'absolute',
                    top: -20,
                    right: -20,
                    width: 100,
                    height: 100,
                    borderRadius: '50%',
                    background: alpha(beeColors.primary.main, 0.1),
                    zIndex: 0
                }} />
                <Box sx={{
                    position: 'absolute',
                    bottom: -30,
                    left: -30,
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    background: alpha(beeColors.secondary.main, 0.1),
                    zIndex: 0
                }} />

                <Box sx={{ position: 'relative', zIndex: 1, display: "flex", justifyContent: "space-between" }}>
                    <Stack direction="row" alignItems="center" spacing={2} sx={{ mb: 2 }}>
                        <Avatar sx={{
                            bgcolor: beeColors.primary.main,
                            width: 56,
                            height: 56
                        }}>
                            <QuestionIcon sx={{ fontSize: 28 }} />
                        </Avatar>
                        <Box>
                            <Typography variant="h4" component="h1" sx={{
                                fontWeight: 700,
                                color: beeColors.neutral.main,
                                mb: 0.5
                            }}>
                                Ngân hàng câu hỏi
                            </Typography>
                            <Typography variant="body1" sx={{
                                color: beeColors.neutral.light,
                                fontSize: '1.1rem'
                            }}>
                                Quản lý và tổ chức câu hỏi cho các bài kiểm tra
                            </Typography>
                        </Box>
                    </Stack>

                    <Stack direction="row" spacing={2} sx={{ mt: 0 }}>
                        <Button
                            variant="contained"
                            startIcon={<AddIcon />}
                            onClick={() => handleOpenDialog()}
                            sx={{
                                background: beeColors.background.gradient,
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                fontSize: '1rem',
                                fontWeight: 600,
                                boxShadow: `0 4px 20px ${alpha(beeColors.primary.main, 0.3)}`,
                                '&:hover': {
                                    transform: 'translateY(-2px)',
                                    boxShadow: `0 6px 25px ${alpha(beeColors.primary.main, 0.4)}`
                                }
                            }}
                        >
                            Thêm câu hỏi mới
                        </Button>

                        <Button
                            variant="outlined"
                            startIcon={<FileUploadIcon />}
                            onClick={() => fileInputRef.current?.click()}
                            sx={{
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                borderColor: beeColors.secondary.main,
                                color: beeColors.secondary.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.secondary.main, 0.1),
                                    borderColor: beeColors.secondary.main
                                }
                            }}
                        >
                            Import Excel
                        </Button>

                        <Button
                            variant="outlined"
                            startIcon={<FileDownloadIcon />}
                            onClick={handleExportToExcel}
                            sx={{
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                borderColor: beeColors.primary.main,
                                color: beeColors.primary.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    borderColor: beeColors.primary.main
                                }
                            }}
                        >
                            Export Excel
                        </Button>

                        <Button
                            variant="text"
                            startIcon={<GetAppIcon />}
                            onClick={handleDownloadTemplate}
                            sx={{
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                color: beeColors.neutral.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.neutral.main, 0.1)
                                }
                            }}
                        >
                            Tải Template
                        </Button>

                        <Button
                            variant="text"
                            startIcon={<HelpIcon />}
                            onClick={() => setHelpDialog(true)}
                            sx={{
                                borderRadius: '12px',
                                px: 3,
                                py: 1.5,
                                textTransform: 'none',
                                color: beeColors.neutral.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.neutral.main, 0.1)
                                }
                            }}
                        >
                            Hướng dẫn
                        </Button>
                    </Stack>
                </Box>
            </Box>

            {/* Statistics */}
            <Grid container spacing={3} sx={{ mb: 4 }}>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.primary.main, 0.05)} 100%)`,
                        border: `1px solid ${alpha(beeColors.primary.main, 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha(beeColors.primary.main, 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: beeColors.primary.main,
                                    width: 48,
                                    height: 48
                                }}>
                                    <AssignmentIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Tổng số câu hỏi
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: beeColors.primary.main
                                    }}>
                                        {questions.length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha(beeColors.secondary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.05)} 100%)`,
                        border: `1px solid ${alpha(beeColors.secondary.main, 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha(beeColors.secondary.main, 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: beeColors.secondary.main,
                                    width: 48,
                                    height: 48
                                }}>
                                    <SchoolIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Môn học
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: beeColors.secondary.main
                                    }}>
                                        {new Set(questions.map(q => q.subject)).size}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha('#4CAF50', 0.1)} 0%, ${alpha('#4CAF50', 0.05)} 100%)`,
                        border: `1px solid ${alpha('#4CAF50', 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha('#4CAF50', 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: '#4CAF50',
                                    width: 48,
                                    height: 48
                                }}>
                                    <TrendingUpIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Câu hỏi dễ
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: '#4CAF50'
                                    }}>
                                        {questions.filter(q => q.difficulty === 'easy').length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
                <Grid size={{ xs: 12, sm: 6, md: 3 }}>
                    <Card sx={{
                        borderRadius: "16px",
                        background: `linear-gradient(135deg, ${alpha('#F44336', 0.1)} 0%, ${alpha('#F44336', 0.05)} 100%)`,
                        border: `1px solid ${alpha('#F44336', 0.1)}`,
                        transition: 'all 0.3s ease',
                        '&:hover': {
                            transform: 'translateY(-4px)',
                            boxShadow: `0 8px 25px ${alpha('#F44336', 0.15)}`
                        }
                    }}>
                        <CardContent sx={{ p: 3 }}>
                            <Stack direction="row" alignItems="center" spacing={2}>
                                <Avatar sx={{
                                    bgcolor: '#F44336',
                                    width: 48,
                                    height: 48
                                }}>
                                    <EmojiEventsIcon />
                                </Avatar>
                                <Box>
                                    <Typography variant="body2" sx={{
                                        color: beeColors.neutral.light,
                                        fontWeight: 500,
                                        mb: 0.5
                                    }}>
                                        Câu hỏi khó
                                    </Typography>
                                    <Typography variant="h4" sx={{
                                        fontWeight: 700,
                                        color: '#F44336'
                                    }}>
                                        {questions.filter(q => q.difficulty === 'hard').length}
                                    </Typography>
                                </Box>
                            </Stack>
                        </CardContent>
                    </Card>
                </Grid>
            </Grid>

            {/* Filters */}
            <Paper sx={{
                p: 3,
                mb: 4,
                borderRadius: "16px",
                background: alpha(beeColors.background.paper, 0.8),
                backdropFilter: 'blur(10px)',
                border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                boxShadow: `0 4px 20px ${alpha(beeColors.neutral.main, 0.08)}`
            }}>
                <Typography variant="h6" sx={{
                    mb: 3,
                    fontWeight: 600,
                    color: beeColors.neutral.main
                }}>
                    Tìm kiếm và lọc câu hỏi
                </Typography>
                <Grid container spacing={3} alignItems="center">
                    <Grid size={{ xs: 12, md: 4 }}>
                        <TextField
                            fullWidth
                            size="small"
                            placeholder="Tìm kiếm câu hỏi..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            slotProps={{
                                input: {
                                    startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
                                }
                            }}
                            sx={{
                                '& .MuiOutlinedInput-root': {
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper,
                                        boxShadow: `0 0 0 2px ${alpha(beeColors.primary.main, 0.2)}`
                                    }
                                }
                            }}
                        />
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Môn học</InputLabel>
                            <Select
                                value={filterSubject}
                                onChange={(e) => setFilterSubject(e.target.value)}
                                label="Môn học"
                                sx={{
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper
                                    }
                                }}
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {subjects.map(subject => (
                                    <MenuItem key={subject.id} value={subject.id}>{subject.name}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 3 }}>
                        <FormControl fullWidth size="small">
                            <InputLabel>Độ khó</InputLabel>
                            <Select
                                value={filterDifficulty}
                                onChange={(e) => setFilterDifficulty(e.target.value)}
                                label="Độ khó"
                                sx={{
                                    borderRadius: '12px',
                                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                                    '&:hover': {
                                        backgroundColor: alpha(beeColors.background.paper, 0.8)
                                    },
                                    '&.Mui-focused': {
                                        backgroundColor: beeColors.background.paper
                                    }
                                }}
                            >
                                <MenuItem value="">Tất cả</MenuItem>
                                {difficulties.map(difficulty => (
                                    <MenuItem key={difficulty.value} value={difficulty.value}>
                                        {difficulty.label}
                                    </MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Grid>
                    <Grid size={{ xs: 12, md: 2 }}>
                        <Button
                            fullWidth
                            // size="small"
                            variant="outlined"
                            startIcon={<FilterIcon />}
                            onClick={() => {
                                setSearchTerm('');
                                setFilterSubject('');
                                setFilterDifficulty('');
                            }}
                            sx={{
                                borderRadius: '12px',
                                // py: 1.5,
                                textTransform: 'none',
                                borderColor: beeColors.neutral.light,
                                color: beeColors.neutral.main,
                                '&:hover': {
                                    backgroundColor: alpha(beeColors.neutral.main, 0.1),
                                    borderColor: beeColors.neutral.main
                                }
                            }}
                        >
                            Xóa bộ lọc
                        </Button>
                    </Grid>
                </Grid>
            </Paper>

            {/* Questions Display */}
            <Box sx={{ mb: 3 }}>
                <Stack direction="row" justifyContent="space-between" alignItems="center" sx={{ mb: 3 }}>
                    <Typography variant="h6" sx={{
                        fontWeight: 600,
                        color: beeColors.neutral.main
                    }}>
                        Danh sách câu hỏi ({filteredQuestions.length})
                    </Typography>
                    <Stack direction="row" spacing={1}>
                        <Button
                            variant={viewMode === 'cards' ? 'contained' : 'outlined'}
                            size="small"
                            onClick={() => setViewMode('cards')}
                            sx={{ borderRadius: '8px' }}
                        >
                            <SpaceDashboardIcon />
                        </Button>
                        <Button
                            variant={viewMode === 'table' ? 'contained' : 'outlined'}
                            size="small"
                            onClick={() => setViewMode('table')}
                            sx={{ borderRadius: '8px' }}
                        >
                            <ViewListIcon />
                        </Button>
                    </Stack>
                </Stack>

                {viewMode === 'cards' ? (
                    <Grid container spacing={3}>
                        {filteredQuestions
                            .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                            .map((question) => (
                                <Grid size={{ xs: 12, md: 6, lg: 4 }} key={question.id}>
                                    <Card sx={{
                                        borderRadius: "16px",
                                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                                        transition: 'all 0.3s ease',
                                        '&:hover': {
                                            transform: 'translateY(-4px)',
                                            boxShadow: `0 8px 25px ${alpha(beeColors.neutral.main, 0.15)}`,
                                            borderColor: alpha(beeColors.primary.main, 0.3)
                                        }
                                    }}>
                                        <CardContent sx={{ p: 3 }}>
                                            <Stack spacing={2}>
                                                <Box>
                                                    <Typography variant="body1" sx={{
                                                        fontWeight: 500,
                                                        color: beeColors.neutral.main,
                                                        lineHeight: 1.5,
                                                        display: '-webkit-box',
                                                        WebkitLineClamp: 3,
                                                        WebkitBoxOrient: 'vertical',
                                                        overflow: 'hidden'
                                                    }}>
                                                        {question.question_text}
                                                    </Typography>
                                                </Box>

                                                <Stack direction="row" spacing={1} flexWrap="wrap">
                                                    <Chip
                                                        label={subjects.find(s => s.id === question.subject)?.name}
                                                        size="small"
                                                        sx={{
                                                            backgroundColor: alpha(beeColors.secondary.main, 0.1),
                                                            color: beeColors.secondary.main,
                                                            fontWeight: 500
                                                        }}
                                                    />
                                                    <Chip
                                                        label={grades.find(g => g.id === question.grade)?.name}
                                                        size="small"
                                                        variant="outlined"
                                                    />
                                                    {getDifficultyChip(question.difficulty)}
                                                </Stack>

                                                {question.tags && (
                                                    <Typography variant="body2" sx={{
                                                        color: beeColors.neutral.light,
                                                        fontStyle: 'italic'
                                                    }}>
                                                        Tags: {question.tags}
                                                    </Typography>
                                                )}

                                                <Typography variant="caption" sx={{
                                                    color: beeColors.neutral.light
                                                }}>
                                                    Tạo ngày: {question.created_at.split('T')[0]}
                                                </Typography>
                                            </Stack>
                                        </CardContent>
                                        <CardActions sx={{ px: 3, pb: 3, pt: 0 }}>
                                            <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                                                <Button
                                                    size="small"
                                                    startIcon={<VisibilityIcon />}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        textTransform: 'none',
                                                        flex: 1
                                                    }}
                                                >
                                                    Xem
                                                </Button>
                                                <Button
                                                    size="small"
                                                    startIcon={<EditIcon />}
                                                    onClick={() => handleOpenDialog(question)}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        textTransform: 'none',
                                                        flex: 1
                                                    }}
                                                >
                                                    Sửa
                                                </Button>
                                                <IconButton
                                                    size="small"
                                                    color="error"
                                                    onClick={() => handleDeleteQuestion(question.id)}
                                                    sx={{
                                                        borderRadius: '8px',
                                                        border: `1px solid ${alpha('#F44336', 0.3)}`,
                                                        '&:hover': {
                                                            backgroundColor: alpha('#F44336', 0.1)
                                                        }
                                                    }}
                                                >
                                                    <DeleteIcon fontSize="small" />
                                                </IconButton>
                                            </Stack>
                                        </CardActions>
                                    </Card>
                                </Grid>
                            ))}
                    </Grid>
                ) : (
                    <TableContainer component={Paper} sx={{
                        borderRadius: "16px",
                        border: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                        overflow: 'hidden'
                    }}>
                        <Table>
                            <TableHead sx={{
                                backgroundColor: alpha(beeColors.primary.main, 0.05)
                            }}>
                                <TableRow>
                                    <TableCell sx={{ fontWeight: 600 }}>Câu hỏi</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Môn học</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Lớp học</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Độ khó</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Tags</TableCell>
                                    <TableCell sx={{ fontWeight: 600 }}>Ngày tạo</TableCell>
                                    <TableCell align="center" sx={{ fontWeight: 600 }}>Thao tác</TableCell>
                                </TableRow>
                            </TableHead>
                            <TableBody>
                                {filteredQuestions
                                    .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                                    .map((question) => (
                                        <TableRow key={question.id} sx={{
                                            '&:hover': {
                                                backgroundColor: alpha(beeColors.primary.main, 0.05)
                                            }
                                        }}>
                                            <TableCell>
                                                <Typography variant="body2" sx={{ maxWidth: 300 }}>
                                                    {question.question_text.length > 100
                                                        ? `${question.question_text.substring(0, 100)}...`
                                                        : question.question_text
                                                    }
                                                </Typography>
                                            </TableCell>
                                            <TableCell>
                                                <Chip label={subjects.find(s => s.id === question.subject)?.name} variant="outlined" size="small" />
                                            </TableCell>
                                            <TableCell>
                                                <Chip label={grades.find(g => g.id === question.grade)?.name} variant="outlined" size="small" />
                                            </TableCell>
                                            <TableCell>
                                                {getDifficultyChip(question.difficulty)}
                                            </TableCell>
                                            <TableCell>
                                                <Typography variant="body2" color="textSecondary">
                                                    {question.tags}
                                                </Typography>
                                            </TableCell>
                                            <TableCell>{question.created_at.split('T')[0]}</TableCell>
                                            <TableCell align="center">
                                                <Tooltip title="Chỉnh sửa">
                                                    <IconButton
                                                        size="small"
                                                        onClick={() => handleOpenDialog(question)}
                                                        color="primary"
                                                    >
                                                        <EditIcon />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Xóa">
                                                    <IconButton
                                                        size="small"
                                                        color="error"
                                                        onClick={() => handleDeleteQuestion(question.id)}
                                                    >
                                                        <DeleteIcon />
                                                    </IconButton>
                                                </Tooltip>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                            </TableBody>
                        </Table>
                    </TableContainer>
                )}

                {/* Pagination */}
                <Box sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    mt: 4,
                    p: 2,
                    backgroundColor: alpha(beeColors.background.paper, 0.5),
                    borderRadius: '12px'
                }}>
                    <TablePagination
                        rowsPerPageOptions={[6, 12, 24]}
                        component="div"
                        count={filteredQuestions.length}
                        rowsPerPage={rowsPerPage}
                        page={page}
                        onPageChange={handleChangePage}
                        onRowsPerPageChange={handleChangeRowsPerPage}
                        labelRowsPerPage="Số hàng mỗi trang:"
                        sx={{
                            '& .MuiTablePagination-toolbar': {
                                paddingLeft: 0,
                                paddingRight: 0
                            }
                        }}
                    />
                </Box>
            </Box>

            {/* Add/Edit Question Dialog */}
            <Dialog
                open={openDialog}
                onClose={handleCloseDialog}
                maxWidth="md"
                fullWidth
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: '16px',
                            boxShadow: `0 20px 60px ${alpha(beeColors.neutral.main, 0.2)}`
                        }
                    }
                }}
            >
                <DialogTitle sx={{
                    background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.1)} 100%)`,
                    borderBottom: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                    p: 3
                }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{
                            bgcolor: beeColors.primary.main,
                            width: 40,
                            height: 40
                        }}>
                            {editingQuestion ? <EditIcon /> : <AddIcon />}
                        </Avatar>
                        <Box>
                            <Typography variant="h6" sx={{
                                fontWeight: 600,
                                color: beeColors.neutral.main
                            }}>
                                {editingQuestion ? 'Chỉnh sửa câu hỏi' : 'Thêm câu hỏi mới'}
                            </Typography>
                            <Typography variant="body2" sx={{
                                color: beeColors.neutral.light
                            }}>
                                {editingQuestion ? 'Cập nhật thông tin câu hỏi' : 'Tạo câu hỏi mới cho ngân hàng'}
                            </Typography>
                        </Box>
                    </Stack>
                </DialogTitle>
                <DialogContent>
                    <Grid container spacing={2} sx={{ mt: 1 }}>
                        <Grid size={{ xs: 12 }}>
                            <CustomTextField
                                fullWidth
                                label="Câu hỏi"
                                multiline
                                rows={3}
                                value={formData.question}
                                onChange={(e) => handleFormChange('question', e.target.value)}
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />
                        </Grid>

                        {formData.options.map((option, index) => (
                            <Grid size={{ xs: 12 }} key={index}>
                                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                                    <FormControlLabel
                                        control={
                                            <Checkbox
                                                checked={formData.correctAnswers.includes(index)}
                                                onChange={(e) => {
                                                    const newCorrectAnswers = [...formData.correctAnswers];
                                                    if (e.target.checked) {
                                                        newCorrectAnswers.push(index);
                                                    } else {
                                                        const idx = newCorrectAnswers.indexOf(index);
                                                        if (idx > -1) newCorrectAnswers.splice(idx, 1);
                                                    }
                                                    setFormData(prev => ({
                                                        ...prev,
                                                        correctAnswers: newCorrectAnswers
                                                    }));
                                                }}
                                            />
                                        }
                                        label={`${String.fromCharCode(65 + index)}.`}
                                        sx={{ minWidth: '50px' }}
                                    />
                                    <CustomTextField
                                        fullWidth
                                        label={`Đáp án ${String.fromCharCode(65 + index)}`}
                                        value={option}
                                        onChange={(e) => handleOptionChange(index, e.target.value)}
                                        color={formData.correctAnswers.includes(index) ? 'success' : 'primary'}
                                    />
                                </Box>
                            </Grid>
                        ))}

                        <Grid size={{ xs: 12 }}>
                            <Typography variant="body2" color="textSecondary">
                                Chọn một hoặc nhiều đáp án đúng bằng cách tick vào checkbox bên trái mỗi đáp án.
                                Đã chọn: {formData.correctAnswers.length} đáp án
                            </Typography>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Môn học</InputLabel>
                                <Select
                                    value={formData.subject}
                                    onChange={(e) => handleFormChange('subject', e.target.value)}
                                    label="Môn học"
                                    sx={{
                                        borderRadius: '12px',
                                        backgroundColor: alpha(beeColors.background.paper, 0.5),
                                        '&:hover': {
                                            backgroundColor: alpha(beeColors.background.paper, 0.8)
                                        },
                                        '&.Mui-focused': {
                                            backgroundColor: beeColors.background.paper
                                        }
                                    }}
                                >
                                    {subjects.map(subject => (
                                        <MenuItem key={subject.id} value={subject.id}>{subject.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Lớp học</InputLabel>
                                <Select
                                    value={formData.grade}
                                    onChange={(e) => handleFormChange('grade', e.target.value)}
                                    label="Lớp học"
                                    sx={{
                                        borderRadius: '12px',
                                        backgroundColor: alpha(beeColors.background.paper, 0.5),
                                        '&:hover': {
                                            backgroundColor: alpha(beeColors.background.paper, 0.8)
                                        },
                                        '&.Mui-focused': {
                                            backgroundColor: beeColors.background.paper
                                        }
                                    }}
                                >
                                    <MenuItem value="">Chọn lớp học</MenuItem>
                                    {grades.map(grade => (
                                        <MenuItem key={grade.id} value={grade.id}>{grade.name}</MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12, sm: 4 }}>
                            <FormControl fullWidth>
                                <InputLabel>Độ khó</InputLabel>
                                <Select
                                    value={formData.difficulty}
                                    onChange={(e) => handleFormChange('difficulty', e.target.value)}
                                    label="Độ khó"
                                    sx={{
                                        borderRadius: '12px',
                                        backgroundColor: alpha(beeColors.background.paper, 0.5),
                                        '&:hover': {
                                            backgroundColor: alpha(beeColors.background.paper, 0.8)
                                        },
                                        '&.Mui-focused': {
                                            backgroundColor: beeColors.background.paper
                                        }
                                    }}
                                >
                                    {difficulties.map(difficulty => (
                                        <MenuItem key={difficulty.value} value={difficulty.value}>
                                            {difficulty.label}
                                        </MenuItem>
                                    ))}
                                </Select>
                            </FormControl>
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <CustomTextField
                                fullWidth
                                label="Giải thích"
                                multiline
                                rows={2}
                                value={formData.explanation}
                                onChange={(e) => handleFormChange('explanation', e.target.value)}
                                sx={{
                                    "& .MuiInputBase-inputMultiline": {
                                        resize: "vertical", // cho phép kéo resize chiều cao
                                    },
                                }}
                            />
                        </Grid>

                        <Grid size={{ xs: 12 }}>
                            <CustomTextField
                                fullWidth
                                label="Tags (phân cách bằng dấu phẩy)"
                                value={formData.tags}
                                onChange={(e) => handleFormChange('tags', e.target.value)}
                                placeholder="ví dụ: toán học, cơ bản, phép cộng"
                            />
                        </Grid>
                    </Grid>
                </DialogContent>
                <DialogActions sx={{
                    backgroundColor: beeColors.background.main,
                    p: 3,
                    gap: 2
                }}>
                    <CustomButton
                        variant='outlined'
                        onClick={handleCloseDialog}
                        sx={{
                            borderColor: alpha(beeColors.inherit.main, 0.3),
                            color: beeColors.inherit.main,
                            '&:hover': {
                                borderColor: beeColors.inherit.main,
                                backgroundColor: alpha(beeColors.inherit.main, 0.9)
                            }
                        }}
                    >
                        Hủy
                    </CustomButton>
                    <CustomButton
                        onClick={handleSaveQuestion}
                        variant="contained"
                        sx={{ borderRadius: "10px" }}
                        startIcon={<AddIcon />}
                    >
                        {editingQuestion ? 'Cập nhật' : 'Thêm'}
                    </CustomButton>
                </DialogActions>
            </Dialog>

            {/* Floating Action Button */}
            <Fab
                color="primary"
                aria-label="add"
                sx={{ position: 'fixed', bottom: 16, right: 16, display: { sm: 'block', md: 'none' } }}
                onClick={() => handleOpenDialog()}
            >
                <AddIcon />
            </Fab>

            {/* Hidden file input for import */}
            <input
                type="file"
                ref={fileInputRef}
                onChange={handleFileSelect}
                accept=".xlsx,.xls"
                style={{ display: 'none' }}
            />

            {/* Help Dialog */}
            <Dialog
                open={helpDialog}
                onClose={() => setHelpDialog(false)}
                maxWidth="md"
                fullWidth
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: '16px',
                            boxShadow: `0 20px 60px ${alpha(beeColors.neutral.main, 0.2)}`
                        }
                    }
                }}
            >
                <DialogTitle sx={{
                    background: `linear-gradient(135deg, ${alpha(beeColors.primary.main, 0.1)} 0%, ${alpha(beeColors.secondary.main, 0.1)} 100%)`,
                    borderBottom: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                    p: 3
                }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{
                            bgcolor: beeColors.primary.main,
                            width: 40,
                            height: 40
                        }}>
                            <HelpIcon />
                        </Avatar>
                        <Box>
                            <Typography variant="h6" sx={{
                                fontWeight: 600,
                                color: beeColors.neutral.main
                            }}>
                                Hướng dẫn Import/Export Excel
                            </Typography>
                            <Typography variant="body2" sx={{
                                color: beeColors.neutral.light
                            }}>
                                Cách sử dụng chức năng nhập và xuất câu hỏi
                            </Typography>
                        </Box>
                    </Stack>
                </DialogTitle>
                <DialogContent sx={{ p: 3 }}>
                    <Stack spacing={3}>
                        <Box>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: beeColors.primary.main }}>
                                📥 Import câu hỏi từ Excel
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 2 }}>
                                1. Nhấn <strong>"Tải Template"</strong> để tải file mẫu<br />
                                2. Điền thông tin câu hỏi vào file Excel theo format<br />
                                3. Nhấn <strong>"Import Excel"</strong> và chọn file đã chuẩn bị<br />
                                4. Xem kết quả và sửa lỗi nếu có
                            </Typography>
                        </Box>

                        <Divider />

                        <Box>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: beeColors.secondary.main }}>
                                📤 Export câu hỏi ra Excel
                            </Typography>
                            <Typography variant="body2" sx={{ mb: 2 }}>
                                1. Sử dụng bộ lọc để chọn câu hỏi cần xuất (tùy chọn)<br />
                                2. Nhấn <strong>"Export Excel"</strong><br />
                                3. File sẽ được tải về máy tính
                            </Typography>
                        </Box>

                        <Divider />

                        <Box>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#4CAF50' }}>
                                📋 Format file Excel
                            </Typography>
                            <Paper sx={{ p: 2, backgroundColor: alpha('#4CAF50', 0.05), borderRadius: '12px' }}>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    <strong>Các cột bắt buộc:</strong>
                                </Typography>
                                <Typography variant="body2" sx={{ mb: 1, fontFamily: 'monospace' }}>
                                    • Số thứ tự | Câu hỏi | Đáp án A | Đáp án B | Đáp án C | Đáp án D | Đáp án đúng | Giải thích
                                </Typography>
                                <Typography variant="body2" sx={{ mt: 2, mb: 1 }}>
                                    <strong>Đáp án đúng:</strong> Sử dụng A, B, C, D. Nhiều đáp án cách nhau bằng dấu phẩy (A, B, D)
                                </Typography>
                            </Paper>
                        </Box>

                        <Box>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600, color: '#F44336' }}>
                                ⚠️ Lưu ý quan trọng
                            </Typography>
                            <Paper sx={{ p: 2, backgroundColor: alpha('#F44336', 0.05), borderRadius: '12px' }}>
                                <Typography variant="body2" sx={{ mb: 1 }}>
                                    • Tất cả các cột bắt buộc không được để trống<br />
                                    • Đáp án đúng chỉ sử dụng A, B, C, D<br />
                                    • Sau khi import, cần cập nhật môn học và lớp học<br />
                                    • Khuyến nghị import tối đa 1000 câu hỏi mỗi lần<br />
                                    • Luôn backup dữ liệu trước khi import số lượng lớn
                                </Typography>
                            </Paper>
                        </Box>
                    </Stack>
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 0 }}>
                    <Button
                        onClick={() => setHelpDialog(false)}
                        variant="contained"
                        sx={{
                            borderRadius: '12px',
                            px: 3,
                            background: beeColors.background.gradient
                        }}
                    >
                        Đã hiểu
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Import Dialog */}
            <Dialog
                open={importDialog}
                onClose={handleCloseImportDialog}
                maxWidth="md"
                fullWidth
                slotProps={{
                    paper: {
                        sx: {
                            borderRadius: '16px',
                            boxShadow: `0 20px 60px ${alpha(beeColors.neutral.main, 0.2)}`
                        }
                    }
                }}
            >
                <DialogTitle sx={{
                    background: `linear-gradient(135deg, ${alpha(beeColors.secondary.main, 0.1)} 0%, ${alpha(beeColors.primary.main, 0.1)} 100%)`,
                    borderBottom: `1px solid ${alpha(beeColors.neutral.main, 0.1)}`,
                    p: 3
                }}>
                    <Stack direction="row" alignItems="center" spacing={2}>
                        <Avatar sx={{
                            bgcolor: beeColors.secondary.main,
                            width: 40,
                            height: 40
                        }}>
                            <FileUploadIcon />
                        </Avatar>
                        <Box>
                            <Typography variant="h6" sx={{
                                fontWeight: 600,
                                color: beeColors.neutral.main
                            }}>
                                Import câu hỏi từ Excel
                            </Typography>
                            <Typography variant="body2" sx={{
                                color: beeColors.neutral.light
                            }}>
                                Đang xử lý file Excel và tạo câu hỏi
                            </Typography>
                        </Box>
                    </Stack>
                </DialogTitle>
                <DialogContent sx={{ p: 3 }}>
                    {importLoading && (
                        <Box sx={{ mb: 3 }}>
                            <Typography variant="body2" sx={{ mb: 1 }}>
                                Đang xử lý... {Math.round(importProgress)}%
                            </Typography>
                            <LinearProgress
                                variant="determinate"
                                value={importProgress}
                                sx={{
                                    height: 8,
                                    borderRadius: 4,
                                    backgroundColor: alpha(beeColors.primary.main, 0.1),
                                    '& .MuiLinearProgress-bar': {
                                        borderRadius: 4,
                                        background: beeColors.background.gradient
                                    }
                                }}
                            />
                        </Box>
                    )}

                    {importResults && (
                        <Box>
                            <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                                Kết quả Import
                            </Typography>

                            <Grid container spacing={2} sx={{ mb: 3 }}>
                                <Grid size={{ xs: 4 }}>
                                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: '12px' }}>
                                        <Typography variant="h4" sx={{
                                            color: beeColors.primary.main,
                                            fontWeight: 700
                                        }}>
                                            {importResults.total}
                                        </Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            Tổng số dòng
                                        </Typography>
                                    </Paper>
                                </Grid>
                                <Grid size={{ xs: 4 }}>
                                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: '12px' }}>
                                        <Typography variant="h4" sx={{
                                            color: '#4CAF50',
                                            fontWeight: 700
                                        }}>
                                            {importResults.success}
                                        </Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            Thành công
                                        </Typography>
                                    </Paper>
                                </Grid>
                                <Grid size={{ xs: 4 }}>
                                    <Paper sx={{ p: 2, textAlign: 'center', borderRadius: '12px' }}>
                                        <Typography variant="h4" sx={{
                                            color: '#F44336',
                                            fontWeight: 700
                                        }}>
                                            {importResults.failed}
                                        </Typography>
                                        <Typography variant="body2" color="textSecondary">
                                            Thất bại
                                        </Typography>
                                    </Paper>
                                </Grid>
                            </Grid>

                            {importResults.errors.length > 0 && (
                                <Box>
                                    <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 600, color: '#F44336' }}>
                                        Lỗi ({importResults.errors.length}):
                                    </Typography>
                                    <Paper sx={{
                                        p: 2,
                                        maxHeight: 200,
                                        overflow: 'auto',
                                        backgroundColor: alpha('#F44336', 0.05),
                                        borderRadius: '12px'
                                    }}>
                                        {importResults.errors.map((error, index) => (
                                            <Typography key={index} variant="body2" sx={{ mb: 0.5 }}>
                                                • {error}
                                            </Typography>
                                        ))}
                                    </Paper>
                                </Box>
                            )}
                        </Box>
                    )}
                </DialogContent>
                <DialogActions sx={{ p: 3, pt: 0 }}>
                    <Button
                        onClick={handleCloseImportDialog}
                        variant="contained"
                        sx={{
                            borderRadius: '12px',
                            px: 3,
                            background: beeColors.background.gradient
                        }}
                    >
                        Đóng
                    </Button>
                </DialogActions>
            </Dialog>

            {/* Snackbar */}
            <Snackbar
                open={snackbar.open}
                autoHideDuration={6000}
                onClose={() => setSnackbar({ ...snackbar, open: false })}
            >
                <Alert
                    onClose={() => setSnackbar({ ...snackbar, open: false })}
                    severity={snackbar.severity}
                    sx={{ width: '100%' }}
                >
                    {snackbar.message}
                </Alert>
            </Snackbar>
        </Box>
    );
}

export default QuestionBank;
