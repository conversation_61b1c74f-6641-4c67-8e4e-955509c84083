import React from "react";
import { useParams } from "react-router-dom";
import {
    Avatar,
    Box,
    Button,
    Chip,
    Divider,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from "@mui/material";
import { blue, deepOrange, green, grey, orange, red } from "@mui/material/colors";
import PrintIcon from "@mui/icons-material/Print";
import Grid from "@mui/material/Grid2";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import axiosInstance from "../../services/axiosInstance";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import ArrowCircleRightIcon from "@mui/icons-material/ArrowCircleRight";
import Loading from "../Common/Loading";
import { useRef } from "react";
import { useReactToPrint } from "react-to-print";
import CustomerLayout from "../Common/CustomerLayout";
import Invoice from "./Invoice";

function getDate(date) {
    if (!date) return "";
    const dateObject = new Date(date);
    return dateObject.toISOString().split("T")[0];
}

function getTime(date) {
    if (!date) return "";
    const timePart = date.split("T")[1];
    const [hours, minutes] = timePart.split(":");

    let hours12 = parseInt(hours, 10);
    const period = hours12 >= 12 ? "PM" : "AM";

    hours12 = hours12 % 12 || 12;

    return `${hours12}:${minutes} ${period}`;
}

function formatPrice(number) {
    if (!number) number = 0;
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function VerticalLinearStepper({ cart, shipment, setShipment, setOrderStatusSeletected }) {
    const [activeStep, setActiveStep] = React.useState(0);
    React.useEffect(() => {
        if (shipment.history["Xác nhận thành công"] != null) setActiveStep(1);
        if (shipment.history["Đang chuẩn bị hàng"] != null) setActiveStep(2);
        if (shipment.history["Đã giao bên vận chuyển"] != null) setActiveStep(3);
        if (shipment.history["Đã giao đến khách"] != null) setActiveStep(4);
        if (shipment.history["Vận chuyển thành công"] != null) setActiveStep(5);
    }, []);

    const CustomStepIcon = (props) => {
        const { active, completed, className } = props;

        if (active)
            return (
                <ArrowCircleRightIcon
                    className={className}
                    style={{ color: completed ? green[600] : active ? red[600] : grey[300] }}
                />
            );
        else
            return (
                <CheckCircleIcon
                    className={className}
                    style={{ color: completed ? green[600] : active ? red[600] : grey[300] }}
                />
            );
    };

    return (
        <Box sx={{ maxWidth: 400 }}>
            <Stepper activeStep={activeStep} orientation="vertical">
                {Object.entries(shipment.history).map(([activity, date], index) => (
                    <Step key={index}>
                        <StepLabel
                            StepIconComponent={CustomStepIcon}
                            optional={
                                date != null && (
                                    <Typography variant="caption">{`${getDate(date)} ${getTime(date)}`}</Typography>
                                )
                            }
                        >
                            {activity}
                        </StepLabel>
                    </Step>
                ))}
            </Stepper>
        </Box>
    );
}

function CartView({ user, setUser, cartItem, setCartItem }) {
    const { order_id } = useParams();
    const componentRef = useRef();

    const handlePrint = useReactToPrint({
        content: () => componentRef.current,
    });

    const [cart, setCart] = React.useState({});
    const [cartDetail, setCartDetail] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [shipment, setShipment] = React.useState({
        vendor: "",
        speedy: "",
        link: "",
        history: {
            "Xác nhận thành công": "",
            "Đang chuẩn bị hàng": "",
            "Đã giao bên vận chuyển": "",
            "Đã giao đến khách": "",
            "Vận chuyển thành công": "",
        },
    });
    const shipPrice = 35000;

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const responseCart = await axiosInstance.get(`/api/shopping/cart/view/${order_id}`);
                setCart(responseCart.data);

                const responseCartDetail = await axiosInstance.get(`/api/shopping/cart/detail/view?filter=${order_id}`);
                setCartDetail(responseCartDetail.data);

                setShipment({
                    ...shipment,
                    vendor: responseCart.data.ship_vendor,
                    speedy: responseCart.data.ship_type,
                    link: responseCart.data.ship_code,
                    history: {
                        ...shipment.history,
                        "Xác nhận thành công": responseCart.data.ship_confirm,
                        "Đang chuẩn bị hàng": responseCart.data.ship_preparing,
                        "Đã giao bên vận chuyển": responseCart.data.ship_to_vendor,
                        "Đã giao đến khách": responseCart.data.ship_to_customer,
                        "Vận chuyển thành công": responseCart.data.ship_success,
                    },
                });
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    const order_color = {
        "Đã giao hàng": { backgroundColor: green[100], color: green[900] },
        "Hủy đơn": { backgroundColor: red[100], color: red[900] },
        "Xuất kho": { backgroundColor: blue[100], color: blue[900] },
        "Hoàn đơn": { backgroundColor: grey[100], color: grey[900] },
        "Chờ xác nhận": { backgroundColor: orange[100], color: orange[900] },
    };

    const [orderStatusSeletected, setOrderStatusSeletected] = React.useState(cart.status);

    const title_style = { fontWeight: 500 };

    if (loading) return <Loading />;

    function get_total() {
        let sum = 0;
        cartDetail.forEach((cart_detail) => {
            sum += cart_detail.quantity * cart_detail.sale_price;
        });
        return formatPrice(sum);
    }

    function get_discount() {
        let sum = 0;
        cartDetail.forEach((cart_detail) => {
            sum += cart_detail.quantity * (cart_detail.sale_price - cart_detail.price);
        });

        return formatPrice(sum);
    }

    function get_total_after_sale() {
        let sum = 0;
        cartDetail.forEach((cart_detail) => {
            sum += cart_detail.price * cart_detail.quantity;
        });
        return formatPrice(sum + shipPrice);
    }

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            <style>
                {`
                    .hide-on-screen {
                        display: none;
                    }

                    @media print {
                        .hide-on-screen {
                        display: block !important;
                        }
                    }
                `}
            </style>
            <div ref={componentRef} className="hide-on-screen">
                <Invoice cart={cart} cartDetail={cartDetail} />
            </div>
            <Grid container spacing={2} sx={{ mt: "30px" }}>
                <Grid size={{ xs: 12, md: 8 }}>
                    <Paper sx={{ borderRadius: "20px" }}>
                        <Box sx={{ padding: "30px" }}>
                            <Box sx={{ display: "flex", mb: "20px" }}>
                                <Typography sx={{ fontSize: "20px", mr: "20px", ...title_style }}>
                                    Đơn hàng: #{cart.short_id}
                                </Typography>
                                <Chip
                                    label={cart.status}
                                    variant="outlined"
                                    sx={{
                                        color: order_color[cart.status].color,
                                        backgroundColor: order_color[cart.status].backgroundColor,
                                        borderColor: order_color[cart.status].color,
                                    }}
                                />
                            </Box>
                            {cartDetail.map((item) => (
                                <Grid container spacing={2} key={item.id} sx={{ mb: "20px" }}>
                                    {/* sx = {{ display: "flex", width: "100%", mb: "20px", justifyContent: "space-between" }} key={item.id}> */}
                                    <Grid size={{ xs: 12, md: 8 }}>
                                        <Box sx={{ display: "flex" }}>
                                            <img
                                                alt={item.item.name}
                                                src={item.item.images.length > 0 ? item.item.images[0].image_url : null}
                                                style={{
                                                    width: "64px",
                                                    height: "64px",
                                                    borderRadius: "10px",
                                                    border: "1px solid #ebebeb",
                                                    marginRight: "10px",
                                                    objectFit: "contain",
                                                }}
                                            />
                                            <Box sx={{ display: "flex", flexDirection: "column" }}>
                                                <Typography>
                                                    <a
                                                        style={{ color: "inherit", textDecoration: "none" }}
                                                        href={"/product/item/" + item.item.sku}
                                                        target="_blank"
                                                    >
                                                        {item.item.name}
                                                    </a>
                                                </Typography>
                                                <Typography sx={{ color: grey[600], fontSize: "12px", mb: "5px" }}>
                                                    {"#" + item.item.sku}
                                                </Typography>
                                                {item.item.attributes.map((attr, index) => (
                                                    <Typography key={index} sx={{ color: grey[600], fontSize: "12px" }}>
                                                        {attr.key}: {attr.value}
                                                    </Typography>
                                                ))}
                                                <Box sx={{ display: "flex", alignItems: "baseline" }}>
                                                    <Typography>{formatPrice(item.price)}</Typography>
                                                    {item.price != item.sale_price && (
                                                        <Typography
                                                            sx={{ ml: "5px", fontSize: "13px", color: grey[600] }}
                                                        >
                                                            <del>{formatPrice(item.sale_price)}</del>
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </Box>
                                        </Box>
                                    </Grid>
                                    <Grid size={{ xs: 2, md: 2 }}>
                                        <Typography align="right">x{item.quantity}</Typography>
                                    </Grid>
                                    <Grid size={{ xs: 2, md: 2 }} align="right">
                                        <Typography sx={{ fontWeight: "bold" }}>
                                            {formatPrice(item.quantity * item.price)}
                                        </Typography>
                                    </Grid>
                                </Grid>
                            ))}
                            <Divider sx={{ mb: "30px", width: "100%" }} />
                            <Grid container spacing={2} sx={{ mb: "10px" }}>
                                <Grid size={{ xs: 6, md: 9 }} align="right">
                                    <Typography sx={{ color: grey[600] }}>Tổng tiền:</Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }} align="right">
                                    <Typography sx={{ fontWeight: "bold" }}>{get_total()}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={2} sx={{ mb: "10px" }}>
                                <Grid size={{ xs: 6, md: 9 }} align="right">
                                    <Typography sx={{ color: grey[600] }}>Vận chuyển:</Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }} align="right">
                                    <Typography>{formatPrice(shipPrice)}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={2} sx={{ mb: "10px" }}>
                                <Grid size={{ xs: 6, md: 9 }} align="right">
                                    <Typography sx={{ color: grey[600] }}>Giảm giá:</Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }} align="right">
                                    <Typography color="error">{get_discount()}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={2}>
                                <Grid size={{ xs: 6, md: 9 }} align="right" alignContent="center">
                                    <Typography>Thành tiền:</Typography>
                                </Grid>
                                <Grid size={{ xs: 6, md: 3 }} align="right" alignContent="center">
                                    <Typography sx={{ ...title_style, fontSize: "25px" }}>
                                        {get_total_after_sale()}
                                    </Typography>
                                </Grid>
                            </Grid>
                        </Box>
                    </Paper>
                    <Paper sx={{ borderRadius: "20px", display: { xs: "flex", md: "none" }, mt: 2 }}>
                        <Box sx={{ padding: "30px" }}>
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Khách hàng</Typography>
                            <Box sx={{ display: "flex" }}>
                                <Avatar sx={{ bgcolor: deepOrange[900], color: "white", mr: "10px" }}>D</Avatar>
                                <Box>
                                    <Typography sx={{ ...title_style }}>{cart.customer_name}</Typography>
                                    <a
                                        href={`mailto:${cart.customer_email}`}
                                        target="_blank"
                                        style={{ textDecoration: "none" }}
                                    >
                                        <Typography sx={{ color: grey[600], fontSize: "14px", mb: "10px" }}>
                                            {cart.customer_email}
                                        </Typography>
                                    </a>
                                </Box>
                            </Box>
                            <Divider sx={{ mb: "20px", mt: "20px" }} />
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Liên hệ</Typography>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Địa chỉ</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{`${cart.customer_address}, ${cart.customer_ward}, ${cart.customer_province}`}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Điện thoại</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{cart.customer_phone}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Ghi chú</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{cart.customer_note}</Typography>
                                </Grid>
                            </Grid>
                            <Divider sx={{ mt: "20px", mb: "20px" }} />
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Thanh toán</Typography>
                            <Grid container spacing={1} sx={{ mb: "10px" }}>
                                <Grid size={6}>
                                    <Chip
                                        label={cart.paid ? "Đã thanh toán" : "Chưa thanh toán"}
                                        variant="outlined"
                                        color={cart.paid ? "success" : "error"}
                                    />
                                </Grid>
                                <Grid size={6} sx={{ display: "flex", alignItems: "center" }}>
                                    {cart.paid ? (
                                        <Typography>{`${cart.payment_method} (#${cart.paid_code})`}</Typography>
                                    ) : (
                                        <Typography>{cart.payment_method}</Typography>
                                    )}
                                </Grid>
                            </Grid>
                            {cart.paid && (
                                <Button
                                    variant="contained"
                                    sx={{ borderRadius: "10px" }}
                                    color="warning"
                                    startIcon={<PrintIcon />}
                                    fullWidth
                                    onClick={handlePrint}
                                >
                                    In hóa đơn
                                </Button>
                            )}
                        </Box>
                    </Paper>
                    {cart.paid && (
                        <Paper sx={{ borderRadius: "20px", mb: "20px", mt: 2 }}>
                            <Box sx={{ width: "100%" }}>
                                <Box sx={{ padding: "30px" }}>
                                    <Box sx={{ display: "flex", alignItems: "center", mb: "20px" }}>
                                        <Typography sx={{ fontSize: "20px", mr: "10px", ...title_style }}>
                                            Vận chuyển
                                        </Typography>
                                    </Box>
                                    <Grid container spacing={2}>
                                        <Grid size={{ xs: 6, md: 4 }} sx={{ color: grey[600] }}>
                                            <Typography>Đv vận chuyển</Typography>
                                        </Grid>
                                        <Grid size={{ xs: 6, md: 8 }}>
                                            <Typography>{shipment.vendor}</Typography>
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={2}>
                                        <Grid size={{ xs: 6, md: 4 }} sx={{ color: grey[600] }}>
                                            <Typography>Gói vận chuyển</Typography>
                                        </Grid>
                                        <Grid size={{ xs: 6, md: 8 }}>
                                            <Typography>{shipment.speedy}</Typography>
                                        </Grid>
                                    </Grid>
                                    <Grid container spacing={2}>
                                        <Grid size={{ xs: 6, md: 4 }} sx={{ color: grey[600] }}>
                                            <Typography>Mã vận chuyển</Typography>
                                        </Grid>
                                        <Grid size={{ xs: 6, md: 8 }}>
                                            <Typography>{shipment.link}</Typography>
                                        </Grid>
                                    </Grid>
                                    <Box sx={{ width: "100%" }}>
                                        <Divider sx={{ mb: "30px", mt: "30px", mr: "60px" }} />
                                    </Box>
                                    <VerticalLinearStepper
                                        cart={cart}
                                        shipment={shipment}
                                        setShipment={setShipment}
                                        setOrderStatusSeletected={setOrderStatusSeletected}
                                    />
                                </Box>
                            </Box>
                        </Paper>
                    )}
                </Grid>
                <Grid size={{ xs: 12, md: 4 }}>
                    <Paper sx={{ borderRadius: "20px", display: { xs: "none", md: "flex" } }}>
                        <Box sx={{ padding: "30px" }}>
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Khách hàng</Typography>
                            <Box sx={{ display: "flex" }}>
                                <Avatar sx={{ bgcolor: deepOrange[900], color: "white", mr: "10px" }}>D</Avatar>
                                <Box>
                                    <Typography sx={{ ...title_style }}>{cart.customer_name}</Typography>
                                    <a
                                        href={`mailto:${cart.customer_email}`}
                                        target="_blank"
                                        style={{ textDecoration: "none" }}
                                    >
                                        <Typography sx={{ color: grey[600], fontSize: "14px", mb: "10px" }}>
                                            {cart.customer_email}
                                        </Typography>
                                    </a>
                                </Box>
                            </Box>
                            <Divider sx={{ mb: "20px", mt: "20px" }} />
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Liên hệ</Typography>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Địa chỉ</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{`${cart.customer_address}, ${cart.customer_ward}, ${cart.customer_province}`}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Điện thoại</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{cart.customer_phone}</Typography>
                                </Grid>
                            </Grid>
                            <Grid container spacing={1}>
                                <Grid size={4}>
                                    <Typography sx={{ color: grey[600] }}>Ghi chú</Typography>
                                </Grid>
                                <Grid size={8}>
                                    <Typography>{cart.customer_note}</Typography>
                                </Grid>
                            </Grid>
                            <Divider sx={{ mt: "20px", mb: "20px" }} />
                            <Typography sx={{ fontSize: "20px", mb: "20px", ...title_style }}>Thanh toán</Typography>
                            <Grid container spacing={1} sx={{ mb: "10px" }}>
                                <Grid size={6}>
                                    <Chip
                                        label={cart.paid ? "Đã thanh toán" : "Chưa thanh toán"}
                                        variant="outlined"
                                        color={cart.paid ? "success" : "error"}
                                    />
                                </Grid>
                                <Grid size={6} sx={{ display: "flex", alignItems: "center" }}>
                                    {cart.paid ? (
                                        <Typography>{`${cart.payment_method} (#${cart.paid_code})`}</Typography>
                                    ) : (
                                        <Typography>{cart.payment_method}</Typography>
                                    )}
                                </Grid>
                            </Grid>
                            {cart.paid && (
                                <Button
                                    variant="contained"
                                    sx={{ borderRadius: "10px" }}
                                    color="warning"
                                    startIcon={<PrintIcon />}
                                    fullWidth
                                    onClick={handlePrint}
                                >
                                    In hóa đơn
                                </Button>
                            )}
                        </Box>
                    </Paper>
                </Grid>
            </Grid>
        </CustomerLayout>
    );
}

export default CartView;
