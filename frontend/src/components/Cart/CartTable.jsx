import * as React from "react";
import PropTypes from "prop-types";
import Box from "@mui/material/Box";
import Collapse from "@mui/material/Collapse";
import IconButton from "@mui/material/IconButton";
import Table from "@mui/material/Table";
import TableBody from "@mui/material/TableBody";
import TableCell from "@mui/material/TableCell";
import TableContainer from "@mui/material/TableContainer";
import TableHead from "@mui/material/TableHead";
import TableRow from "@mui/material/TableRow";
import Paper from "@mui/material/Paper";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";
import KeyboardArrowUpIcon from "@mui/icons-material/KeyboardArrowUp";
import { deepPurple, green, grey, orange, red } from "@mui/material/colors";
import { Avatar, Chip, Tab, TablePagination, Typography } from "@mui/material";

function Row({ row, index }) {
    const [open, setOpen] = React.useState(false);

    const order_color = {
        "Đã giao hàng": { backgroundColor: green[100], color: green[900] },
        "Hủy đơn": { backgroundColor: red[100], color: red[900] },
        "Hoàn đơn": { backgroundColor: grey[100], color: grey[900] },
        "Chờ xác nhận": { backgroundColor: orange[100], color: orange[900] },
    };

    function formatPrice(number) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "VND",
        }).format(number);
    }

    function getDate(date) {
        const dateObject = new Date(date);
        return dateObject.toISOString().split("T")[0];
    }

    function getTime(date) {
        const timePart = date.split("T")[1];
        const [hours, minutes] = timePart.split(":");

        let hours12 = parseInt(hours, 10);
        const period = hours12 >= 12 ? "PM" : "AM";

        hours12 = hours12 % 12 || 12;

        return `${hours12}:${minutes} ${period}`;
    }

    return (
        <React.Fragment>
            <TableRow sx={{ "& > *": { borderBottom: "unset" } }}>
                <TableCell component="th" scope="row">
                    <Typography>{index + 1}</Typography>
                </TableCell>
                <TableCell component="th" scope="row">
                    <a
                        style={{ color: "inherit", textDecoration: "underline" }}
                        href={"/cart/" + row.id}
                        target="_blank"
                    >
                        {"#" + row.order}
                    </a>
                </TableCell>
                <TableCell align="left">
                    <Box sx={{ display: "flex", width: "100%" }}>
                        <Avatar
                            sx={{ bgcolor: deepPurple[600], color: "white", mr: "10px", width: "50px", height: "50px" }}
                        >
                            {row.customer.name[0]}
                        </Avatar>
                        <Box>
                            <Typography>{row.customer.name}</Typography>
                            <Typography sx={{ color: grey[600], fontSize: "13px" }}>{row.customer.phone}</Typography>
                        </Box>
                    </Box>
                </TableCell>
                <TableCell align="left">
                    <Box>
                        <Typography sx={{ fontSize: "14px" }}>{getDate(row.date)}</Typography>
                        <Typography sx={{ fontSize: "12px", color: grey[600] }}>{getTime(row.date)}</Typography>
                    </Box>
                </TableCell>
                <TableCell align="center">{row.cart.length}</TableCell>
                <TableCell align="right">
                    <Typography sx={{ fontWeight: 700 }}>
                        {formatPrice(row.price - row.coupon_discount + row.ship_fee)}
                    </Typography>
                </TableCell>
                <TableCell align="center">
                    <Chip label={row.order_status} sx={{ ...order_color[row.order_status] }}></Chip>
                </TableCell>
                <TableCell>
                    <IconButton aria-label="expand row" size="small" onClick={() => setOpen(!open)}>
                        {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
                    </IconButton>
                </TableCell>
            </TableRow>
            <TableRow>
                <TableCell style={{ paddingBottom: 0, paddingTop: 0, backgroundColor: grey[100] }} colSpan={12}>
                    <Collapse in={open} timeout="auto" unmountOnExit>
                        <Box sx={{ margin: 1 }}>
                            <Table aria-label="purchases" sx={{ backgroundColor: "white", mb: 4, mt: 4 }}>
                                <TableHead>
                                    <TableRow>
                                        <TableCell>Sản phẩm</TableCell>
                                        <TableCell align="right">Số Lượng</TableCell>
                                        <TableCell align="right">Đơn Giá</TableCell>
                                        <TableCell align="right">Giảm Giá</TableCell>
                                        <TableCell align="right">Thành Tiền</TableCell>
                                    </TableRow>
                                </TableHead>
                                <TableBody>
                                    {row.cart.map((item) => (
                                        <TableRow key={item.id}>
                                            <TableCell
                                                component="th"
                                                scope="row"
                                                sx={{
                                                    maxWidth: "200px",
                                                    whiteSpace: "nowrap",
                                                    overflow: "hidden",
                                                    textOverflow: "ellipsis",
                                                }}
                                            >
                                                <Box sx={{ display: "flex", width: "100%" }}>
                                                    <img
                                                        alt={item.name}
                                                        src={item.image}
                                                        style={{
                                                            width: "56px",
                                                            height: "56px",
                                                            borderRadius: "10px",
                                                            marginRight: "10px",
                                                            objectFit: "contain",
                                                        }}
                                                    />
                                                    <Box sx={{ display: "flex", flexDirection: "column" }}>
                                                        <Typography>
                                                            <a
                                                                style={{ color: "inherit", textDecoration: "none" }}
                                                                href={"/product/item/" + item.id}
                                                                target="_blank"
                                                            >
                                                                {item.name}
                                                            </a>
                                                        </Typography>
                                                        <Typography sx={{ color: grey[500], fontSize: 14 }}>
                                                            #{item.id}
                                                        </Typography>
                                                        {item.attributes.map((attr, index) => (
                                                            <Typography
                                                                key={index}
                                                                sx={{ color: grey[500], fontSize: 14 }}
                                                            >
                                                                {attr.key}: {attr.value}
                                                            </Typography>
                                                        ))}
                                                    </Box>
                                                </Box>
                                            </TableCell>
                                            <TableCell align="right">x {item.quantity}</TableCell>
                                            <TableCell align="right">
                                                <Typography>
                                                    {formatPrice(Math.round(item.sale_price * 100) / 100)}
                                                </Typography>
                                            </TableCell>
                                            <TableCell align="right">
                                                <Typography>
                                                    -{formatPrice(Math.round(item.sale_price - item.price))}
                                                </Typography>
                                            </TableCell>
                                            <TableCell align="right">
                                                <Typography>
                                                    {formatPrice(Math.round(item.quantity * item.price * 100) / 100)}
                                                </Typography>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                    {row.ship_fee > 0 && (
                                        <TableRow>
                                            <TableCell component="th" scope="row">
                                                <Typography sx={{ mb: "10px", color: grey[600] }}>
                                                    Vận chuyển
                                                </Typography>
                                            </TableCell>
                                            <TableCell align="right" colSpan={4}>
                                                <Typography>{formatPrice(row.ship_fee)}</Typography>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                    {row.coupon_discount > 0 && (
                                        <TableRow>
                                            <TableCell component="th" scope="row">
                                                <Typography sx={{ mb: "10px", color: grey[600] }}>
                                                    Mã giảm giá
                                                </Typography>
                                            </TableCell>
                                            <TableCell align="right" colSpan={3}>
                                                <Chip label={row.coupon_code} color="error" variant="outlined"></Chip>
                                            </TableCell>
                                            <TableCell align="right">
                                                <Typography>-{formatPrice(row.coupon_discount)}</Typography>
                                            </TableCell>
                                        </TableRow>
                                    )}
                                </TableBody>
                            </Table>
                        </Box>
                    </Collapse>
                </TableCell>
            </TableRow>
        </React.Fragment>
    );
}

Row.propTypes = {
    index: PropTypes.number.isRequired,
    row: PropTypes.shape({
        id: PropTypes.string.isRequired,
        order: PropTypes.string.isRequired,
        customer: PropTypes.shape({
            name: PropTypes.string.isRequired,
            phone: PropTypes.string.isRequired,
        }).isRequired,
        date: PropTypes.string.isRequired,
        cart: PropTypes.arrayOf(
            PropTypes.shape({
                id: PropTypes.string.isRequired,
                name: PropTypes.string.isRequired,
                image: PropTypes.string.isRequired,
                quantity: PropTypes.number.isRequired,
                price: PropTypes.number.isRequired,
                attributes: PropTypes.array.isRequired,
            })
        ).isRequired,
        // items: PropTypes.number.isRequired,
        price: PropTypes.number.isRequired,
        order_status: PropTypes.string.isRequired,
    }).isRequired,
};

export default function CartTable({ rows }) {
    const [rowsPerPage, setRowsPerPage] = React.useState(10);
    const [page, setPage] = React.useState(0);

    const handleChangePage = (event, newPage) => {
        setPage(newPage);
    };

    const handleChangeRowsPerPage = (event) => {
        setRowsPerPage(parseInt(event.target.value, 10));
        setPage(0);
    };

    // const emptyRows =
    //     page > 0 ? Math.max(0, (1 + page) * rowsPerPage - rows.length) : 0;

    const visibleRows = React.useMemo(
        () => rows.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage),
        [rows, page, rowsPerPage]
    );

    return (
        <>
            <TableContainer component={Paper} elevation={0} sx={{ mt: "10px" }}>
                <Table aria-label="collapsible table">
                    <TableHead>
                        <TableRow>
                            <TableCell align="left">#</TableCell>
                            <TableCell align="left">Đơn hàng</TableCell>
                            <TableCell align="left">Khách hàng</TableCell>
                            <TableCell align="left">Ngày</TableCell>
                            <TableCell align="center">Sản phẩm</TableCell>
                            <TableCell align="right">Tổng tiền</TableCell>
                            <TableCell align="center">Trạng thái</TableCell>
                            <TableCell />
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {visibleRows.map((row, index) => (
                            <Row key={index} row={row} index={index} />
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            <TablePagination
                rowsPerPageOptions={[10, 25, 50]}
                component="div"
                count={rows.length}
                rowsPerPage={rowsPerPage}
                page={page}
                onPageChange={handleChangePage}
                onRowsPerPageChange={handleChangeRowsPerPage}
                labelRowsPerPage={"Hiển thị:"}
            />
        </>
    );
}
