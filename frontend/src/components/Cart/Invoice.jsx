import {
    Box,
    Divider,
    Paper,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Typography,
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import React from "react";
import EmailIcon from "@mui/icons-material/Email";
import FacebookIcon from "@mui/icons-material/Facebook";
import { grey } from "@mui/material/colors";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

function getDate(date) {
    if (!date) return "";
    const dateObject = new Date(date);
    return dateObject.toISOString().split("T")[0];
}

function getTime(date) {
    if (!date) return "";
    const timePart = date.split("T")[1];
    const [hours, minutes] = timePart.split(":");

    let hours12 = parseInt(hours, 10);
    const period = hours12 >= 12 ? "PM" : "AM";

    hours12 = hours12 % 12 || 12;

    return `${hours12}:${minutes} ${period}`;
}

function formatPrice(number) {
    if (!number) number = 0;
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function Invoice({ cart, cartDetail }) {
    useDocumentTitle(`Hóa đơn - #${cart.short_id}`);
    const currentDate = new Date();
    const utc7Date = new Date(currentDate.getTime() + 7 * 60 * 60 * 1000);
    const formattedDate = utc7Date.toISOString();

    const today = `${getDate(formattedDate)} ${getTime(formattedDate)}`;

    const [totalPrice, setTotalPrice] = React.useState(0);
    const [discount, setDiscount] = React.useState(0);
    const [totalPriceAfterSale, setTotalPriceAfterSale] = React.useState(0);

    React.useEffect(() => {
        function calculateTotals() {
            let total = 0;
            let totalDiscount = 0;
            let totalAfterSale = 0;

            cartDetail.forEach((item) => {
                total += item.sale_price * item.quantity;
                totalDiscount += (item.sale_price - item.price) * item.quantity;
                totalAfterSale += item.price * item.quantity;
            });

            setTotalPrice(total);
            setDiscount(totalDiscount);
            setTotalPriceAfterSale(totalAfterSale - cart.coupon_discount + cart.ship_fee);
        }

        if (cartDetail.length > 0) {
            calculateTotals();
        }
    }, [cartDetail]);

    return (
        <div style={{ padding: "40px" }}>
            <Box sx={{ display: "flex", alignItems: "center" }}>
                <img src="/beeIco.svg" width="64px" height="64px" alt="beeblock" style={{ marginRight: "5px" }} />
                <Box>
                    <Typography>BeeBlock</Typography>
                    <Box sx={{ display: "flex" }}>
                        <EmailIcon
                            sx={{
                                mr: "5px",
                                fontSize: "18px",
                                color: grey[600],
                            }}
                        />
                        <Typography sx={{ display: "flex", fontSize: "12px" }}><EMAIL></Typography>
                    </Box>
                    <Box sx={{ display: "flex" }}>
                        <FacebookIcon
                            sx={{
                                mr: "5px",
                                fontSize: "18px",
                                color: grey[600],
                            }}
                        />
                        <Typography sx={{ display: "flex", fontSize: "12px" }}>https://fb.com/beeblock-vn</Typography>
                    </Box>
                </Box>
            </Box>
            <Divider sx={{ mt: "5px", mb: "20px" }} />
            <Typography variant="h4" gutterBottom align="center">
                Hóa đơn bán hàng
            </Typography>
            <Typography variant="h6" sx={{ fontSize: "12px", color: grey[600] }} gutterBottom align="center">
                {today}
            </Typography>
            <Grid container spacing={2}>
                <Grid size={2}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        Mã hóa đơn:
                    </Typography>
                </Grid>
                <Grid size={10}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        #{cart.short_id}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={2}>
                <Grid size={2}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        Khách hàng:
                    </Typography>
                </Grid>
                <Grid size={10}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        {cart.customer_name}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={2}>
                <Grid size={2}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        Số điện thoại:
                    </Typography>
                </Grid>
                <Grid size={10}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        {cart.customer_phone}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={2}>
                <Grid size={2}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        Địa chỉ:
                    </Typography>
                </Grid>
                <Grid size={10}>
                    <Typography variant="subtitle1" sx={{ fontSize: "13px" }}>
                        {cart.customer_address}, {cart.customer_ward}, {cart.customer_province}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={2} style={{ marginTop: 20 }}>
                <Grid size={6}>
                    <Typography variant="subtitle2" sx={{ fontSize: "13px" }}>
                        Phương thức thanh toán: {cart.payment_method} ({cart.paid_code})
                    </Typography>
                </Grid>
                {/* <Grid size={6}>
                    <Typography variant="subtitle2">
                        Tổng tiền: {formatPrice(cart.total_price)}
                    </Typography>
                </Grid> */}
            </Grid>

            <TableContainer component={Paper} style={{ marginTop: 20, borderRadius: "10px" }} elevation={0}>
                <Table>
                    <TableHead>
                        <TableRow>
                            <TableCell>Sản phẩm</TableCell>
                            <TableCell align="right">Đơn giá</TableCell>
                            <TableCell align="right">Số lượng</TableCell>
                            <TableCell align="right">Thành tiền</TableCell>
                        </TableRow>
                    </TableHead>
                    <TableBody>
                        {cartDetail.map((detail) => (
                            <TableRow key={detail.id}>
                                <TableCell>
                                    <Box sx={{ display: "flex" }}>
                                        <img
                                            alt={detail.item.name}
                                            src={detail.item.images.length > 0 ? detail.item.images[0].image_url : null}
                                            style={{
                                                width: "64px",
                                                height: "64px",
                                                borderRadius: "10px",
                                                border: "1px solid #ebebeb",
                                                marginRight: "10px",
                                                objectFit: "contain",
                                            }}
                                        />
                                        <Box
                                            sx={{
                                                display: "flex",
                                                flexDirection: "column",
                                            }}
                                        >
                                            <Typography>
                                                <a
                                                    style={{
                                                        color: "inherit",
                                                        textDecoration: "none",
                                                    }}
                                                    href={"/product/item/" + detail.item.sku}
                                                    target="_blank"
                                                >
                                                    {detail.item.name}
                                                </a>
                                            </Typography>
                                            <Typography
                                                sx={{
                                                    color: grey[600],
                                                    fontSize: "12px",
                                                }}
                                            >
                                                {"#" + detail.item.sku}
                                            </Typography>
                                            {detail.item.attributes.map((attr, index) => (
                                                <Typography
                                                    key={index}
                                                    sx={{
                                                        color: grey[600],
                                                        fontSize: "12px",
                                                    }}
                                                >
                                                    {attr.key}: {attr.value}
                                                </Typography>
                                            ))}
                                        </Box>
                                    </Box>
                                    {/* {detail.item.name} */}
                                </TableCell>
                                <TableCell align="right">
                                    <Typography>{formatPrice(detail.price)}</Typography>
                                    {detail.sale_price != detail.price && (
                                        <Typography>
                                            <del>{formatPrice(detail.sale_price)}</del>
                                        </Typography>
                                    )}
                                </TableCell>
                                <TableCell align="right">
                                    <Typography>x{detail.quantity}</Typography>
                                </TableCell>
                                <TableCell align="right">
                                    <Typography>{formatPrice(detail.price * detail.quantity)}</Typography>
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </TableContainer>
            <Grid container spacing={2}>
                <Grid size={10}>
                    <Typography align="right" style={{ marginTop: "20px" }}>
                        Tổng tiền:
                    </Typography>
                    <Typography align="right" style={{ marginTop: "10px" }}>
                        Giảm tiền:
                    </Typography>
                    {cart.coupon_discount > 0 && (
                        <Typography align="right" style={{ marginTop: "10px" }}>
                            Mã giảm giá:
                        </Typography>
                    )}
                    <Typography align="right" style={{ marginTop: "10px" }}>
                        Vận chuyển:
                    </Typography>
                    <Typography variant="h6" align="right" style={{ marginTop: "15px" }}>
                        Thành tiền:
                    </Typography>
                </Grid>
                <Grid size={2}>
                    <Typography align="right" style={{ marginTop: "20px" }}>
                        {formatPrice(totalPrice)}
                    </Typography>
                    <Typography align="right" style={{ marginTop: "10px" }}>
                        -{formatPrice(discount)}
                    </Typography>
                    {cart.coupon_discount > 0 && (
                        <Typography align="right" style={{ marginTop: "10px" }}>
                            -{formatPrice(cart.coupon_discount)}
                        </Typography>
                    )}
                    <Typography align="right" style={{ marginTop: "10px" }}>
                        {formatPrice(cart.ship_fee)}
                    </Typography>
                    <Typography variant="h6" align="right" style={{ marginTop: "15px" }}>
                        {formatPrice(totalPriceAfterSale)}
                    </Typography>
                </Grid>
            </Grid>
            <Grid container spacing={2} sx={{ mt: "20px" }}>
                <Grid size={8}>
                    <Box
                        sx={{
                            display: "flex",
                            justifyContent: "center",
                            flexDirection: "column",
                            width: "100%",
                        }}
                    >
                        <Typography sx={{ fontWeight: "bold", mb: "100px" }}>Người lập phiếu</Typography>
                        <Typography sx={{ fontWeight: "bold" }}>
                            {cart.processUser ? cart.processUser : "Lê Quang Dũng"}
                        </Typography>
                    </Box>
                </Grid>
            </Grid>
        </div>
    );
}

export default Invoice;
