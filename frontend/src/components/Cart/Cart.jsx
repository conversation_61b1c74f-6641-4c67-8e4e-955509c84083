import React from "react";
import PropTypes from "prop-types";
import HomeAppBar from "../Common/HomeAppBar";
import Grid from "@mui/material/Grid2";
import Box from "@mui/material/Box";
import Stepper from "@mui/material/Stepper";
import Step from "@mui/material/Step";
import StepLabel from "@mui/material/StepLabel";
import Button from "@mui/material/Button";
import Typography from "@mui/material/Typography";
import Footer from "../Common/Footer";
import {
    Accordion,
    AccordionDetails,
    AccordionSummary,
    Autocomplete,
    Card,
    CardContent,
    CardMedia,
    Container,
    Divider,
    FormControl,
    FormControlLabel,
    FormLabel,
    IconButton,
    Paper,
    Radio,
    RadioGroup,
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    TextField,
} from "@mui/material";
import KeyboardBackspaceIcon from "@mui/icons-material/KeyboardBackspace";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import ArrowForwardIcon from "@mui/icons-material/ArrowForward";
import { green, grey, red } from "@mui/material/colors";
import DoneIcon from "@mui/icons-material/Done";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import TaskAltIcon from "@mui/icons-material/TaskAlt";
import ArrowBack from "@mui/icons-material/ArrowBack";
import DeleteIcon from "@mui/icons-material/Delete";
import { useNavigate } from "react-router-dom";
import axiosInstance from "../../services/axiosInstance";
import ShoppingBagIcon from "@mui/icons-material/ShoppingBag";
import CustomButton from "../Common/CustomButton";
import CustomTextField from "../Common/CustomTextField";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import Loading from "../Common/Loading";
import ErrorIcon from "@mui/icons-material/Error";
import SendIcon from "@mui/icons-material/Send";
import WalletIcon from "@mui/icons-material/Wallet";
import PersonPinIcon from "@mui/icons-material/PersonPin";
import ReCaptcha from "../Common/ReCaptcha";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

CheckoutCart.propTypes = {
    cartInfo: PropTypes.shape({
        user: PropTypes.string.isRequired,
        id: PropTypes.string.isRequired,
        short_id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        phone: PropTypes.string.isRequired,
        address: PropTypes.string.isRequired,
        ward: PropTypes.string.isRequired,
        province: PropTypes.string.isRequired,
        note: PropTypes.string.isRequired,
    }).isRequired,
    cartItem: PropTypes.array.isRequired,
    // setCartItem: PropTypes.func.isRequired,
    // setActiveStep: PropTypes.func.isRequired,
};

CheckPayment.propTypes = {
    cartInfo: PropTypes.shape({
        user: PropTypes.string.isRequired,
        id: PropTypes.string.isRequired,
        short_id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        phone: PropTypes.string.isRequired,
        address: PropTypes.string.isRequired,
        ward: PropTypes.string.isRequired,
        province: PropTypes.string.isRequired,
        note: PropTypes.string.isRequired,
    }).isRequired,
    cartItem: PropTypes.array.isRequired,
    setCartItem: PropTypes.func.isRequired,
    setActiveStep: PropTypes.func.isRequired,
};

CheckCart.propTypes = {
    cartInfo: PropTypes.shape({
        user: PropTypes.string.isRequired,
        id: PropTypes.string.isRequired,
        short_id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        phone: PropTypes.string.isRequired,
        address: PropTypes.string.isRequired,
        ward: PropTypes.string.isRequired,
        province: PropTypes.string.isRequired,
        note: PropTypes.string.isRequired,
    }).isRequired,
    cartItem: PropTypes.array.isRequired,
    setCartItem: PropTypes.func.isRequired,
    setActiveStep: PropTypes.func.isRequired,
};

CheckAddress.propTypes = {
    cartInfo: PropTypes.shape({
        user: PropTypes.string.isRequired,
        id: PropTypes.string.isRequired,
        short_id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        phone: PropTypes.string.isRequired,
        address: PropTypes.string.isRequired,
        ward: PropTypes.string.isRequired,
        province: PropTypes.string.isRequired,
        note: PropTypes.string.isRequired,
    }).isRequired,
    setCartInfo: PropTypes.func.isRequired,
    cartItem: PropTypes.array.isRequired,
    setActiveStep: PropTypes.func.isRequired,
};

HorizontalLinearStepper.propTypes = {
    cartInfo: PropTypes.shape({
        user: PropTypes.string.isRequired,
        id: PropTypes.string.isRequired,
        short_id: PropTypes.string.isRequired,
        name: PropTypes.string.isRequired,
        phone: PropTypes.string.isRequired,
        address: PropTypes.string.isRequired,
        ward: PropTypes.string.isRequired,
        province: PropTypes.string.isRequired,
        note: PropTypes.string.isRequired,
    }).isRequired,
    setCartInfo: PropTypes.func.isRequired,
    cartItem: PropTypes.array.isRequired,
    setCartItem: PropTypes.func.isRequired,
};

function formatPrice(number) {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function CheckoutCart(props) {
    const navigate = useNavigate();

    const deliveryFee = props.deliveryType === "ship" ? props.deliveryFee : 0;

    function handleRedirect(href) {
        navigate(href);
    }

    function get_total_after_sale() {
        let total_amount = 0;
        for (var i = 0; i < props.cartItem.length; i++) {
            if (props.cartItem[i].discount_value > 0)
                total_amount += Math.round(props.cartItem[i].final_price * props.cartItem[i].quantity);
            else total_amount += Math.round(props.cartItem[i].sale_price * props.cartItem[i].quantity);
        }
        return total_amount;
    }
    const mode = 1;
    if (mode === 1)
        return (
            <Card sx={{ width: "100%", borderRadius: "20px" }}>
                <Box sx={{ mt: "20px" }}>
                    <Typography
                        sx={{
                            fontWeight: "bold",
                            ml: 1,
                            display: { xs: "flex", md: "none" },
                            mt: 1,
                            mb: 1,
                        }}
                        color="error"
                    >
                        #{props.cartInfo.short_id}
                    </Typography>
                    <Box
                        sx={{
                            display: "flex",
                            width: "100%",
                            justifyContent: "space-between",
                            alignItems: "baseline",
                        }}
                    >
                        <Typography
                            sx={{
                                fontWeight: "bold",
                                ml: "20px",
                                display: { xs: "none", md: "flex" },
                            }}
                            color="error"
                        >
                            Mã đơn hàng: #{props.cartInfo.short_id}
                        </Typography>
                        <Box
                            sx={{
                                display: "flex",
                                alignItems: "baseline",
                                ml: 1,
                            }}
                        >
                            <Typography
                                sx={{
                                    fontWeight: 400,
                                    fontSize: 20,
                                    mr: "15px",
                                }}
                            >
                                Tổng tiền:
                            </Typography>
                            <Typography
                                sx={{
                                    fontWeight: 600,
                                    fontSize: 30,
                                    color: red[500],
                                    mr: 1,
                                }}
                            >
                                {formatPrice(get_total_after_sale() - props.coupon.discount + deliveryFee)}
                            </Typography>
                        </Box>
                    </Box>
                </Box>
                {props.showInfo && (
                    <Box sx={{ ml: 1, color: grey[800], width: "100%", mr: 1 }}>
                        <Grid container spacing={2} sx={{ mb: 1 }}>
                            <Grid size={{ xs: 4, md: 3 }}>
                                <Typography sx={{ color: grey[600] }}>Họ & Tên:</Typography>
                            </Grid>
                            <Grid size={{ xs: 8, md: 9 }}>
                                <Typography>{props.cartInfo.name}</Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} sx={{ mb: 1 }}>
                            <Grid size={{ xs: 4, md: 3 }}>
                                <Typography sx={{ color: grey[600] }}>Số điện thoại:</Typography>
                            </Grid>
                            <Grid size={{ xs: 8, md: 9 }}>
                                <Typography>{props.cartInfo.phone}</Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} sx={{ mb: 1 }}>
                            <Grid size={{ xs: 4, md: 3 }}>
                                <Typography sx={{ color: grey[600] }}>Địa chỉ:</Typography>
                            </Grid>
                            <Grid size={{ xs: 8, md: 9 }}>
                                <Typography>
                                    {props.cartInfo.address}, {props.cartInfo.ward}, {props.cartInfo.district},{" "}
                                    {props.cartInfo.province}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2} sx={{ mb: 1 }}>
                            <Grid size={{ xs: 4, md: 3 }}>
                                <Typography sx={{ color: grey[600] }}>Ghi chú:</Typography>
                            </Grid>
                            <Grid size={{ xs: 8, md: 9 }}>
                                <Typography>{props.cartInfo.note}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                )}
                <Divider sx={{ mt: "20px", mb: "20px" }} />
                <Typography sx={{ fontWeight: 600, ml: "20px", mb: "10px" }}>Danh sách sản phẩm</Typography>
                <TableContainer component={Box} sx={{ borderRadius: "20px" }}>
                    <Table sx={{ minWidth: 650 }} aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell>#</TableCell>
                                <TableCell align="left">Sản Phẩm</TableCell>
                                <TableCell align="right">Đơn Giá</TableCell>
                                <TableCell align="right">Số Lượng</TableCell>
                                <TableCell align="right">Thành Tiền</TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {props.cartItem
                                .filter((item) => item.quantity > 0) // Filters out items with quantity 0
                                .map((item, index) => (
                                    <TableRow
                                        key={index}
                                        sx={{
                                            "&:last-child td, &:last-child th": { border: 0 },
                                        }}
                                    >
                                        <TableCell component="th" scope="row">
                                            {index + 1}
                                        </TableCell>
                                        <TableCell align="left">
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    alignItems: "initial",
                                                }}
                                            >
                                                <img
                                                    width="64px"
                                                    height="64px"
                                                    style={{
                                                        objectFit: "contain",
                                                        marginRight: "10px",
                                                        borderRadius: "10px",
                                                    }}
                                                    src={item.image}
                                                    alt={item.name}
                                                />
                                                <Box
                                                    sx={{ cursor: "pointer" }}
                                                    onClick={() => handleRedirect("/product/item/" + item.sku)}
                                                >
                                                    <Typography
                                                        sx={{
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        {item.name}
                                                    </Typography>
                                                    <Typography
                                                        sx={{
                                                            fontSize: 13,
                                                            fontWeight: 400,
                                                            color: grey[600],
                                                        }}
                                                    >
                                                        {"#" + item.sku}
                                                    </Typography>
                                                    {item.attributes.map((attr, index) => (
                                                        <Typography
                                                            key={index}
                                                            sx={{
                                                                fontSize: "12px",
                                                                fontWeight: 400,
                                                                color: grey[600],
                                                            }}
                                                        >
                                                            {attr.key}: {attr.value}
                                                        </Typography>
                                                    ))}
                                                </Box>
                                            </Box>
                                        </TableCell>
                                        <TableCell align="right">
                                            <Typography sx={{ color: grey[900] }} variant="span">
                                                {item.discount_value > 0
                                                    ? formatPrice(item.final_price)
                                                    : formatPrice(item.sale_price)}
                                            </Typography>
                                        </TableCell>
                                        <TableCell align="right">
                                            <Typography sx={{ color: grey[900] }} variant="span">
                                                {"x " + item.quantity}
                                            </Typography>
                                        </TableCell>
                                        <TableCell align="right">
                                            <Typography sx={{ fontWeight: 600 }}>
                                                {item.discount_value > 0
                                                    ? formatPrice(item.final_price * item.quantity)
                                                    : formatPrice(item.sale_price * item.quantity)}
                                            </Typography>
                                        </TableCell>
                                    </TableRow>
                                ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Card>
        );
    else
        return (
            <Card sx={{ width: "100%", borderRadius: "20px" }}>
                <Box
                    sx={{
                        display: "flex",
                        width: "100%",
                        justifyContent: "space-between",
                        alignItems: "baseline",
                        mt: "20px",
                    }}
                >
                    <Typography sx={{ fontWeight: "bold", ml: "20px" }} color="error">
                        Mã đơn hàng: #{props.cartInfo.short_id}
                    </Typography>
                    <Box sx={{ display: "flex", alignItems: "baseline" }}>
                        <Typography sx={{ fontWeight: 400, fontSize: 20, mr: "15px" }}>Tổng tiền:</Typography>
                        <Typography
                            sx={{
                                fontWeight: 600,
                                fontSize: 30,
                                color: red[500],
                                mr: "20px",
                            }}
                        >
                            {formatPrice(get_total_after_sale())}
                        </Typography>
                    </Box>
                </Box>
                {props.showInfo && (
                    <Box sx={{ ml: "20px", color: grey[800], width: "100%" }}>
                        <Grid container spacing={2}>
                            <Grid size={2}>
                                <Typography sx={{ color: grey[600] }}>Họ & Tên:</Typography>
                            </Grid>
                            <Grid size={9}>
                                <Typography>{props.cartInfo.name}</Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid size={2}>
                                <Typography sx={{ color: grey[600] }}>Số điện thoại:</Typography>
                            </Grid>
                            <Grid size={9}>
                                <Typography>{props.cartInfo.phone}</Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid size={2}>
                                <Typography sx={{ color: grey[600] }}>Địa chỉ:</Typography>
                            </Grid>
                            <Grid size={9}>
                                <Typography>
                                    {props.cartInfo.address}, {props.cartInfo.ward}, {props.cartInfo.province}
                                </Typography>
                            </Grid>
                        </Grid>
                        <Grid container spacing={2}>
                            <Grid size={2}>
                                <Typography sx={{ color: grey[600] }}>Ghi chú:</Typography>
                            </Grid>
                            <Grid size={9}>
                                <Typography>{props.cartInfo.note}</Typography>
                            </Grid>
                        </Grid>
                    </Box>
                )}
                <Divider sx={{ mt: "20px", mb: "20px" }} />
                {props.cartItem.map((item) => (
                    <>
                        <Box
                            sx={{
                                ml: "20px",
                                mr: "20px",
                                display: "flex",
                                borderRadius: "20px",
                                mb: "20px",
                                alignItems: "center",
                            }}
                        >
                            <CardMedia
                                sx={{
                                    width: "120px",
                                    height: "120px",
                                    objectFit: "contain",
                                    ml: "10px",
                                }}
                                image={item.image}
                                title={item.name}
                            />
                            <CardContent sx={{ flexGrow: 1 }}>
                                <Box sx={{ mb: "20px" }}>
                                    <Typography
                                        sx={{
                                            fontSize: 18,
                                            mr: "10px",
                                            fontWeight: 600,
                                        }}
                                    >
                                        {item.name}
                                    </Typography>
                                    {/* <Typography sx={{ fontSize: 13, color: grey[600] }}>Màu: {item.color}</Typography> */}
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "baseline",
                                        mb: "20px",
                                    }}
                                >
                                    <Typography sx={{ color: grey[600], mr: "10px" }} variant="span">
                                        {formatPrice(item.final_price)} x {item.quantity}
                                    </Typography>
                                    <Typography sx={{ fontWeight: 600 }} color="error">
                                        = {formatPrice(item.final_price * item.quantity)}
                                    </Typography>
                                </Box>
                            </CardContent>
                        </Box>
                        <Divider />
                    </>
                ))}
            </Card>
        );
}

function CheckCart(props) {
    const navigate = useNavigate();
    const [couponMessage, setCouponMessage] = React.useState("");

    function handleRedirect(href) {
        navigate(href);
    }

    function handleDescQty(item_id) {
        props.setCartItem((prevCartItems) =>
            prevCartItems.map((_item) =>
                _item.id === item_id
                    ? {
                          ..._item,
                          quantity: _item.quantity - 1 > 0 ? _item.quantity - 1 : 1,
                      }
                    : _item
            )
        );
    }

    function handleIncQty(item_id) {
        props.setCartItem((prevCartItems) =>
            prevCartItems.map((_item) => (_item.id === item_id ? { ..._item, quantity: _item.quantity + 1 } : _item))
        );
    }

    function handleChangeQty(item_id, quantity) {
        props.setCartItem((prevCartItems) =>
            prevCartItems.map((_item) =>
                _item.id === item_id ? { ..._item, quantity: quantity > 0 ? quantity : 1 } : _item
            )
        );
    }

    function formatPrice(number) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "VND",
        }).format(number);
    }

    function get_total_price() {
        let total_amount = 0;
        for (var i = 0; i < props.cartItem.length; i++) {
            total_amount += props.cartItem[i].sale_price * props.cartItem[i].quantity;
        }
        return total_amount;
    }

    function get_total_after_sale() {
        let total_amount = 0;
        for (var i = 0; i < props.cartItem.length; i++) {
            if (props.cartItem[i].discount_value > 0)
                total_amount += Math.round(props.cartItem[i].final_price * props.cartItem[i].quantity);
            else total_amount += Math.round(props.cartItem[i].sale_price * props.cartItem[i].quantity);
        }
        return total_amount;
    }

    function handleNextStep() {
        for (var i = 0; i < props.cartItem.length; i++) {
            const item = props.cartItem[i];
            axiosInstance
                .patch(`/api/shopping/cart/detail/view/${item.id}`, {
                    quantity: item.quantity,
                })
                .then((response) => {
                    if (response.status === 200) console.info(`Item ${item.id} updated`);
                });
        }
        props.setActiveStep(1);
    }

    const handleRemoveItemFromCart = async (item_id) => {
        try {
            const response = await axiosInstance.delete(`/api/shopping/cart/detail/view/${item_id}`);
            if (response.status === 200) {
                const updatedCartItem = props.cartItem.filter((item) => item.id != item_id);
                props.setCartItem(updatedCartItem);
            }
        } catch (error) {
            console.error("Error deleting cart item:", error);
        }
    };

    const handleApplyCoupon = async () => {
        if (props.coupon.code === "") {
            alert("Mã giám giá không được để trống!");
            return;
        }
        const formData = new FormData();
        formData.append("coupon_code", props.coupon.code);
        formData.append("total", get_total_after_sale());
        await axiosInstance
            .post("/api/shopping/cart/apply-coupon", formData, {
                headers: {
                    "Content-Type": "application/json",
                },
            })
            .then((response) => {
                if (response.data.success) {
                    setCouponMessage("Áp mã giảm giá thành công!");
                    props.setCoupon({
                        ...props.coupon,
                        discount: parseInt(response.data.discount),
                    });
                } else {
                    setCouponMessage(response.data.message);
                    props.setCoupon({
                        ...props.coupon,
                        discount: 0,
                    });
                }
            })
            .catch((error) => {
                setCouponMessage("Mã giảm giá không tồn tại!");
                props.setCoupon({
                    ...props.coupon,
                    discount: 0,
                });
            });
    };

    return (
        <Grid container spacing={2} sx={{ width: "100%" }}>
            <Grid size={{ xs: 12, md: 8 }}>
                <TableContainer component={Paper} sx={{ borderRadius: "20px" }}>
                    <Table sx={{ minWidth: 650 }} aria-label="simple table">
                        <TableHead>
                            <TableRow>
                                <TableCell>#</TableCell>
                                <TableCell align="left">Sản Phẩm</TableCell>
                                <TableCell align="center">Số Lượng</TableCell>
                                <TableCell align="right">Thành Tiền</TableCell>
                                <TableCell align="right"></TableCell>
                            </TableRow>
                        </TableHead>
                        <TableBody>
                            {props.cartItem.map((item, index) => (
                                <TableRow
                                    key={index}
                                    sx={{
                                        "&:last-child td, &:last-child th": {
                                            border: 0,
                                        },
                                    }}
                                >
                                    <TableCell component="th" scope="row">
                                        {index + 1}
                                    </TableCell>
                                    <TableCell align="left">
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "initial",
                                            }}
                                        >
                                            <img
                                                width="64px"
                                                height="64px"
                                                style={{
                                                    objectFit: "contain",
                                                    marginRight: "10px",
                                                    borderRadius: "10px",
                                                    // border: "1px solid #ebebeb"
                                                }}
                                                src={item.image}
                                                alt={item.name}
                                            />
                                            <Box
                                                sx={{ cursor: "pointer" }}
                                                onClick={() => handleRedirect("/product/item/" + item.sku)}
                                            >
                                                {item.visible ? (
                                                    <Typography
                                                        sx={{
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        {item.name}
                                                    </Typography>
                                                ) : (
                                                    <Typography
                                                        sx={{
                                                            fontSize: 14,
                                                            fontWeight: 600,
                                                        }}
                                                    >
                                                        <strike>{item.name}</strike>
                                                    </Typography>
                                                )}

                                                <Typography
                                                    sx={{
                                                        fontSize: 13,
                                                        fontWeight: 400,
                                                        color: grey[500],
                                                        mb: "5px",
                                                    }}
                                                >
                                                    {"#" + item.sku}
                                                </Typography>
                                                {item.attributes.map((attr, index) => (
                                                    <Typography
                                                        key={index}
                                                        sx={{
                                                            fontSize: 13,
                                                            fontWeight: 400,
                                                            color: grey[500],
                                                        }}
                                                    >
                                                        {attr.key}: {attr.value}
                                                    </Typography>
                                                ))}
                                                <Box
                                                    sx={{
                                                        display: "flex",
                                                        alignItems: "baseline",
                                                    }}
                                                >
                                                    {item.discount_value > 0 && item.visible ? (
                                                        <>
                                                            <Typography
                                                                sx={{
                                                                    mr: "5px",
                                                                }}
                                                            >
                                                                {formatPrice(item.final_price)}
                                                            </Typography>
                                                            <Typography
                                                                sx={{
                                                                    fontSize: 13,
                                                                    color: grey[500],
                                                                }}
                                                                variant="span"
                                                            >
                                                                <del>{formatPrice(item.sale_price)}</del>
                                                            </Typography>
                                                        </>
                                                    ) : (
                                                        <Typography sx={{ mr: "5px" }}>
                                                            {item.visible
                                                                ? formatPrice(item.sale_price)
                                                                : "Đã hết hàng"}
                                                        </Typography>
                                                    )}
                                                </Box>
                                            </Box>
                                        </Box>
                                    </TableCell>
                                    <TableCell align="center">
                                        <Box
                                            sx={{
                                                display: "flex",
                                            }}
                                        >
                                            <IconButton
                                                sx={{ borderRadius: "10px" }}
                                                disabled={item.quantity <= 1 || !item.visible}
                                                aria-label="delete"
                                                color="error"
                                                onClick={() => handleDescQty(item.id)}
                                            >
                                                <RemoveIcon />
                                            </IconButton>
                                            <CustomTextField
                                                disabled={!item.visible}
                                                id="item-quantity"
                                                value={item.quantity}
                                                onChange={(event) => handleChangeQty(item.id, event.target.value)}
                                                sx={{
                                                    width: "75px",
                                                    ml: "5px",
                                                    mr: "5px",
                                                    border: "0px",
                                                }}
                                                slotProps={{
                                                    htmlInput: {
                                                        style: {
                                                            textAlign: "center",
                                                        },
                                                    }, // Centering the text
                                                }}
                                                size="small"
                                                type="number"
                                            />
                                            <IconButton
                                                disabled={!item.visible}
                                                sx={{ borderRadius: "10px" }}
                                                aria-label="delete"
                                                color="error"
                                                onClick={() => handleIncQty(item.id)}
                                            >
                                                <AddIcon />
                                            </IconButton>
                                        </Box>
                                    </TableCell>
                                    <TableCell align="right">
                                        <Typography sx={{ fontWeight: 600 }} color="error">
                                            {item.discount_value > 0
                                                ? formatPrice(item.final_price * item.quantity)
                                                : formatPrice(item.sale_price * item.quantity)}
                                        </Typography>
                                    </TableCell>
                                    <TableCell align="right">
                                        <IconButton
                                            sx={{
                                                height: "40px",
                                                margin: "5px",
                                                color: grey[400],
                                                "&:hover": {
                                                    color: red[700],
                                                    backgroundColor: "transparent",
                                                },
                                            }}
                                            onClick={() => handleRemoveItemFromCart(item.id)}
                                        >
                                            <DeleteIcon />
                                        </IconButton>
                                    </TableCell>
                                </TableRow>
                            ))}
                        </TableBody>
                    </Table>
                </TableContainer>
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
                <Card sx={{ width: "100%", borderRadius: "20px" }}>
                    <Box sx={{ padding: "20px" }}>
                        <Box>
                            <Typography sx={{ fontWeight: "bold" }} color="error">
                                Mã đơn hàng: #{props.cartInfo.short_id}
                            </Typography>
                        </Box>
                        <Divider sx={{ mt: "10px", mb: "10px" }} />
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                mb: "10px",
                            }}
                        >
                            <Typography>Tổng tiền:</Typography>
                            <Typography>{formatPrice(get_total_price())}</Typography>
                        </Box>
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                mb: "10px",
                            }}
                        >
                            <Typography>Giảm giá:</Typography>
                            <Typography>{formatPrice(get_total_after_sale() - get_total_price())}</Typography>
                        </Box>
                        {props.coupon.discount > 0 && (
                            <Box
                                sx={{
                                    display: "flex",
                                    justifyContent: "space-between",
                                    mb: "10px",
                                }}
                            >
                                <Typography>Voucher:</Typography>
                                <Typography>-{formatPrice(props.coupon.discount)}</Typography>
                            </Box>
                        )}
                        <Box
                            sx={{
                                display: "flex",
                                justifyContent: "space-between",
                                alignItems: "baseline",
                                mb: "10px",
                                color: red[900],
                            }}
                        >
                            <Typography sx={{ fontSize: "20px" }}>Thành tiền:</Typography>
                            <Typography sx={{ fontSize: "24px" }}>
                                {formatPrice(get_total_after_sale() - props.coupon.discount)}
                            </Typography>
                        </Box>
                        <Divider
                            sx={{
                                mt: "10px",
                                mb: "20px",
                                alignItems: "center",
                            }}
                        />
                        <Box sx={{ display: "flex" }}>
                            <CustomTextField
                                id="voucher"
                                label="Mã khuyến mãi"
                                variant="outlined"
                                sx={{ mr: "15px" }}
                                size="small"
                                value={props.coupon.code}
                                onChange={(event) => {
                                    props.setCoupon({
                                        ...props.coupon,
                                        code: event.target.value,
                                    });
                                }}
                            />
                            <Button
                                size="small"
                                sx={{ borderRadius: "10px", width: "80px" }}
                                onClick={handleApplyCoupon}
                            >
                                Áp dụng
                            </Button>
                        </Box>
                        {couponMessage != "" && props.coupon.discount > 0 && (
                            <Box
                                sx={{
                                    display: "flex",
                                    mt: "10px",
                                    alignItems: "center",
                                }}
                            >
                                <CheckCircleIcon sx={{ color: green[500] }} />
                                <Typography
                                    sx={{
                                        color: green[500],
                                        fontSize: 13,
                                        ml: 1,
                                    }}
                                >
                                    {couponMessage}
                                </Typography>
                            </Box>
                        )}
                        {couponMessage != "" && props.coupon.discount == 0 && (
                            <Box
                                sx={{
                                    display: "flex",
                                    mt: "10px",
                                    alignItems: "center",
                                }}
                            >
                                <ErrorIcon sx={{ color: red[500] }} />
                                <Typography
                                    sx={{
                                        color: red[500],
                                        fontSize: 13,
                                        ml: 1,
                                    }}
                                >
                                    {couponMessage}
                                </Typography>
                            </Box>
                        )}
                        <Button
                            fullWidth
                            variant="contained"
                            color="error"
                            sx={{ borderRadius: "10px", mt: "15px" }}
                            onClick={() => handleNextStep()}
                        >
                            Tiếp tục
                        </Button>
                    </Box>
                </Card>
            </Grid>
        </Grid>
    );
}

function CheckAddress(props) {
    const [provinceList, setProvinceList] = React.useState([]);
    const [districtList, setDistrictList] = React.useState([]);
    const [wardList, setWardList] = React.useState([]);
    const [loading, setLoading] = React.useState(true);

    React.useEffect(() => {
        const fetchData = async () => {
            const response = await axiosInstance.get("/api/shopping/location/province");
            if (response.status === 200) {
                setProvinceList(response.data);
                // If there is a province in cartInfo, get district list
                if (props.cartInfo.province !== "") {
                    const provinces = response.data;
                    handleGetDistrict(provinces.find((province) => province.name === props.cartInfo.province).code);
                } else {
                    setLoading(false);
                }
            }
        };
        fetchData();
    }, []);

    const handleGetDistrict = async (province_id) => {
        const response = await axiosInstance.get(`/api/shopping/location/province/${province_id}`);
        if (response.status === 200) {
            setDistrictList(response.data);
            // If there is a district in cartInfo, get ward list
            if (props.cartInfo.district !== "") {
                const districts = response.data;
                try {
                    handleGetWard(
                        province_id,
                        districts.find((district) => district.name === props.cartInfo.district).code
                    );
                } catch (error) {
                    console.error(error);
                    setLoading(false);
                }
            } else {
                setLoading(false);
            }
        }
    };

    const handleGetWard = async (province_id, district_id) => {
        const response = await axiosInstance.get(`/api/shopping/location/province/${province_id}/${district_id}`);
        if (response.status === 200) {
            setWardList(response.data);
        }
        setLoading(false);
    };

    function handleNextStep() {
        if (props.cartInfo.name === "") {
            alert("Vui lòng nhập Họ và Tên!");
            return;
        }
        if (props.cartInfo.phone === "") {
            alert("Vui lòng nhập Số điện thoại!");
            return;
        }
        if (props.cartInfo.address === "") {
            alert("Vui lòng nhập Địa chỉ!");
            return;
        }
        if (props.cartInfo.province === "") {
            alert("Vui lòng chọn Tỉnh!");
            return;
        }
        axiosInstance
            .patch(`/api/shopping/cart/view`, {
                customer_name: props.cartInfo.name,
                customer_phone: props.cartInfo.phone,
                customer_address: props.cartInfo.address,
                customer_province: props.cartInfo.province,
                customer_district: props.cartInfo.district,
                customer_ward: props.cartInfo.ward,
                customer_note: props.cartInfo.note,
            })
            .then((response) => {
                if (response.status === 200) {
                    console.info(`CartInfo is updated`);
                    props.setActiveStep(2);
                }
            });
    }

    const handleUpdateCartInfo = (event) => {
        props.setCartInfo({
            ...props.cartInfo,
            [event.target.name]: event.target.value,
        });
    };

    if (loading) return <Loading />;

    return (
        <Grid container spacing={2} sx={{ width: "100%" }}>
            <Grid size={{ xs: 12, md: 8 }}>
                <CheckoutCart
                    cartItem={props.cartItem}
                    cartInfo={props.cartInfo}
                    coupon={props.coupon}
                    deliveryType={""}
                    deliveryFee={0}
                />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
                <Card
                    sx={{
                        maxWidth: "400px",
                        borderRadius: "20px",
                        padding: "24px",
                        display: "flex",
                        justifyContent: "center",
                        flexDirection: "column",
                        alignItems: "center",
                    }}
                >
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: "20px",
                            color: grey[600],
                        }}
                    >
                        <PersonPinIcon sx={{ mr: 1 }} />
                        <Typography variant="h6">Thông tin người nhận</Typography>
                    </Box>
                    <Box sx={{ display: "flex", mb: "15px" }}>
                        <CustomTextField
                            name="name"
                            label="Họ và Tên"
                            variant="outlined"
                            sx={{ mr: "20px" }}
                            value={props.cartInfo.name}
                            onChange={handleUpdateCartInfo}
                        />
                        <CustomTextField
                            name="phone"
                            label="Số điện thoại"
                            variant="outlined"
                            value={props.cartInfo.phone}
                            onChange={handleUpdateCartInfo}
                        />
                    </Box>
                    <Autocomplete
                        disablePortal
                        name="province"
                        options={provinceList}
                        getOptionLabel={(option) => option.name}
                        value={provinceList.find((option) => option.name === props.cartInfo.province) || null}
                        onChange={(event, newValue) => {
                            props.setCartInfo({
                                ...props.cartInfo,
                                province: newValue.name,
                            });
                            handleGetDistrict(newValue.code);
                        }}
                        sx={{ mb: "15px" }}
                        fullWidth
                        renderInput={(params) => <CustomTextField {...params} label="Tỉnh/Thành Phố" />}
                    />
                    <Autocomplete
                        disablePortal
                        name="district"
                        options={districtList}
                        getOptionLabel={(option) => option.name}
                        value={districtList.find((option) => option.name === props.cartInfo.district) || null}
                        onChange={(event, newValue) => {
                            props.setCartInfo({
                                ...props.cartInfo,
                                district: newValue.name,
                            });
                            handleGetWard(newValue.province_id, newValue.code);
                        }}
                        sx={{ mb: "15px" }}
                        fullWidth
                        renderInput={(params) => <CustomTextField {...params} label="Quận/Huyện" />}
                    />
                    <Autocomplete
                        disablePortal
                        name="ward"
                        options={wardList}
                        value={props.cartInfo.ward}
                        onChange={(event, newValue) => {
                            props.setCartInfo({
                                ...props.cartInfo,
                                ward: newValue,
                            });
                        }}
                        sx={{ mb: "15px" }}
                        fullWidth
                        renderInput={(params) => <CustomTextField {...params} label="Xã/Thị Trấn" />}
                    />
                    <CustomTextField
                        name="address"
                        label="Địa chỉ nhà"
                        ariant="outlined"
                        fullWidth
                        sx={{ mb: "15px" }}
                        value={props.cartInfo.address}
                        onChange={handleUpdateCartInfo}
                    />
                    {/* <CustomTextField
                        name="ward"
                        label="Phường/Thị xã"
                        variant="outlined"
                        fullWidth
                        sx={{ mb: "15px" }}
                        value={props.cartInfo.ward}
                        onChange={handleUpdateCartInfo}
                    /> */}
                    <CustomTextField
                        name="note"
                        label="Ghi chú"
                        variant="outlined"
                        fullWidth
                        sx={{ mb: "15px" }}
                        multiline
                        rows={4}
                        value={props.cartInfo.note}
                        onChange={handleUpdateCartInfo}
                    />
                    <Divider sx={{ mb: "15px", width: "100%" }}></Divider>
                    <Button
                        fullWidth
                        color="error"
                        variant="contained"
                        sx={{ borderRadius: "10px" }}
                        onClick={() => handleNextStep()}
                    >
                        Tiếp tục
                    </Button>
                </Card>
            </Grid>
        </Grid>
    );
}

function CheckPayment(props) {
    const [expanded, setExpanded] = React.useState(false);
    const [deliveryType, setDeliveryType] = React.useState("pickup");
    const deliveryFee = 35000;
    const [recaptchaValue, setRecaptchaValue] = React.useState(null);
    const [loading, setLoading] = React.useState(false);

    const payment_list = [
        {
            id: 1,
            value: "Tiền mặt",
            name: "Tiền mặt",
            description: "Quý khách vui lòng thanh toán tại quầy khi nhận hàng",
        },
        {
            id: 2,
            value: "Ngân hàng",
            name: "Ngân hàng",
            description: "Quý khách quét mã VietQR để chuyển khoản trong mail sau khi xác nhận",
        },
        {
            id: 3,
            value: "Ship COD",
            name: "Ship COD",
            description: "Quý khách gửi tiền sau khi nhận hàng",
        },
    ];

    const [selectedMethod, setSelectedMethod] = React.useState("panel" + payment_list[0].id);

    const handleNextStep = async () => {
        setLoading(true);

        if (!recaptchaValue && import.meta.env.VITE_ENV === "prod") {
            alert("Vui lòng xác nhận reCAPTCHA!");
            return;
        }
        try {
            const response = await axiosInstance.patch(`/api/shopping/cart/view`, {
                payment_method: payment_list[parseInt(selectedMethod.replace("panel", "")) - 1].value,
                coupon_code: props.coupon.code,
                delivery_fee: deliveryType === "ship" ? deliveryFee : 0,
                captcha_token: recaptchaValue,
            });

            if (response.status === 200) {
                console.info(`Cart done`);
                props.setCartItem([]);
                props.setActiveStep(3);
            }
        } catch (error) {
            if (error.status === 400) {
                alert("Lỗi: ", JSON.stringify(error.response));
            }
        } finally {
            setLoading(false);
        }
    };

    const handleChangeDelivery = (event) => {
        setDeliveryType(event.target.value);
    };

    return (
        <Grid container spacing={2} sx={{ width: "100%" }}>
            <Grid size={{ xs: 12, md: 8 }}>
                <CheckoutCart
                    cartItem={props.cartItem}
                    cartInfo={props.cartInfo}
                    coupon={props.coupon}
                    deliveryType={deliveryType}
                    deliveryFee={deliveryFee}
                    showInfo={true}
                />
            </Grid>
            <Grid size={{ xs: 12, md: 4 }}>
                <Card
                    sx={{
                        maxWidth: "400px",
                        borderRadius: "20px",
                        padding: "24px",
                        display: "flex",
                        justifyContent: "center",
                        flexDirection: "column",
                        alignItems: "center",
                        mb: 2,
                    }}
                >
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: "10px",
                            color: grey[900],
                        }}
                    >
                        <SendIcon sx={{ mr: 1 }} color="inherit" />
                        <Typography variant="h6" color="inherit">
                            Phương thức nhận hàng
                        </Typography>
                    </Box>
                    <Divider sx={{ mb: "10px", width: "100%" }}></Divider>
                    <Box sx={{ width: "100%" }}>
                        <FormControl sx={{ ml: 2 }}>
                            <RadioGroup
                                aria-labelledby="demo-controlled-radio-buttons-group"
                                name="controlled-radio-buttons-group"
                                value={deliveryType}
                                color="error"
                                onChange={handleChangeDelivery}
                            >
                                <FormControlLabel
                                    value="pickup"
                                    control={
                                        <Radio
                                            sx={{
                                                color: "red",
                                                "&.Mui-checked": {
                                                    color: "red",
                                                },
                                            }}
                                        />
                                    }
                                    label="Nhận tại cửa hàng"
                                />
                                <FormControlLabel
                                    value="ship"
                                    control={
                                        <Radio
                                            sx={{
                                                color: "red",
                                                "&.Mui-checked": {
                                                    color: "red",
                                                },
                                            }}
                                        />
                                    }
                                    label={`Ship đến địa chỉ (+${formatPrice(deliveryFee)})`}
                                />
                            </RadioGroup>
                        </FormControl>
                    </Box>
                </Card>
                <Card
                    sx={{
                        maxWidth: "400px",
                        borderRadius: "20px",
                        padding: "24px",
                        display: "flex",
                        justifyContent: "center",
                        flexDirection: "column",
                        alignItems: "center",
                    }}
                >
                    <Box
                        sx={{
                            display: "flex",
                            alignItems: "center",
                            mb: "20px",
                            color: grey[600],
                        }}
                    >
                        <WalletIcon sx={{ mr: 1 }} color="inherit" />
                        <Typography variant="h6" color="inherit">
                            Phương thức thanh toán
                        </Typography>
                    </Box>
                    <div style={{ marginBottom: "20px" }}>
                        <FormControl>
                            <RadioGroup
                                aria-labelledby="demo-radio-buttons-group-label"
                                value={selectedMethod}
                                name="radio-buttons-group"
                            >
                                {payment_list.map((method) => (
                                    <Accordion
                                        expanded={expanded === "panel" + method.id}
                                        key={method.id}
                                        onChange={() => {
                                            setExpanded(expanded === "panel" + method.id ? false : "panel" + method.id);
                                            setSelectedMethod("panel" + method.id);
                                        }}
                                    >
                                        <AccordionSummary
                                            expandIcon={<ExpandMoreIcon />}
                                            aria-controls={method.name}
                                            id={"panel-" + method.id}
                                        >
                                            <FormControlLabel
                                                color="error"
                                                value={"panel" + method.id}
                                                onClick={(event) => event.stopPropagation()}
                                                control={
                                                    <Radio
                                                        sx={{
                                                            color: "red",
                                                            "&.Mui-checked": {
                                                                color: "red",
                                                            },
                                                        }}
                                                    />
                                                }
                                                label={method.name}
                                            />
                                        </AccordionSummary>
                                        <AccordionDetails>
                                            <Typography>{method.description}</Typography>
                                        </AccordionDetails>
                                    </Accordion>
                                ))}
                            </RadioGroup>
                        </FormControl>
                    </div>
                    <Divider sx={{ mb: "15px", width: "100%" }}></Divider>
                    {import.meta.env.VITE_ENV === "prod" && (
                        <Box
                            sx={{
                                mb: 2,
                                display: "flex",
                                justifyContent: "start",
                                width: "100%",
                            }}
                        >
                            <ReCaptcha onChange={(value) => setRecaptchaValue(value)} />
                        </Box>
                    )}
                    <Button
                        fullWidth
                        color="error"
                        variant="contained"
                        sx={{ borderRadius: "10px" }}
                        loading={loading}
                        disabled={loading}
                        onClick={handleNextStep}
                    >
                        Xác nhận
                    </Button>
                </Card>
            </Grid>
        </Grid>
    );
}

function CheckDone({ setCartItem }) {
    React.useEffect(() => {
        setCartItem([]);
    }, []);

    return (
        <>
            <Box
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    width: "100%",
                    flexDirection: "column",
                }}
            >
                <IconButton color="error" sx={{ mb: "20px" }}>
                    <TaskAltIcon sx={{ fontSize: "100px" }} />
                </IconButton>
                <Typography variant="h4" component="h1" sx={{ textAlign: "center", mb: "15px" }}>
                    Cám ơn bạn đã lựa chọn BeE!
                </Typography>
                <Typography variant="body1" component="p" sx={{ textAlign: "center", mb: "20px" }}>
                    Chúng tôi sẽ phản hồi trong thời gian sớm nhất
                </Typography>
                <Box
                    sx={{
                        display: "flex",
                        justifyContent: "center",
                        width: "100%",
                    }}
                >
                    <CustomButton variant="contained" href="/" color="error" startIcon={<ArrowBack />}>
                        Trang Chủ
                    </CustomButton>
                </Box>
            </Box>
        </>
    );
}

function HorizontalLinearStepper({ cartItem, setCartItem, cartInfo, setCartInfo, coupon, setCoupon }) {
    const navigate = useNavigate();

    const steps = ["Giỏ hàng", "Địa chỉ nhận", "Thanh toán", "Hoàn tất"];

    const [activeStep, setActiveStep] = React.useState(0);

    const stepsRender = [
        <CheckCart
            cartItem={cartItem}
            setCartItem={setCartItem}
            cartInfo={cartInfo}
            setActiveStep={setActiveStep}
            coupon={coupon}
            setCoupon={setCoupon}
            key={1}
        />,
        <CheckAddress
            setActiveStep={setActiveStep}
            cartItem={cartItem}
            cartInfo={cartInfo}
            setCartInfo={setCartInfo}
            coupon={coupon}
            key={2}
        />,
        <CheckPayment
            setActiveStep={setActiveStep}
            cartItem={cartItem}
            setCartItem={setCartItem}
            cartInfo={cartInfo}
            setCartInfo={setCartInfo}
            coupon={coupon}
            // onStepComplete={handleStepComplete}
            key={3}
        />,
        <CheckDone setCartItem={setCartItem} key={4} />,
    ];

    const handleNext = () => {
        setActiveStep((prevActiveStep) => prevActiveStep + 1);
    };

    const handleBack = () => {
        setActiveStep((prevActiveStep) => prevActiveStep - 1);
    };

    function handleRedirect(href) {
        navigate(href);
    }

    const CustomStepIcon = (props) => {
        const { active, completed, className } = props;

        return (
            <CheckCircleIcon
                className={className}
                style={{
                    color: completed ? red[600] : active ? red[600] : grey[300],
                }}
            />
        );
    };

    return (
        <Box sx={{ width: "100%" }}>
            <Stepper activeStep={activeStep} alternativeLabel>
                {steps.map((label, index) => {
                    const stepProps = {};
                    const labelProps = {};
                    return (
                        <Step key={index} {...stepProps}>
                            <StepLabel StepIconComponent={CustomStepIcon} {...labelProps}>
                                {label}
                            </StepLabel>
                        </Step>
                    );
                })}
            </Stepper>
            {activeStep === steps.length ? (
                <React.Fragment>
                    <Container sx={{ display: "flex", justifyContent: "center" }}>
                        <CustomButton href="/" startIcon={<KeyboardBackspaceIcon />} variant="outlined">
                            Trở về
                        </CustomButton>
                    </Container>
                </React.Fragment>
            ) : (
                <React.Fragment>
                    <Box sx={{ mt: 2, mb: 1 }}>{stepsRender[activeStep]}</Box>
                    <Box sx={{ display: "flex", flexDirection: "row", pt: 2 }}>
                        {activeStep === 0 ? (
                            <Button
                                color="inherit"
                                onClick={() => {
                                    handleRedirect("/shop");
                                }}
                                sx={{
                                    mr: 1,
                                    borderRadius: "10px",
                                    color: grey[600],
                                    textTransform: "none",
                                }}
                                startIcon={<ArrowBackIcon />}
                            >
                                Tiếp tục mua hàng
                            </Button>
                        ) : (
                            <Button
                                color="inherit"
                                onClick={handleBack}
                                sx={{
                                    mr: 1,
                                    borderRadius: "10px",
                                    color: grey[600],
                                    textTransform: "none",
                                    display: activeStep === steps.length - 1 && "none",
                                }}
                                startIcon={<ArrowBackIcon />}
                            >
                                Trở lại
                            </Button>
                        )}
                        <Box sx={{ flex: "1 1 auto" }} />
                        <Button
                            onClick={handleNext}
                            sx={{ borderRadius: "20px", display: "none" }}
                            endIcon={activeStep < steps.length - 1 ? <ArrowForwardIcon /> : <DoneIcon />}
                            variant="outlined"
                        >
                            {activeStep === steps.length - 1 ? "Kết thúc" : steps[activeStep + 1]}
                        </Button>
                    </Box>
                </React.Fragment>
            )}
        </Box>
    );
}

function Cart({ user, setUser, cartItem, setCartItem }) {
    const navigate = useNavigate();
    const [cartInfo, setCartInfo] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [coupon, setCoupon] = React.useState({
        code: "",
        discount: 0,
    });
    const [cartIsEmpty, setCartIsEmpty] = React.useState(cartItem.length > 0 ? false : true);

    useDocumentTitle(cartInfo?.short_id ? `Giỏ hàng - #${cartInfo.short_id} | BeE` : "Giỏ hàng | BeE");

    React.useEffect(() => {
        const fetchData = async () => {
            /* Check if item:
                - item is disabled/removed => set cart item quantity to 0
                - item is out of stock => set cart item quantity to 0
                - item has inventory < user added => set cart item quantity to inventory
            */
            setCartItem(
                cartItem.map((item) => {
                    let adjustedQuantity =
                        item.quantity > item.inventory_quantity
                            ? item.inventory_quantity // Set to available stock if the requested quantity is too high
                            : item.quantity;

                    if (!item.visible && item.quantity > 0) {
                        alert(`Rất tiếc, sản phẩm ${item.name} - ${item.sku} đã không còn tồn tại!`);
                        adjustedQuantity = 0;
                    } else if (item.quantity > item.inventory_quantity) {
                        if (item.inventory_quantity > 0) {
                            alert(`Sản phẩm ${item.name} - ${item.sku} chỉ còn ${item.inventory_quantity} sản phẩm!`);
                        } else {
                            alert(`Rất tiếc, sản phẩm ${item.name} - ${item.sku} đã hết hàng!`);
                        }
                    }
                    return {
                        ...item,
                        quantity: adjustedQuantity,
                    };
                })
            );
            try {
                const response = await axiosInstance.get("/api/shopping/cart/view");
                setCartInfo({
                    user: "",
                    id: response.data.id,
                    short_id: response.data.short_id,
                    name: response.data.customer_name === "" ? user.name : response.data.customer_name,
                    phone: response.data.customer_phone === "" ? user.phone : response.data.customer_phone,
                    address: response.data.customer_address === "" ? user.address : response.data.customer_address,
                    ward: response.data.customer_ward === "" ? user.ward : response.data.customer_ward,
                    district: response.data.customer_district === "" ? user.district : response.data.customer_district,
                    province: response.data.customer_province === "" ? user.province : response.data.customer_province,
                    note: response.data.customer_note,
                });
                if (response.data.length > 0) {
                    setDoneCheckout(false);
                }
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, []);

    const align_center = {
        display: "flex",
        justifyContent: "center",
        padding: "0.75rem",
        minHeight: "100vh",
    };

    const w_1200 = { maxWidth: "1200px", width: "100%" };

    if (loading) return <Loading />;

    return (
        <div
            className="App"
            style={{
                backgroundColor: "#fafafa",
            }}
        >
            <HomeAppBar cartItem={cartItem} user={user} setUser={setUser} />
            <section style={align_center}>
                <div style={w_1200}>
                    <Box sx={{ width: "100%", flexGrow: 1, mt: "100px" }}>
                        {!cartIsEmpty ? (
                            <HorizontalLinearStepper
                                cartItem={cartItem}
                                setCartItem={setCartItem}
                                cartInfo={cartInfo}
                                setCartInfo={setCartInfo}
                                coupon={coupon}
                                setCoupon={setCoupon}
                            />
                        ) : (
                            <>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                    }}
                                >
                                    <ShoppingBagIcon
                                        sx={{
                                            fontSize: "100px",
                                            mb: "10px",
                                            color: "#1a237e",
                                        }}
                                    />
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        color: grey[700],
                                        mb: "20px",
                                    }}
                                >
                                    <Typography
                                        sx={{
                                            fontWeight: 350,
                                            fontSize: "30px",
                                        }}
                                    >
                                        Không có sản phẩm nào trong giỏ hàng của bạn
                                    </Typography>
                                </Box>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "center",
                                        color: grey[700],
                                    }}
                                >
                                    <CustomButton
                                        startIcon={<ArrowBackIcon />}
                                        variant="contained"
                                        onClick={() => {
                                            navigate("/shop");
                                        }}
                                    >
                                        Tiếp tục mua sắm
                                    </CustomButton>
                                </Box>
                            </>
                        )}
                    </Box>
                </div>
            </section>
            <Footer />
        </div>
    );
}

export default Cart;
