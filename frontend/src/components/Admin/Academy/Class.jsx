import React, { useState, useEffect } from "react";
import { useDocumentTitle } from "../../../hooks/useDocumentTitle";
import AdminLayout from "../Common/AdminLayout2";
import Grid from "@mui/material/Grid2";
import {
    Box,
    Typography,
    Card,
    CardContent,
    CardActions,
    Button,
    Modal,
    TextField,
    Divider,
    IconButton,
    CardHeader,
    Avatar,
    Chip,
    Autocomplete,
    ListItemIcon,
    Menu,
    MenuItem,
} from "@mui/material";
import MoreVertIcon from '@mui/icons-material/MoreVert';
import Fade from '@mui/material/Fade';
import DirectionsRunIcon from '@mui/icons-material/DirectionsRun';
import NoteAddIcon from '@mui/icons-material/NoteAdd';
import PauseIcon from '@mui/icons-material/Pause';
import { styled } from "@mui/material/styles";
import AddIcon from "@mui/icons-material/Add";
import EditIcon from "@mui/icons-material/Edit";
import DeleteIcon from "@mui/icons-material/Delete";
import CheckIcon from "@mui/icons-material/Check";
import CalendarMonthIcon from "@mui/icons-material/CalendarMonth";
import ClassIcon from "@mui/icons-material/Class";
import PersonIcon from "@mui/icons-material/Person";
import RoomIcon from "@mui/icons-material/Room";
import { green, red, blue, grey, purple, orange } from "@mui/material/colors";
import axiosInstance from "../../../services/axiosInstance";
import { DatePicker } from "@mui/x-date-pickers/DatePicker";
import { LocalizationProvider } from "@mui/x-date-pickers/LocalizationProvider";
import { AdapterDayjs } from "@mui/x-date-pickers/AdapterDayjs";
import dayjs from "dayjs";

const CustomTextField = styled(TextField)({
    "& .MuiOutlinedInput-root": {
        borderRadius: "10px",
    },
});

const CustomButton = styled(Button)({
    borderRadius: "10px",
    textTransform: "none",
});

function formatPrice(number) {
    return new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "VND",
    }).format(number);
}

function ClassCard({ classData, handleEdit, handleDelete, handleChangeStatus }) {
    const [anchorEl, setAnchorEl] = React.useState(null);
    const open = Boolean(anchorEl);

    const handleClick = (event) => {
        setAnchorEl(event.currentTarget);
    };
    const handleClose = () => {
        setAnchorEl(null);
    };

    return (
        <Card sx={{ borderRadius: "15px", height: "100%" }}>
            <CardHeader
                avatar={
                    <Avatar sx={{ bgcolor: blue[500] }}>
                        <ClassIcon />
                    </Avatar>
                }
                title={classData.name}
                subheader={`Bắt đầu: ${new Date(classData.start_date).toLocaleDateString()}`}
                action={
                    !classData.completed && (
                        <Box>
                            <IconButton
                                id="option-button"
                                aria-controls={open ? 'option-menu' : undefined}
                                aria-haspopup="true"
                                aria-expanded={open ? 'true' : undefined}
                                onClick={handleClick}
                            >
                                <MoreVertIcon />
                            </IconButton>
                            <Menu
                                id="option-menu"
                                slotProps={{
                                    list: {
                                        'aria-labelledby': 'option-button',
                                    },
                                }}
                                slots={{ transition: Fade }}
                                anchorEl={anchorEl}
                                open={open}
                                onClose={handleClose}
                            >
                                <MenuItem onClick={() => {
                                    handleEdit(classData.id);
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <EditIcon fontSize="small" sx={{ color: blue[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Chỉnh sửa</Typography>
                                </MenuItem>
                                <Divider />
                                <MenuItem onClick={() => {
                                    handleChangeStatus(classData.id, "Chuẩn bị mở");
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <NoteAddIcon fontSize="small" sx={{ color: orange[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Chuẩn bị mở</Typography>
                                </MenuItem>
                                <MenuItem onClick={() => {
                                    handleChangeStatus(classData.id, "Đang học");
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <DirectionsRunIcon fontSize="small" sx={{ color: purple[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Đang học</Typography>
                                </MenuItem>
                                <MenuItem onClick={() => {
                                    handleChangeStatus(classData.id, "Hoàn thành");
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <CheckIcon fontSize="small" sx={{ color: green[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Hoàn thành</Typography>
                                </MenuItem>
                                <MenuItem onClick={() => {
                                    handleChangeStatus(classData.id, "Đã hủy");
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <PauseIcon fontSize="small" sx={{ color: grey[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Hủy lớp</Typography>
                                </MenuItem>
                                <Divider />
                                <MenuItem onClick={() => {
                                    handleDelete(classData.id);
                                    setAnchorEl(null);
                                }}>
                                    <ListItemIcon>
                                        <DeleteIcon fontSize="small" sx={{ color: red[600] }} />
                                    </ListItemIcon>
                                    <Typography variant="inherit">Xóa</Typography>
                                </MenuItem>
                            </Menu>
                        </Box>
                    )
                }
            />
            <CardContent>
                <Chip
                    size="small"
                    label={classData.status}
                    color={classData.completed ? "success" : "error"}
                    variant="outlined"
                    sx={{ mb: 1 }}
                />
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <PersonIcon sx={{ mr: 1, color: blue[500] }} />
                    <Typography variant="body2">
                        Giáo viên: {classData.teacher ? classData.teacher.name : "Chưa phân công"}
                    </Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <RoomIcon sx={{ mr: 1, color: blue[500] }} />
                    <Typography variant="body2">Phòng: {classData.room}</Typography>
                </Box>
                <Box sx={{ display: "flex", alignItems: "center", mb: 1 }}>
                    <CalendarMonthIcon sx={{ mr: 1, color: blue[500] }} />
                    {classData.discount === 0 ? (
                        <Typography variant="body2">
                            Số buổi học: {classData.number_of_lessons} ({formatPrice(classData.fee)})
                        </Typography>
                    ) : (
                        <Box sx={{ display: "flex", alignItems: "center" }}>
                            <Typography variant="body2">Số buổi học: {classData.number_of_lessons} (</Typography>
                            <Typography variant="body2" sx={{ textDecoration: "line-through", mr: 1 }}>
                                {formatPrice(classData.fee)}
                            </Typography>
                            <Typography variant="body2">{formatPrice(classData.fee - classData.discount)})</Typography>
                        </Box>
                    )}
                </Box>
                <Typography variant="body2" sx={{ mt: 2, fontWeight: "bold" }}>
                    Lịch học:
                </Typography>
                <Box sx={{ display: "flex", flexWrap: "wrap", gap: 1, mt: 1 }}>
                    {classData.schedule.map((scheduleItem, index) => (
                        <Chip
                            key={index}
                            label={`${scheduleItem.day}: ${scheduleItem.start} - ${scheduleItem.end}`}
                            size="small"
                            color="primary"
                        />
                    ))}
                </Box>
            </CardContent>
        </Card>
    );
}

function ClassModal({ open, handleClose, isEditing, editingClass, teachers, setRequestRefreshClasses }) {
    const [classData, setClassData] = useState({
        name: "",
        start_date: dayjs(),
        number_of_lessons: 0,
        room: "",
        teacher: "",
        teacher_id: "",
        fee: 0,
        discount: 0,
        schedule: [{ day: "T2", start: "17:30", end: "19:00" }],
        completed: false
    });

    useEffect(() => {
        if (isEditing && editingClass) {
            // Đảm bảo schedule là một mảng
            const schedule = Array.isArray(editingClass.schedule)
                ? editingClass.schedule
                : typeof editingClass.schedule === "string"
                    ? JSON.parse(editingClass.schedule)
                    : [{ day: "T2", start: "17:30", end: "19:00" }];

            setClassData({
                name: editingClass.name || "",
                start_date: dayjs(editingClass.start_date) || dayjs(),
                number_of_lessons: editingClass.number_of_lessons || 0,
                room: editingClass.room || "",
                teacher_id: editingClass.teacher ? editingClass.teacher.id : null,
                schedule: schedule,
                fee: editingClass.fee || 0,
                discount: editingClass.discount || 0,
            });
        } else {
            setClassData({
                name: "",
                start_date: dayjs(),
                number_of_lessons: 0,
                room: "",
                teacher_id: null,
                schedule: [{ day: "T2", start: "17:30", end: "19:00" }],
                fee: 0,
                discount: 0,
            });
        }
    }, [isEditing, editingClass]);

    const handleChange = (e) => {
        const { name, value } = e.target;
        setClassData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleDateChange = (date) => {
        setClassData((prev) => ({
            ...prev,
            start_date: date,
        }));
    };

    const handleScheduleChange = (index, field, value) => {
        const newSchedule = [...classData.schedule];
        newSchedule[index] = { ...newSchedule[index], [field]: value };
        setClassData((prev) => ({
            ...prev,
            schedule: newSchedule,
        }));
    };

    const addScheduleItem = () => {
        setClassData((prev) => ({
            ...prev,
            schedule: [...prev.schedule, { day: "T2", start: "17:30", end: "19:00" }],
        }));
    };

    const removeScheduleItem = (index) => {
        const newSchedule = [...classData.schedule];
        newSchedule.splice(index, 1);
        setClassData((prev) => ({
            ...prev,
            schedule: newSchedule,
        }));
    };

    const handleSubmit = () => {
        const formData = new FormData();
        formData.append("name", classData.name);
        formData.append("start_date", classData.start_date.format("YYYY-MM-DD"));
        formData.append("number_of_lessons", classData.number_of_lessons);
        formData.append("room", classData.room);
        formData.append("fee", classData.fee);
        formData.append("discount", classData.discount);

        // Kiểm tra và gửi teacher_id nếu có
        if (classData.teacher_id) {
            formData.append("teacher_id", classData.teacher_id);
        }

        formData.append("schedule", JSON.stringify(classData.schedule));

        if (isEditing && editingClass) {
            axiosInstance
                .patch(`/api/academy/class/${editingClass.id}`, formData)
                .then((response) => {
                    if (response.status === 200) {
                        handleClose();
                        setRequestRefreshClasses(true);
                    }
                })
                .catch((error) => {
                    console.error("Error updating class:", error.response ? error.response.data : error);
                });
        } else {
            axiosInstance
                .post("/api/academy/class/", formData)
                .then((response) => {
                    if (response.status === 201) {
                        handleClose();
                        setRequestRefreshClasses(true);
                        setClassData({
                            name: "",
                            start_date: dayjs(),
                            number_of_lessons: 0,
                            room: "",
                            teacher_id: null,
                            fee: 0,
                            discount: 0,
                            schedule: [{ day: "T2", start: "17:30", end: "19:00" }],
                        });
                    }
                })
                .catch((error) => {
                    console.error(error);
                });
        }
    };

    const style = {
        position: "absolute",
        top: "50%",
        left: "50%",
        transform: "translate(-50%, -50%)",
        width: 500,
        maxHeight: "90vh",
        overflow: "auto",
        bgcolor: "background.paper",
        border: "none",
        boxShadow: 24,
        p: 4,
        borderRadius: "20px",
    };

    return (
        <Modal open={open} onClose={handleClose} sx={{ borderRadius: "20px", border: "none" }}>
            <Box sx={style}>
                <Typography id="modal-modal-title" variant="h6" component="h2" sx={{ mb: 2 }}>
                    {isEditing ? "Chỉnh sửa lớp học" : "Tạo lớp học mới"}
                </Typography>
                <Divider sx={{ mb: 2 }} />

                <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                    <CustomTextField
                        label="Tên lớp"
                        name="name"
                        value={classData.name}
                        onChange={handleChange}
                        fullWidth
                        required
                    />

                    <LocalizationProvider dateAdapter={AdapterDayjs}>
                        <DatePicker
                            label="Ngày bắt đầu"
                            value={classData.start_date}
                            onChange={handleDateChange}
                            // renderInput={(params) => <CustomTextField {...params} fullWidth required />}
                            slots={{
                                textField: CustomTextField,
                            }}
                            slotProps={{
                                textField: {
                                    fullWidth: true,
                                    required: true,
                                    // size: "small",
                                },
                            }}
                        />
                    </LocalizationProvider>

                    <CustomTextField
                        label="Số buổi học"
                        name="number_of_lessons"
                        type="number"
                        value={classData.number_of_lessons}
                        onChange={handleChange}
                        fullWidth
                        required
                    />

                    <CustomTextField
                        label="Phòng học"
                        name="room"
                        value={classData.room}
                        onChange={handleChange}
                        fullWidth
                        required
                    />

                    <CustomTextField
                        label={"Học phí: " + formatPrice(classData.fee)}
                        name="fee"
                        type="number"
                        value={classData.fee}
                        onChange={handleChange}
                        fullWidth
                        required
                    />

                    <CustomTextField
                        label={"Giảm giá: " + formatPrice(classData.discount)}
                        name="discount"
                        type="number"
                        value={classData.discount}
                        onChange={handleChange}
                        fullWidth
                        required
                    />

                    <Autocomplete
                        options={teachers}
                        getOptionLabel={(option) => option.name}
                        value={teachers.find((teacher) => teacher.id === classData.teacher_id) || null}
                        onChange={(event, newValue) => {
                            setClassData({
                                ...classData,
                                teacher_id: newValue ? newValue.id : null,
                            });
                        }}
                        renderInput={(params) => <CustomTextField {...params} label="Giáo viên" />}
                    />

                    <Typography variant="subtitle1" sx={{ mt: 1 }}>
                        Lịch học
                    </Typography>

                    {classData.schedule.map((scheduleItem, index) => (
                        <Box key={index} sx={{ display: "flex", gap: 1, alignItems: "center" }}>
                            <CustomTextField
                                select
                                label="Thứ"
                                value={scheduleItem.day}
                                onChange={(e) => handleScheduleChange(index, "day", e.target.value)}
                                sx={{ width: "30%" }}
                                SelectProps={{
                                    native: true,
                                }}
                            >
                                <option value="T2">Thứ 2</option>
                                <option value="T3">Thứ 3</option>
                                <option value="T4">Thứ 4</option>
                                <option value="T5">Thứ 5</option>
                                <option value="T6">Thứ 6</option>
                                <option value="T7">Thứ 7</option>
                                <option value="CN">Chủ nhật</option>
                            </CustomTextField>

                            <CustomTextField
                                label="Bắt đầu"
                                type="time"
                                value={scheduleItem.start}
                                onChange={(e) => handleScheduleChange(index, "start", e.target.value)}
                                sx={{ width: "30%" }}
                                InputLabelProps={{ shrink: true }}
                                inputProps={{ step: 300 }}
                            />

                            <CustomTextField
                                label="Kết thúc"
                                type="time"
                                value={scheduleItem.end}
                                onChange={(e) => handleScheduleChange(index, "end", e.target.value)}
                                sx={{ width: "30%" }}
                                InputLabelProps={{ shrink: true }}
                                inputProps={{ step: 300 }}
                            />

                            <IconButton
                                color="error"
                                onClick={() => removeScheduleItem(index)}
                                disabled={classData.schedule.length <= 1}
                            >
                                <DeleteIcon />
                            </IconButton>
                        </Box>
                    ))}

                    <Button startIcon={<AddIcon />} onClick={addScheduleItem} sx={{ alignSelf: "flex-start" }}>
                        Thêm lịch học
                    </Button>
                </Box>

                <Box sx={{ display: "flex", justifyContent: "space-between", mt: 3 }}>
                    <CustomButton variant="contained" onClick={handleSubmit} startIcon={<CheckIcon />}>
                        {isEditing ? "Cập nhật" : "Lưu"}
                    </CustomButton>
                    <Button color="inherit" onClick={handleClose}>
                        Hủy
                    </Button>
                </Box>
            </Box>
        </Modal>
    );
}

function Class({ user }) {
    useDocumentTitle("Quản lý lớp học | BeE");

    const [loading, setLoading] = useState(true);
    const [classes, setClasses] = useState([]);
    const [filteredClasses, setFilteredClasses] = useState([]);
    const [teachers, setTeachers] = useState([]);
    const [open, setOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editingClass, setEditingClass] = useState(null);
    const [requestRefreshClasses, setRequestRefreshClasses] = useState(false);

    const handleOpen = () => setOpen(true);
    const handleClose = () => {
        setOpen(false);
        setIsEditing(false);
        setEditingClass(null);
    };

    useEffect(() => {
        const fetchData = async () => {
            try {
                const [classesResponse, teachersResponse] = await Promise.all([
                    axiosInstance.get("/api/academy/class/"),
                    axiosInstance.get("/api/academy/teacher/"),
                ]);
                const processedClasses = classesResponse.data.map((classItem) => ({
                    ...classItem,
                    schedule:
                        typeof classItem.schedule === "string"
                            ? JSON.parse(classItem.schedule)
                            : Array.isArray(classItem.schedule)
                                ? classItem.schedule
                                : [],
                }));
                setClasses(processedClasses);
                setFilteredClasses(processedClasses);
                setTeachers(teachersResponse.data);
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
                setRequestRefreshClasses(false);
            }
        };
        fetchData();
    }, [requestRefreshClasses]);

    const handleEdit = (id) => {
        const classToEdit = classes.find((c) => c.id === id);
        if (classToEdit) {
            setEditingClass(classToEdit);
            setIsEditing(true);
            handleOpen();
        }
    };

    const handleDelete = (id) => {
        if (window.confirm("Bạn có chắc chắn muốn xóa lớp học này?")) {
            axiosInstance
                .delete(`/api/academy/class/${id}/`)
                .then(() => {
                    setRequestRefreshClasses(true);
                })
                .catch((error) => {
                    console.error("Error deleting class:", error);
                });
        }
    };

    const handleChangeStatus = (id, status) => {
        if (window.confirm(`Bạn có chắc chắn chuyển khóa học sang trạng thái ${status}?`)) {
            const formData = new FormData();
            if (status === "Hoàn thành") {
                formData.append("completed", true);
            }
            formData.append("status", status);
            axiosInstance
                .patch(`/api/academy/class/${id}`, formData)
                .then(() => {
                    setRequestRefreshClasses(true);
                })
                .catch((error) => {
                    console.error("Error updating class:", error);
                });
        }
    }

    const handleSearchClass = (event) => {
        const keyword = event.target.value.toLowerCase();
        if (keyword === "") {
            setFilteredClasses(classes);
        } else {
            const filtered = classes.filter(
                (c) =>
                    (c.name && c.name.toLowerCase().includes(keyword)) ||
                    (c.room && c.room.toLowerCase().includes(keyword)) ||
                    (c.teacher && c.teacher.name && c.teacher.name.toLowerCase().includes(keyword))
            );
            setFilteredClasses(filtered);
        }
    };

    return (
        <AdminLayout title="Quản lý lớp học" user={user}>
            <Box sx={{ display: "flex", gap: 2, flexDirection: "column" }}>
                <Box sx={{ display: "flex", justifyContent: "space-between", mb: "20px" }}>
                    <CustomTextField
                        sx={{ width: { xs: "100%", md: "300px" } }}
                        label="Tìm kiếm"
                        variant="outlined"
                        size="small"
                        onChange={handleSearchClass}
                        placeholder="Tìm theo tên lớp, phòng, giáo viên..."
                    />
                    <CustomButton variant="contained" onClick={handleOpen} startIcon={<AddIcon />}>
                        Thêm lớp học
                    </CustomButton>
                </Box>

                {loading ? (
                    <Typography>Đang tải...</Typography>
                ) : (
                    <Grid container spacing={3}>
                        {filteredClasses.map((classData) => (
                            <Grid size={{ xs: 12, md: 6, lg: 4 }} key={classData.id}>
                                <ClassCard
                                    classData={classData}
                                    handleEdit={handleEdit}
                                    handleDelete={handleDelete}
                                    handleChangeStatus={handleChangeStatus}
                                />
                            </Grid>
                        ))}
                    </Grid>
                )}
            </Box>

            <ClassModal
                open={open}
                handleClose={handleClose}
                isEditing={isEditing}
                editingClass={editingClass}
                teachers={teachers}
                setRequestRefreshClasses={setRequestRefreshClasses}
            />
        </AdminLayout>
    );
}

export default Class;
