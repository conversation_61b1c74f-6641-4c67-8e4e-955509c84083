import { Box, Button, Typography } from "@mui/material";
import React from "react";
import ArrowBackIcon from "@mui/icons-material/ArrowBack";
import { grey } from "@mui/material/colors";
import { useNavigate } from "react-router-dom";
import { useDocumentTitle } from "../hooks/useDocumentTitle";

function PageNotFound() {
    useDocumentTitle("Page Not Found");
    const navigate = useNavigate();
    const handelBack = () => {
        navigate("/");
    };
    return (
        <Box
            sx={{
                display: "flex",
                justifyContent: "center",
                alignItems: "center",
                minHeight: "100vh",
            }}
        >
            <Box
                sx={{
                    display: "flex",
                    flexDirection: "column",
                    justifyContent: "center",
                }}
            >
                <Typography variant="h5" sx={{ fontWeight: 500, mb: "40px", textAlign: "center" }}>
                    Xin lỗi, chúng tôi không tìm thấy trang web này!
                </Typography>
                <img src="/assets/404.png" style={{ maxWidth: "600px", marginBottom: "40px" }} />
                <Box sx={{ display: "flex", justifyContent: "center" }}>
                    <Button
                        variant="contained"
                        startIcon={<ArrowBackIcon />}
                        sx={{
                            borderRadius: "10px",
                            backgroundColor: grey[600],
                            "&:hover": { backgroundColor: grey[800] },
                        }}
                        onClick={() => {
                            handelBack();
                        }}
                    >
                        Trở về
                    </Button>
                </Box>
            </Box>
        </Box>
    );
}

export default PageNotFound;
