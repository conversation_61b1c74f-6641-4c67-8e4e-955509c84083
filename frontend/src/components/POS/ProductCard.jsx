import * as React from "react";

import PropTypes from "prop-types";
import {
    <PERSON><PERSON><PERSON>,
    <PERSON>ton,
    CardContent,
    CardMedia,
    Box,
    Chip,
    Paper,
    Card,
    CardActions,
    IconButton,
} from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import { grey, red } from "@mui/material/colors";

ProductCard.propTypes = {
    item: PropTypes.object.isRequired,
};

export default function ProductCard({ item, cartList, setCartList, activeCart }) {
    function formatPrice(number) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "VND",
        }).format(number);
    }

    function handleAddToCart(item) {
        const item_exist = cartList.find((cartItem) => cartItem.item.sku === item.sku);
        if (item_exist) {
            setCartList(
                cartList.map((cartItem) => {
                    return cartItem.item.sku === item.sku ? { ...cartItem, quantity: cartItem.quantity + 1 } : cartItem;
                })
            );
        } else {
            setCartList([...cartList, { item: item, quantity: 1 }]);
        }
    }

    return (
        <>
            <Paper
                elevation={2}
                sx={{
                    display: "flex",
                    borderRadius: "15px",
                    height: "100%",
                    overflow: "hidden",
                }}
            >
                <Card sx={{ width: "100%", cursor: "pointer" }} onClick={() => handleAddToCart(item)}>
                    <CardMedia
                        component="img"
                        alt={item.name}
                        height="100"
                        image={item.images.length > 0 ? item.images[0].image_url : null}
                        style={{
                            width: "100%",
                            height: "150px",
                            objectFit: "cover",
                        }}
                    />
                    <Box
                        sx={{
                            padding: "10px",
                            display: "flex",
                            flexDirection: "column",
                            paddingBottom: 0,
                            position: "relative",
                        }}
                    >
                        <Typography>
                            {item.name.length > 25 ? item.name.substring(0, 25) + "..." : item.name}
                        </Typography>
                        <Typography sx={{ color: grey[500], fontSize: "12px" }}>
                            #{item.sku} ({item.quantity})
                        </Typography>
                        <Box sx={{ mb: 1 }}>
                            {item.attributes.map((attr, index) => (
                                <Chip
                                    key={index}
                                    label={`${attr.key}: ${attr.value}`}
                                    style={{ mr: "5px" }}
                                    size="small"
                                />
                            ))}
                        </Box>
                        {item.discount_value > 0 ? (
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: "-20px",
                                    left: "-5px",
                                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                                    borderTopRightRadius: "10px",
                                    paddingLeft: "10px",
                                    paddingRight: "10px",
                                }}
                            >
                                <Box sx={{ display: "flex", alignItems: "baseline", mb: "2px" }}>
                                    <Typography
                                        sx={{
                                            fontSize: "16px",
                                            fontWeight: 500,
                                            color: red[500],
                                            mr: "5px",
                                        }}
                                    >
                                        {formatPrice(item.final_price)}
                                    </Typography>
                                    <Typography sx={{ fontSize: "13px", fontWeight: 100, color: grey[600] }}>
                                        <del>{formatPrice(item.sale_price)}</del>
                                    </Typography>
                                </Box>
                                {/* <Chip label={item.promotion[0].title} size="small" /> */}
                            </Box>
                        ) : (
                            <Box
                                sx={{
                                    position: "absolute",
                                    top: "-20px",
                                    left: "-5px",
                                    backgroundColor: "rgba(255, 255, 255, 0.8)",
                                    borderTopRightRadius: "10px",
                                    paddingLeft: "10px",
                                    paddingRight: "10px",
                                }}
                            >
                                <Typography
                                    sx={{
                                        fontSize: "16px",
                                        fontWeight: 500,
                                        color: red[500],
                                    }}
                                >
                                    {formatPrice(item.sale_price)}
                                </Typography>
                            </Box>
                        )}
                    </Box>
                </Card>
            </Paper>
        </>
    );
}
