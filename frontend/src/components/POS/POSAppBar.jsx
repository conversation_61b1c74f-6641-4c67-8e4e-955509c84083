import * as React from "react";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Toolbar from "@mui/material/Toolbar";
import IconButton from "@mui/material/IconButton";
import Typography from "@mui/material/Typography";
import Menu from "@mui/material/Menu";
import Container from "@mui/material/Container";
import Avatar from "@mui/material/Avatar";
import Button from "@mui/material/Button";
import Tooltip from "@mui/material/Tooltip";
import MenuItem from "@mui/material/MenuItem";
import { Autocomplete, Divider, InputAdornment, ToggleButton, ToggleButtonGroup } from "@mui/material";
import AccountCircleIcon from "@mui/icons-material/AccountCircle";
import LogoutIcon from "@mui/icons-material/Logout";
import { deepOrange, grey } from "@mui/material/colors";
import authService from "../../services/authService";
import { useNavigate } from "react-router-dom";
import CustomTextField from "../Common/CustomTextField";
import DashboardIcon from "@mui/icons-material/Dashboard";
import axiosInstance from "../../services/axiosInstance";
import QrCodeIcon from "@mui/icons-material/QrCode";
import KeyboardIcon from "@mui/icons-material/Keyboard";

const settings = [
    {
        title: "Cá nhân",
        link: "/profile",
        icon: <AccountCircleIcon />,
    },
    {
        title: "Dashboard",
        link: "/admin/dashboard",
        icon: <DashboardIcon />,
    },
];

export default function POSAppBar({ user, setUser, searchProduct, setSearchProduct, filterType, setFilterType }) {
    const navigate = useNavigate();

    const [anchorElUser, setAnchorElUser] = React.useState(null);

    const handleOpenUserMenu = (event) => {
        setAnchorElUser(event.currentTarget);
    };

    const handleCloseUserMenu = () => {
        setAnchorElUser(null);
    };

    const handleSearchProduct = (event) => {
        if (event.keyCode === 13) {
            setSearchProduct(event.target.value);
            event.target.value = "";
        }
    };

    const handleInput = (event, newInput) => {
        setFilterType(newInput);
    };

    return (
        <AppBar
            position="fixed"
            sx={{
                background: "linear-gradient(135deg, #1a237e 0%, #0d47a1 100%)",
                maxWidth: "100% !important",
                width: "100%",
            }}
        >
            <Container
                sx={{
                    display: "flex",
                    justifyContent: "center",
                    maxWidth: "100% !important",
                    width: "100%",
                }}
            >
                <Toolbar disableGutters sx={{ maxWidth: "100%", width: "100%" }}>
                    <img
                        alt="beeblock"
                        src="/beeIco.svg"
                        style={{ width: "56px", cursor: "pointer" }}
                        onClick={() => navigate("/")}
                    />
                    <Typography
                        variant="h6"
                        noWrap
                        component="a"
                        href="/"
                        sx={{
                            ml: 1,
                            mr: 1,
                            display: { xs: "none", md: "flex" },
                            color: "inherit",
                            textDecoration: "none",
                            textAlign: "center",
                            verticalAlign: "center",
                        }}
                    >
                        BeE STEM Solutions
                    </Typography>
                    <Box sx={{ flexGrow: 1, ml: 1, alignItems: "center" }}>
                        <CustomTextField
                            sx={{ width: { xs: "300px", md: "400px" } }}
                            size="small"
                            placeholder={filterType === "input_keyboard" ? "Tìm kiếm sản phẩm" : "Quét Barcode"}
                            slotProps={{
                                input: {
                                    style: {
                                        backgroundColor: "white",
                                        paddingLeft: "5px",
                                    },
                                    startAdornment: (
                                        <InputAdornment position="start" sx={{ cursor: "pointer" }}>
                                            <ToggleButtonGroup
                                                value={filterType}
                                                exclusive
                                                onChange={handleInput}
                                                aria-label="filter product"
                                                size="small"
                                                sx={{ height: "30px" }}
                                            >
                                                <ToggleButton
                                                    value="input_keyboard"
                                                    aria-label="input keyboard"
                                                    sx={{ borderRadius: "8px" }}
                                                >
                                                    <KeyboardIcon />
                                                </ToggleButton>
                                                <ToggleButton
                                                    value="input_scan"
                                                    aria-label="input scan"
                                                    sx={{ borderRadius: "8px" }}
                                                >
                                                    <QrCodeIcon />
                                                </ToggleButton>
                                            </ToggleButtonGroup>
                                            <Divider
                                                orientation="vertical"
                                                variant="middle"
                                                flexItem
                                                sx={{
                                                    ml: "10px",
                                                    height: "30px",
                                                    transform: "translateY(-6px)",
                                                }}
                                            />
                                        </InputAdornment>
                                    ),
                                },
                            }}
                            autoFocus
                            defaultValue={searchProduct}
                            onKeyDown={handleSearchProduct}
                        />
                    </Box>
                    <Box
                        sx={{
                            flexGrow: 0,
                            display: { xs: "none", md: "flex" },
                        }}
                    >
                        <Divider orientation="vertical" sx={{ mr: "10px" }} flexItem />
                        {user ? (
                            <Box sx={{ display: "flex", alignItems: "center" }}>
                                <Typography variant="h4" sx={{ fontSize: 14, mr: "10px" }}>
                                    Chào, {user.last_name}
                                </Typography>
                                <Tooltip title="Open settings">
                                    <IconButton onClick={handleOpenUserMenu} sx={{ p: 0 }}>
                                        {user.avatar_url ? (
                                            <Avatar
                                                alt={user.username}
                                                src={user.avatar_url}
                                                sx={{
                                                    bgcolor: "none",
                                                    border: "1px solid #ebebeb",
                                                }}
                                            />
                                        ) : (
                                            <Avatar
                                                alt={user.username}
                                                sx={{
                                                    bgcolor: deepOrange[500],
                                                }}
                                            />
                                        )}
                                    </IconButton>
                                </Tooltip>
                                <Menu
                                    sx={{
                                        // mt: '45px',
                                        // transform: "translate(0px, 15px)",
                                        "& .MuiPaper-root": {
                                            borderRadius: "10px",
                                        },
                                    }}
                                    id="menu-appbar"
                                    anchorEl={anchorElUser}
                                    slotProps={{
                                        paper: {
                                            elevation: 0,
                                            sx: {
                                                // width: "200px",
                                                overflow: "visible",
                                                filter: "drop-shadow(0px 2px 8px rgba(0,0,0,0.32))",
                                                mt: 1.5,
                                                "& .MuiAvatar-root": {
                                                    width: 32,
                                                    height: 32,
                                                    ml: -0.5,
                                                    mr: 1,
                                                },
                                                "&::before": {
                                                    content: '""',
                                                    display: "block",
                                                    position: "absolute",
                                                    top: 0,
                                                    right: 14,
                                                    width: 10,
                                                    height: 10,
                                                    bgcolor: "background.paper",
                                                    transform: "translateY(-50%) rotate(45deg)",
                                                    zIndex: 0,
                                                },
                                            },
                                        },
                                    }}
                                    transformOrigin={{
                                        horizontal: "right",
                                        vertical: "top",
                                    }}
                                    anchorOrigin={{
                                        horizontal: "right",
                                        vertical: "bottom",
                                    }}
                                    // anchorOrigin={{
                                    //     vertical: 'top',
                                    //     horizontal: 'right',
                                    // }}
                                    keepMounted
                                    // transformOrigin={{
                                    //     vertical: 'top',
                                    //     horizontal: 'right',
                                    // }}
                                    open={Boolean(anchorElUser)}
                                    onClose={handleCloseUserMenu}
                                >
                                    {settings.map((setting) => (
                                        <MenuItem key={setting.title} onClick={handleCloseUserMenu}>
                                            <Button
                                                sx={{
                                                    color: grey[600],
                                                    textTransform: "none",
                                                }}
                                                href={setting.link}
                                                size="small"
                                                startIcon={setting.icon}
                                            >
                                                {setting.title}
                                            </Button>
                                        </MenuItem>
                                    ))}
                                    <Divider></Divider>
                                    <MenuItem>
                                        <Button
                                            onClick={() => {
                                                authService.logout();
                                                setUser(null);
                                                navigate("/");
                                            }}
                                            size="small"
                                            sx={{
                                                color: grey[600],
                                                textTransform: "none",
                                            }}
                                            startIcon={<LogoutIcon />}
                                        >
                                            Đăng xuất
                                        </Button>
                                    </MenuItem>
                                </Menu>
                            </Box>
                        ) : (
                            <Button
                                href="/login"
                                sx={{
                                    color: "white",
                                    display: "block",
                                    mr: "10px",
                                    border: "1px solid white",
                                    borderRadius: "10px",
                                    "&:hover": {
                                        backgroundColor: "transparent",
                                    },
                                }}
                            >
                                Đăng nhập
                            </Button>
                            // <Link href='/login'>Đăng nhập</Link>
                        )}
                    </Box>
                </Toolbar>
            </Container>
        </AppBar>
    );
}
