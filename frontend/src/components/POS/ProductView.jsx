import React from "react";
import { Box, List, Typography } from "@mui/material";
import ProductCard from "./ProductCard";
import { grey } from "@mui/material/colors";
import Grid from "@mui/material/Grid2";

function ProductView({ itemList, cartList, setCartList, activeCart }) {
    return (
        <Box sx={{ display: "flex", flexDirection: "column" }}>
            {/* <List
                sx={{
                    width: '100%',
                    bgcolor: grey[100],
                    position: 'relative',
                    overflow: 'auto',
                    height: "90vh",
                    '& ul': { padding: 0 },
                    borderRadius: "10px"
                }}
                subheader={<li />}
            > */}
            <Grid container spacing={2}>
                {itemList.map((item, index) => (
                    <Grid size={{ xs: 6, md: 3 }} key={index}>
                        <ProductCard
                            item={item}
                            cartList={cartList}
                            setCartList={setCartList}
                            activeCart={activeCart}
                        />
                    </Grid>
                ))}
                {/* </List> */}
            </Grid>
        </Box>
    );
}

export default ProductView;
