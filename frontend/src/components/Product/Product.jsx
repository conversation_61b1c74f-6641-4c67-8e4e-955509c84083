import React from "react";
import { Link, useLocation, useNavigate, useParams } from "react-router-dom";
import Grid from "@mui/material/Grid2";
import {
    <PERSON>ert,
    Box,
    Button,
    Divider,
    Chip,
    List,
    ListItem,
    Snackbar,
    Typography,
    Breadcrumbs,
    useTheme,
    useMediaQuery,
    Paper,
    Container,
    Rating,
    Slide,
    Zoom,
    IconButton,
} from "@mui/material";
import ShoppingCartIcon from "@mui/icons-material/ShoppingCart";
import AddIcon from "@mui/icons-material/Add";
import RemoveIcon from "@mui/icons-material/Remove";
import ShareIcon from "@mui/icons-material/Share";
import FavoriteIcon from "@mui/icons-material/Favorite";
import { green, grey, red } from "@mui/material/colors";
import CustomerLayout from "../Common/CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import ProductImageCarousel from "./ProductImageCarousel";
import ProductCard from "../Common/ProductCard";
import CheckIcon from "@mui/icons-material/Check";
import Loading from "../Common/Loading";
import CustomTextField from "../Common/CustomTextField";
import { motion } from "framer-motion";
import PageNotFound from "../404";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";

function Attribute({ attribute, values, selectedItem, handleAttributeChange, disabledValues }) {
    const isSelected = (selected_attribute, selected_value) => {
        if (selectedItem.attributes)
            return selectedItem.attributes.some(
                (attr) => attr.key.includes(selected_attribute) && attr.value === selected_value
            );
        else return false;
    };

    return (
        <Box>
            <Typography variant="subtitle1" sx={{ mt: 1, mb: 1, fontWeight: 500 }}>
                {attribute}:
            </Typography>
            <Box sx={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
                {values.map((value, index) => (
                    <Button
                        key={index}
                        onClick={() => handleAttributeChange(attribute, value)}
                        disabled={disabledValues.includes(value)}
                        sx={{
                            borderRadius: "10px",
                            border: "2px solid",
                            textTransform: "none",
                            fontWeight: "bold",
                            color: selectedItem.attributes?.some(
                                (attr) => attr.key.includes(attribute) && attr.value === value
                            )
                                ? "red"
                                : "grey",
                            borderColor: selectedItem.attributes?.some(
                                (attr) => attr.key.includes(attribute) && attr.value === value
                            )
                                ? "red"
                                : "grey",
                            "&:hover": {
                                borderColor: "red",
                                color: "red",
                                transform: "translateY(-2px)",
                                transition: "transform 0.2s",
                            },
                            transition: "all 0.2s",
                        }}
                    >
                        {value}
                    </Button>
                ))}
            </Box>
        </Box>
    );
}

function Product({ user, setUser, cartItem, setCartItem }) {
    const theme = useTheme();
    const isMobile = useMediaQuery(theme.breakpoints.down("md"));
    const navigate = useNavigate();
    const location = useLocation();
    const { item_sku } = useParams();

    const [itemQty, setItemQty] = React.useState(1);
    const [openSnackbar, setOpenSnackbar] = React.useState(false);
    const [itemList, setItemList] = React.useState([]);
    const [attributeOption, setAttributeOption] = React.useState([]);
    const [selectedItem, setSelectedItem] = React.useState({});
    const [selectedAttributes, setSelectedAttributes] = React.useState({});
    const [disabledAttributes, setDisabledAttributes] = React.useState({});
    // const [item, setItem] = React.useState({});
    const [loading, setLoading] = React.useState(true);
    const [relatedProduct, setRelatedProduct] = React.useState([]);
    const commit_sentences = [
        "Cam kết chính hãng",
        "Kiểm tra sản phẩm trước khi bán",
        "Miễn phí giao hàng toàn quốc đơn trên 500k",
        "Bảo hành trách nhiệm",
    ];
    useDocumentTitle(itemList[0]?.name ? itemList[0].name : "Sản phẩm | BeE");

    const [showMobileActions, setShowMobileActions] = React.useState(false);

    React.useEffect(() => {
        const handleScroll = () => {
            if (isMobile) {
                const scrollPosition = window.scrollY;
                const windowHeight = window.innerHeight;
                const documentHeight = document.documentElement.scrollHeight;

                // Show mobile actions when scrolling down
                setShowMobileActions(scrollPosition > 200);
            }
        };

        window.addEventListener("scroll", handleScroll);
        return () => window.removeEventListener("scroll", handleScroll);
    }, [isMobile]);

    const CommitmentCard = ({ icon, title, description }) => (
        <Paper
            elevation={0}
            sx={{
                p: 2,
                // height: "100%",
                display: "flex",
                alignItems: "center",
                borderRadius: 2,
                backgroundColor: "rgba(255,255,255,0.9)",
                border: `1px solid ${grey[200]}`,
                transition: "transform 0.2s, box-shadow 0.2s",
                "&:hover": {
                    transform: "translateY(-4px)",
                    boxShadow: theme.shadows[4],
                },
            }}
        >
            {icon}
            <Typography variant="subtitle1" sx={{ fontWeight: 600, ml: 2 }}>
                {title}
            </Typography>
            <Typography variant="body2" color="text.secondary" sx={{ ml: 2 }}>
                {description}
            </Typography>
        </Paper>
    );

    function addCartItem(item) {
        setCartItem((prevItemList) => [
            ...prevItemList,
            {
                id: item.id,
                sku: item.item.sku,
                name: item.item.name,
                image: item.item.image1_url,
                sale_price: item.item.sale_price,
                saleOff: 0,
                quantity: item.quantity,
            },
        ]);
    }

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const responseProduct = await axiosInstance.get(`/api/shopping/product-name/${item_sku}`);
                setItemList(responseProduct.data);
                // Transform attributes into the desired structure
                const transformedAttributes = responseProduct.data.reduce((acc, item) => {
                    item.attributes.forEach((attr) => {
                        if (!acc[attr.key]) {
                            acc[attr.key] = [];
                        }
                        if (!acc[attr.key].includes(attr.value)) {
                            acc[attr.key].push(attr.value);
                        }
                    });
                    return acc;
                }, {});
                // Set selected item where sku matches product_id
                let matchingItem = responseProduct.data.find((item) => item.sku === item_sku);
                if (matchingItem === null) {
                    matchingItem = responseProduct.data[0];
                }
                setSelectedItem(matchingItem);
                setSelectedAttributes(
                    matchingItem.attributes.reduce((acc, attr) => {
                        acc[attr.key] = attr.value;
                        return acc;
                    }, {})
                );
                setAttributeOption(transformedAttributes);
                updateDisabledAttributes([matchingItem]);
                const responseRelatedProduct = await axiosInstance.get(`/api/shopping/product/alternative/${item_sku}`);
                setRelatedProduct(responseRelatedProduct.data);
            } catch (error) {
                alert(error);
            } finally {
                setLoading(false);
            }
        };

        fetchData();
    }, [item_sku]);

    // Ensure updateDisabledAttributes only runs after attributeOption is set
    React.useEffect(() => {
        if (Object.keys(attributeOption).length > 0 && selectedItem) {
            updateDisabledAttributes([selectedItem]);
        }
    }, [attributeOption, selectedItem]);

    function formatPrice(number) {
        return new Intl.NumberFormat("en-US", {
            style: "currency",
            currency: "VND",
        }).format(number);
    }

    function handleDescQty() {
        setItemQty(parseInt(itemQty) - 1 > 0 ? parseInt(itemQty) - 1 : 1);
    }

    function handleIncQty() {
        setItemQty(parseInt(itemQty) + 1 <= selectedItem.quantity ? parseInt(itemQty) + 1 : parseInt(itemQty));
    }

    const handleChangeItemQty = (event) => {
        if (parseInt(event.target.value) > selectedItem.quantity) {
            alert(`Bạn chỉ có thể chọn tối đa ${selectedItem.quantity} ${selectedItem.unit}`);
        }
        try {
            setItemQty(
                parseInt(event.target.value) <= selectedItem.quantity
                    ? parseInt(event.target.value)
                    : selectedItem.quantity
            );
        } catch (err) {
            console.error(err);
        }
    };

    const handleCloseSnackbar = (event, reason) => {
        if (reason === "clickaway") {
            return;
        }
        setOpenSnackbar(false);
    };

    function handleAddToCart() {
        const formData = new FormData();
        formData.append("sku", selectedItem.sku);
        formData.append("quantity", itemQty);
        formData.append("sale_price", selectedItem.sale_price);

        axiosInstance
            .post("api/shopping/cart/add", formData)
            .then((response) => {
                if (response.status === 201) {
                    setCartItem([]);
                    for (var i = 0; i < response.data.length; i++) addCartItem(response.data[i]);
                    setOpenSnackbar(true);
                }
            })
            .catch((error) => {
                if (error.response && error.response.status === 401) {
                    navigate("/login", { state: { from: location } });
                } else {
                    console.error("Error adding to cart:", error);
                }
            });
    }

    const handleAttributeChange = (attr, value) => {
        setSelectedAttributes((prevSelected) => {
            const updatedSelection = { ...prevSelected, [attr]: value };

            // Find the matching item from itemList based on selected attributes
            let matchedItems = itemList.filter((item) =>
                item.attributes.every(
                    (attribute) =>
                        // Match only if the attribute is already selected in the updated selection
                        !(attribute.key in updatedSelection) || updatedSelection[attribute.key] === attribute.value
                )
            );

            // If no matches are found, remove the conflicting attribute and find matches again
            if (matchedItems.length === 0) {
                const adjustedSelection = { [attr]: value };

                matchedItems = itemList.filter((item) =>
                    item.attributes.every((attribute) => {
                        return (
                            !(attribute.key in adjustedSelection) ||
                            adjustedSelection[attribute.key] === attribute.value
                        );
                    })
                );

                // Update the selected attributes to reflect the adjusted selection
                setSelectedAttributes(adjustedSelection);
            }

            if (matchedItems.length > 0) {
                // Example: Select the item with the highest quantity
                const selectedItem = matchedItems[0]; //.reduce((prev, current) => (prev.quantity > current.quantity) ? prev : current);
                setSelectedItem(selectedItem);

                updateDisabledAttributes(matchedItems);
            }

            return updatedSelection;
        });
    };

    function updateDisabledAttributes(matchedItems) {
        // Update disabled attributes based on the matched items
        const newDisabledAttributes = {};
        const attributeKeys = Object.keys(attributeOption);

        const allAttributes = matchedItems[0].attributes[0].value;

        const matchedFirstItem = itemList.filter((item) =>
            item.attributes.some((attribute) => attribute.key === attributeKeys[0] && attribute.value === allAttributes)
        );

        // Skip the first attribute in the list
        attributeKeys.slice(1).forEach((attributeKey) => {
            newDisabledAttributes[attributeKey] = attributeOption[attributeKey].filter((optionValue) => {
                // Disable the value if it is not b present in any of the matched items
                return !matchedFirstItem.some((item) =>
                    item.attributes.some((attr) => attr.key === attributeKey && attr.value === optionValue)
                );
            });
        });

        setDisabledAttributes(newDisabledAttributes);
    }

    if (loading) return <Loading />;

    return (
        <CustomerLayout cartItem={cartItem} user={user} setUser={setUser} setCartItem={setCartItem}>
            <Container maxWidth="xl">
                <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5 }}
                >
                    {/* Breadcrumbs */}
                    <Breadcrumbs sx={{ mb: 2 }}>
                        <Link to="/" style={{ color: grey[600], textDecoration: "none" }}>
                            Trang chủ
                        </Link>
                        <Link to="/shop" style={{ color: grey[600], textDecoration: "none" }}>
                            Cửa hàng
                        </Link>
                        <Link
                            to={`/shop/${selectedItem.category}`}
                            style={{ color: grey[600], textDecoration: "none" }}
                        >
                            {selectedItem.category}
                        </Link>
                        <Typography color="text.primary">{selectedItem.name}</Typography>
                    </Breadcrumbs>

                    <Grid container spacing={4}>
                        {/* Product Images */}
                        <Grid size={{ xs: 12, md: 6 }}>
                            <Paper
                                elevation={0}
                                sx={{
                                    borderRadius: 2,
                                    overflow: "hidden",
                                    backgroundColor: "transparent",
                                }}
                            >
                                <ProductImageCarousel images={selectedItem.images} />
                            </Paper>
                        </Grid>

                        {/* Product Info */}
                        <Grid size={{ xs: 12, md: 6 }}>
                            <Box sx={{ position: "sticky", top: 20 }}>
                                <Box
                                    sx={{
                                        display: "flex",
                                        justifyContent: "space-between",
                                        alignItems: "flex-start",
                                    }}
                                >
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            fontWeight: 700,
                                            color: grey[800],
                                            mb: 1,
                                        }}
                                    >
                                        {selectedItem.name}
                                    </Typography>
                                    {/* <Box>
                                        <IconButton>
                                            <ShareIcon />
                                        </IconButton>
                                        <IconButton>
                                            <FavoriteIcon />
                                        </IconButton>
                                    </Box> */}
                                </Box>

                                {selectedItem.is_removed ||
                                    (!selectedItem.visible && (
                                        <Chip label="SKU đã bị xóa" color="error" variant="contained" sx={{ mb: 1 }} />
                                    ))}

                                <Box
                                    sx={{
                                        display: "flex",
                                        alignItems: "center",
                                        mb: 2,
                                    }}
                                >
                                    <Rating value={4.5} readOnly precision={0.5} />
                                    {/* <Typography variant="body2" sx={{ ml: 1, color: grey[600] }}>
                                        (`${Math.random() * 100} đánh giá`)
                                    </Typography> */}
                                </Box>

                                {/* Price Section */}
                                <Box sx={{ mb: 3 }}>
                                    {selectedItem.discount_value > 0 ? (
                                        <Box
                                            sx={{
                                                display: "flex",
                                                alignItems: "baseline",
                                                gap: 1,
                                            }}
                                        >
                                            <Typography
                                                variant="h4"
                                                sx={{
                                                    color: red[500],
                                                    fontWeight: 700,
                                                }}
                                            >
                                                {formatPrice(selectedItem.final_price)}
                                            </Typography>
                                            <Typography
                                                sx={{
                                                    color: grey[500],
                                                    textDecoration: "line-through",
                                                }}
                                            >
                                                {formatPrice(selectedItem.sale_price)}
                                            </Typography>
                                            <Chip
                                                label={`-${selectedItem.discount_value}%`}
                                                color="error"
                                                size="small"
                                            />
                                        </Box>
                                    ) : (
                                        <Typography
                                            variant="h4"
                                            sx={{
                                                color: red[500],
                                                fontWeight: 700,
                                            }}
                                        >
                                            {formatPrice(selectedItem.sale_price)}
                                        </Typography>
                                    )}
                                </Box>

                                {/* Attributes */}
                                {Object.entries(attributeOption).map(([attr, values]) => (
                                    <Attribute
                                        key={attr}
                                        attribute={attr}
                                        values={values}
                                        selectedItem={selectedItem}
                                        handleAttributeChange={handleAttributeChange}
                                        disabledValues={disabledAttributes[attr] || []}
                                    />
                                ))}
                                {!isMobile && (
                                    <>
                                        {/* Quantity Selector */}
                                        <Box sx={{ mt: 3 }}>
                                            <Typography variant="subtitle1" sx={{ mb: 1, fontWeight: 500 }}>
                                                Số lượng:
                                            </Typography>
                                            <Box
                                                sx={{
                                                    display: "flex",
                                                    alignItems: "center",
                                                    gap: 1,
                                                }}
                                            >
                                                <Button
                                                    disabled={itemQty <= 1}
                                                    onClick={handleDescQty}
                                                    variant="outlined"
                                                    color="primary"
                                                    sx={{
                                                        minWidth: "auto",
                                                        borderRadius: 2,
                                                        height: "40px",
                                                    }}
                                                >
                                                    <RemoveIcon />
                                                </Button>
                                                <CustomTextField
                                                    size="small"
                                                    value={itemQty}
                                                    onChange={handleChangeItemQty}
                                                    type="number"
                                                    sx={{ width: 100 }}
                                                    inputProps={{
                                                        min: 1,
                                                        max: selectedItem.quantity,
                                                        style: { textAlign: "center" },
                                                    }}
                                                />
                                                <Button
                                                    disabled={itemQty >= selectedItem.quantity}
                                                    onClick={handleIncQty}
                                                    variant="outlined"
                                                    color="primary"
                                                    sx={{
                                                        minWidth: "auto",
                                                        borderRadius: 2,
                                                        height: "40px",
                                                    }}
                                                >
                                                    <AddIcon />
                                                </Button>
                                            </Box>
                                        </Box>

                                        {/* Action Buttons */}
                                        <Box sx={{ mt: 3, display: "flex", gap: 2 }}>
                                            <Button
                                                variant="contained"
                                                size="large"
                                                startIcon={<ShoppingCartIcon />}
                                                onClick={handleAddToCart}
                                                sx={{
                                                    flex: 2,
                                                    borderRadius: 2,
                                                    backgroundColor: theme.palette.primary.main,
                                                    "&:hover": {
                                                        backgroundColor: theme.palette.primary.dark,
                                                    },
                                                }}
                                            >
                                                Thêm vào giỏ
                                            </Button>
                                            <Button
                                                variant="outlined"
                                                size="large"
                                                href="https://zalo.me/0987845231"
                                                target="_blank"
                                                sx={{
                                                    flex: 1,
                                                    borderRadius: 2,
                                                    display: "flex",
                                                    gap: 1,
                                                    textTransform: "none",
                                                }}
                                            >
                                                <img width={24} height={24} src="/assets/zalo-48.png" alt="Zalo" />
                                                <Typography
                                                    sx={{
                                                        display: {
                                                            md: "none",
                                                            lg: "flex",
                                                        },
                                                    }}
                                                >
                                                    Liên hệ Zalo
                                                </Typography>
                                            </Button>
                                        </Box>
                                    </>
                                )}

                                {/* Commitments */}
                                <Grid container spacing={1} sx={{ mt: 3 }}>
                                    {commit_sentences.map((commitment, index) => (
                                        <Grid size={{ xs: 12 }} key={index}>
                                            <CommitmentCard icon={<CheckIcon color="success" />} title={commitment} />
                                        </Grid>
                                    ))}
                                </Grid>
                            </Box>
                        </Grid>
                    </Grid>

                    {/* Product Description */}
                    <Box sx={{ mt: 6 }}>
                        <Typography variant="h5" sx={{ fontWeight: 500, mb: 3 }}>
                            Mô Tả Sản Phẩm
                        </Typography>
                        <Paper
                            elevation={0}
                            sx={{
                                p: 3,
                                borderRadius: 2,
                                backgroundColor: "rgba(255,255,255,0.9)",
                            }}
                        >
                            <Typography
                                sx={{
                                    width: "100%",
                                    overflowWrap: "break-word",
                                    wordWrap: "break-word",
                                    hyphens: "auto",
                                    textAlign: "justify",
                                    "& img": {
                                        maxWidth: "100%",
                                        height: "auto",
                                        display: "block",
                                        margin: "auto",
                                    },
                                }}
                                dangerouslySetInnerHTML={{
                                    __html: selectedItem.description,
                                }}
                            />
                        </Paper>
                    </Box>

                    {/* Related Products */}
                    <Divider sx={{ mt: 6, mb: 3 }} />
                    <Box>
                        <Typography variant="h5" sx={{ fontWeight: 500, mb: 3 }}>
                            Sản Phẩm Liên Quan
                        </Typography>
                        <Grid container spacing={3}>
                            {relatedProduct.map((item, index) => (
                                <Grid size={{ xs: 6, md: 3 }} key={index}>
                                    <ProductCard
                                        item={item}
                                        setOpenSnackbar={setOpenSnackbar}
                                        cartItem={cartItem}
                                        setCartItem={setCartItem}
                                    />
                                </Grid>
                            ))}
                        </Grid>
                    </Box>

                    {/* Mobile Fixed Bottom Bar */}
                    {isMobile && (
                        <Slide direction="up" in={showMobileActions}>
                            <Paper
                                elevation={4}
                                sx={{
                                    position: "fixed",
                                    bottom: 0,
                                    left: 0,
                                    right: 0,
                                    zIndex: 1000,
                                    p: 2,
                                    backgroundColor: "rgba(255,255,255,0.95)",
                                    backdropFilter: "blur(10px)",
                                }}
                            >
                                <Box sx={{ display: "flex", gap: 2 }}>
                                    <Box
                                        sx={{
                                            display: "flex",
                                            alignItems: "center",
                                            gap: 1,
                                        }}
                                    >
                                        <Button
                                            disabled={itemQty <= 1}
                                            onClick={handleDescQty}
                                            variant="outlined"
                                            color="primary"
                                            sx={{
                                                minWidth: "auto",
                                                width: "40px",
                                                borderRadius: 2,
                                                height: "40px",
                                            }}
                                        >
                                            <RemoveIcon />
                                        </Button>
                                        <CustomTextField
                                            size="small"
                                            value={itemQty}
                                            onChange={handleChangeItemQty}
                                            type="number"
                                            sx={{ width: 75 }}
                                            inputProps={{
                                                min: 1,
                                                max: selectedItem.quantity,
                                                style: { textAlign: "center" },
                                            }}
                                        />
                                        <Button
                                            disabled={itemQty >= selectedItem.quantity}
                                            onClick={handleIncQty}
                                            variant="outlined"
                                            color="primary"
                                            sx={{
                                                minWidth: "auto",
                                                width: "40px",
                                                borderRadius: 2,
                                                height: "40px",
                                            }}
                                        >
                                            <AddIcon />
                                        </Button>
                                    </Box>
                                    <Button
                                        variant="contained"
                                        fullWidth
                                        startIcon={<ShoppingCartIcon />}
                                        onClick={handleAddToCart}
                                        sx={{
                                            borderRadius: 2,
                                            backgroundColor: theme.palette.primary.main,
                                            "&:hover": {
                                                backgroundColor: theme.palette.primary.dark,
                                            },
                                        }}
                                    >
                                        Thêm vào giỏ
                                    </Button>
                                </Box>
                            </Paper>
                        </Slide>
                    )}

                    {/* Snackbar */}
                    <Snackbar
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "right",
                        }}
                        open={openSnackbar}
                        onClose={handleCloseSnackbar}
                        autoHideDuration={3000}
                    >
                        <Alert severity="success" sx={{ borderRadius: 2 }}>
                            Đã thêm vào giỏ hàng!
                        </Alert>
                    </Snackbar>
                </motion.div>
            </Container>
        </CustomerLayout>
    );
}

export default Product;
