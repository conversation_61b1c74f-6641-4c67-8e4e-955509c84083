import { useState, useRef } from "react";
import { Box, IconButton } from "@mui/material";
import ArrowBackIosIcon from "@mui/icons-material/ArrowBackIos";
import ArrowForwardIosIcon from "@mui/icons-material/ArrowForwardIos";
import SwipeableViews from "react-swipeable-views";

function ProductImageCarousel({ images }) {
    const [activeStep, setActiveStep] = useState(0);
    const maxSteps = images.length;
    const thumbnailRef = useRef(null);

    const handleNext = () => {
        setActiveStep((prevStep) => (prevStep + 1) % maxSteps);
    };

    const handleBack = () => {
        setActiveStep((prevStep) => (prevStep - 1 + maxSteps) % maxSteps);
    };

    const scrollThumbnails = (direction) => {
        const scrollAmount = 100;
        if (thumbnailRef.current) {
            thumbnailRef.current.scrollLeft += direction === "left" ? -scrollAmount : scrollAmount;
        }
    };

    return (
        <Box>
            {/* Swipeable Image Viewer */}
            <Box
                position="relative"
                sx={{
                    width: "100%",
                    height: "500px",
                    overflow: "hidden",
                }}
            >
                <SwipeableViews index={activeStep} onChangeIndex={setActiveStep}>
                    {images.map((step, index) => (
                        <div key={index}>
                            {Math.abs(activeStep - index) <= 2 ? (
                                <img
                                    src={step.image_url}
                                    alt={`image${index}`}
                                    style={{
                                        width: "100%",
                                        height: "500px",
                                        objectFit: "contain",
                                    }}
                                />
                            ) : null}
                        </div>
                    ))}
                </SwipeableViews>

                {/* Centered Navigation Buttons */}
                {images.length > 1 && (
                    <>
                        <IconButton
                            onClick={handleBack}
                            sx={{
                                position: "absolute",
                                top: "50%",
                                left: "10px",
                                transform: "translateY(-50%)",
                                backgroundColor: "rgba(255, 255, 255, 0.5)",
                            }}
                        >
                            <ArrowBackIosIcon />
                        </IconButton>

                        <IconButton
                            onClick={handleNext}
                            sx={{
                                position: "absolute",
                                top: "50%",
                                right: "10px",
                                transform: "translateY(-50%)",
                                backgroundColor: "rgba(255, 255, 255, 0.5)",
                            }}
                        >
                            <ArrowForwardIosIcon />
                        </IconButton>
                    </>
                )}
            </Box>

            {/* Thumbnail Navigation Carousel */}
            <Box sx={{ display: "flex", alignItems: "center", mt: 2 }}>
                {images.length > 5 && (
                    <IconButton onClick={() => scrollThumbnails("left")}>
                        <ArrowBackIosIcon />
                    </IconButton>
                )}
                <Box
                    ref={thumbnailRef}
                    sx={{
                        display: "flex",
                        overflowX: "auto",
                        width: "100%",
                        scrollBehavior: "smooth",
                        "&::-webkit-scrollbar": { display: "none" }, // Hide scrollbar
                    }}
                >
                    {images.map((item, index) => (
                        <Box
                            key={index}
                            onClick={() => setActiveStep(index)}
                            sx={{
                                width: "80px",
                                cursor: "pointer",
                                padding: "0 10px",
                                opacity: activeStep === index ? 1 : 0.5,
                                transition: "opacity 0.3s ease-in-out",
                                mr: 2,
                            }}
                        >
                            <img
                                src={item.image_url}
                                alt={`image${index}`}
                                style={{ width: "80px", height: "80px", objectFit: "contain" }}
                            />
                        </Box>
                    ))}
                </Box>
                {images.length > 5 && (
                    <IconButton onClick={() => scrollThumbnails("right")}>
                        <ArrowForwardIosIcon />
                    </IconButton>
                )}
            </Box>
        </Box>
    );
}

export default ProductImageCarousel;
