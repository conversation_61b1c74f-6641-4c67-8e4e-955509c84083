import React from "react";
import CustomerLayout from "../Common/CustomerLayout";
import {
    Alert,
    Box,
    Chip,
    Card,
    InputAdornment,
    Snackbar,
    Typography,
    Container,
    Fade,
    Slide,
    IconButton,
    useTheme,
    Button,
    Badge,
    Tooltip,
    alpha,
    styled,
    Paper,
    TextField
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import ProductCard from "../Common/ProductCard";
import SearchIcon from "@mui/icons-material/Search";
import axiosInstance from "../../services/axiosInstance";
import CustomTextField from "../Common/CustomTextField";
import Loading from "../Common/Loading";
import { motion } from "framer-motion";
import { useParams } from "react-router-dom";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";
import {
    ShoppingCart,
    TrendingUp,
    LocalOffer,
    Star,
    ViewList,
    ViewModule,
    Sort,
    FilterAlt,
    LocalShipping,
    Security,
    Support,
    ArrowUpward
} from "@mui/icons-material";

// Styled Components
const HeroSection = styled(Box)(({ theme }) => ({
    position: 'relative',
    minHeight: '70vh',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    background: `linear-gradient(135deg,
        ${theme.palette.primary.main}15 0%,
        ${theme.palette.secondary.main}10 25%,
        ${theme.palette.info.main}08 50%,
        ${theme.palette.success.main}12 75%,
        ${theme.palette.primary.main}18 100%)`,
    borderRadius: 0,
    overflow: 'hidden',
    marginBottom: 0,
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `
            radial-gradient(circle at 20% 20%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 40%),
            radial-gradient(circle at 80% 80%, ${alpha(theme.palette.secondary.main, 0.08)} 0%, transparent 40%),
            radial-gradient(circle at 40% 60%, ${alpha(theme.palette.info.main, 0.06)} 0%, transparent 40%)
        `,
        pointerEvents: 'none'
    },
    '&::after': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${theme.palette.primary.main.slice(1)}' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
        pointerEvents: 'none'
    }
}));

const SearchSection = styled(Paper)(({ theme }) => ({
    padding: theme.spacing(4, 4, 3, 4),
    marginBottom: theme.spacing(4),
    borderRadius: theme.spacing(4),
    background: `linear-gradient(145deg,
        ${theme.palette.background.paper} 0%,
        ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
    backdropFilter: 'blur(20px)',
    border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
    boxShadow: `
        0 8px 32px ${alpha(theme.palette.common.black, 0.04)},
        0 2px 8px ${alpha(theme.palette.common.black, 0.02)}
    `,
    position: 'relative',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        height: '3px',
        // background: `linear-gradient(90deg,
        //     ${theme.palette.primary.main},
        //     ${theme.palette.secondary.main},
        //     ${theme.palette.info.main})`,
        borderRadius: `${theme.spacing(4)} ${theme.spacing(4)} 0 0`
    }
}));

const StatsCard = styled(Card)(({ theme }) => ({
    padding: theme.spacing(3, 2),
    textAlign: 'center',
    borderRadius: theme.spacing(3),
    background: `linear-gradient(145deg,
        ${theme.palette.background.paper} 0%,
        ${alpha(theme.palette.background.paper, 0.8)} 100%)`,
    border: `1px solid ${alpha(theme.palette.divider, 0.08)}`,
    backdropFilter: 'blur(10px)',
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(135deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
        opacity: 0,
        transition: 'opacity 0.3s ease'
    },
    '&:hover': {
        transform: 'translateY(-8px) scale(1.02)',
        boxShadow: `
            0 20px 40px ${alpha(theme.palette.common.black, 0.1)},
            0 8px 16px ${alpha(theme.palette.common.black, 0.06)}
        `,
        '&::before': {
            opacity: 1
        }
    }
}));

const FloatingActionButton = styled(Button)(({ theme }) => ({
    position: 'fixed',
    bottom: theme.spacing(4),
    right: theme.spacing(4),
    borderRadius: '50%',
    width: 64,
    height: 64,
    minWidth: 64,
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
    boxShadow: `
        0 8px 25px ${alpha(theme.palette.primary.main, 0.4)},
        0 4px 10px ${alpha(theme.palette.common.black, 0.1)}
    `,
    border: `3px solid ${alpha(theme.palette.background.paper, 0.9)}`,
    zIndex: 1000,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    '&:hover': {
        background: `linear-gradient(135deg, ${theme.palette.primary.dark} 0%, ${theme.palette.secondary.main} 100%)`,
        transform: 'translateY(-4px) scale(1.1)',
        boxShadow: `
            0 12px 35px ${alpha(theme.palette.primary.main, 0.5)},
            0 6px 15px ${alpha(theme.palette.common.black, 0.15)}
        `
    },
    '&:active': {
        transform: 'translateY(-2px) scale(1.05)'
    }
}));

function BeeShop({ user, setUser, cartItem, setCartItem }) {
    useDocumentTitle("Cửa hàng | BeE");
    const { category_query } = useParams();

    const theme = useTheme();
    const [itemList, setItemList] = React.useState([]);
    const [loading, setLoading] = React.useState(true);
    const [loadingProduct, setLoadingProduct] = React.useState(true);
    const [tag, setTag] = React.useState([]);
    const [category, setCategory] = React.useState([]);
    const [selectedCategory, setSelectedCategory] = React.useState("");
    const [searchTerm, setSearchTerm] = React.useState("");
    const [showFilters, setShowFilters] = React.useState(category_query ? true : false);
    const [viewMode, setViewMode] = React.useState('grid'); // 'grid' or 'list'
    const [sortBy, setSortBy] = React.useState('name'); // 'name', 'price', 'rating'
    const [showScrollTop, setShowScrollTop] = React.useState(false);

    const addTags = (newTags) => {
        const uniqueTags = newTags.filter((newTag) => !tag.includes(newTag));
        setTag((prevTagList) => [...prevTagList, ...uniqueTags]);
    };

    // Fetch initial data
    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const [tagRes, categoryRes] = await Promise.all([
                    axiosInstance.get("/api/product/tag/"),
                    axiosInstance.get("/api/shopping/category/"),
                ]);
                addTags(tagRes.data.map((tag) => tag.name));
                setCategory(categoryRes.data);

                if (category_query) {
                    const foundCategory = categoryRes.data.find((cat) => cat.name === category_query);
                    const category_selected = foundCategory ? foundCategory.id : "all";
                    setSelectedCategory(category_selected);
                    setShowFilters(true);
                }
            } catch (err) {
                console.error(err);
            } finally {
                setLoading(false);
                setLoadingProduct(false);
            }
        };
        fetchData();
    }, []);

    // Handle category selection
    React.useEffect(() => {
        const fetchData = async () => {
            let category_selected = selectedCategory;
            if (category_selected === "") category_selected = "all";
            setLoadingProduct(true);
            try {
                const response = await axiosInstance.get(`/api/shopping/home/<USER>/${category_selected}`);
                setItemList(response.data);
            } catch (err) {
                console.error(err);
            } finally {
                setLoadingProduct(false);
            }
        };
        fetchData();
    }, [selectedCategory]);

    // Snackbar state
    const [openSnackbar, setOpenSnackbar] = React.useState(false);
    const handleCloseSnackbar = (_, reason) => {
        if (reason === "clickaway") return;
        setOpenSnackbar(false);
    };

    // Handle scroll for floating button
    React.useEffect(() => {
        const handleScroll = () => {
            setShowScrollTop(window.scrollY > 300);
        };
        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const scrollToTop = () => {
        window.scrollTo({ top: 0, behavior: 'smooth' });
    };

    const handleSortChange = (newSort) => {
        setSortBy(newSort);
        // Sort logic here
    };

    const handleViewModeChange = (mode) => {
        setViewMode(mode);
    };

    // Filter products based on search
    const filteredProducts = React.useMemo(() => {
        return itemList.filter((item) => item.name.toLowerCase().includes(searchTerm.toLowerCase()));
    }, [itemList, searchTerm]);

    // Animation variants for Framer Motion
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            {loading ? (
                <Box
                    sx={{
                        minHeight: "70vh",
                        display: "flex",
                        alignItems: "center",
                        justifyContent: "center",
                    }}
                >
                    <Loading />
                </Box>
            ) : (
                <>
                    {/* Hero Section */}
                    <HeroSection>
                        <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2, py: 8 }}>
                            <motion.div
                                initial={{ opacity: 0, y: 30 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.8 }}
                                style={{ textAlign: 'center' }}
                            >
                                {/* Main Title */}
                                <Box sx={{ mb: 6 }}>
                                    <Typography
                                        variant="h1"
                                        sx={{
                                            fontSize: { xs: '2.8rem', md: '4.5rem', lg: '5rem' },
                                            fontWeight: 900,
                                            background: `linear-gradient(135deg,
                                                ${theme.palette.primary.main} 0%,
                                                ${theme.palette.secondary.main} 50%,
                                                ${theme.palette.info.main} 100%)`,
                                            backgroundClip: 'text',
                                            WebkitBackgroundClip: 'text',
                                            WebkitTextFillColor: 'transparent',
                                            mb: 3,
                                            letterSpacing: '-0.02em',
                                            lineHeight: 0.9
                                        }}
                                    >
                                        BeE Shop
                                    </Typography>
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            color: 'text.primary',
                                            mb: 2,
                                            fontWeight: 400,
                                            maxWidth: '700px',
                                            mx: 'auto',
                                            lineHeight: 1.4,
                                            fontSize: { xs: '1.2rem', md: '1.5rem' }
                                        }}
                                    >
                                        🚀 Khám phá thế giới công nghệ STEM
                                    </Typography>
                                    <Typography
                                        variant="h6"
                                        sx={{
                                            color: 'text.secondary',
                                            maxWidth: '600px',
                                            mx: 'auto',
                                            lineHeight: 1.6,
                                            fontSize: { xs: '1rem', md: '1.1rem' }
                                        }}
                                    >
                                        Hàng ngàn sản phẩm chất lượng cao từ Arduino, Raspberry Pi đến Robot AI
                                    </Typography>
                                </Box>

                                {/* Hero Stats */}
                                <Grid container spacing={4} justifyContent="center" sx={{ mt: 6 }}>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.2, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.primary.main, 0.3)}`
                                                    }}
                                                >
                                                    <ShoppingCart sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'primary.main', mb: 0.5 }}>
                                                    {itemList.length}+
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Sản phẩm
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.3, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.success.main, 0.3)}`
                                                    }}
                                                >
                                                    <LocalShipping sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'success.main', mb: 0.5 }}>
                                                    24h
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Giao hàng
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.4, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.info.main, 0.3)}`
                                                    }}
                                                >
                                                    <Security sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'info.main', mb: 0.5 }}>
                                                    100%
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Bảo mật
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                    <Grid size={{ xs: 6, sm: 3 }}>
                                        <motion.div
                                            initial={{ opacity: 0, y: 20 }}
                                            animate={{ opacity: 1, y: 0 }}
                                            transition={{ delay: 0.5, duration: 0.6 }}
                                        >
                                            <StatsCard>
                                                <Box
                                                    sx={{
                                                        width: 60,
                                                        height: 60,
                                                        borderRadius: '50%',
                                                        background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                                        display: 'flex',
                                                        alignItems: 'center',
                                                        justifyContent: 'center',
                                                        mx: 'auto',
                                                        mb: 2,
                                                        boxShadow: `0 8px 20px ${alpha(theme.palette.warning.main, 0.3)}`
                                                    }}
                                                >
                                                    <Support sx={{ fontSize: 28, color: 'white' }} />
                                                </Box>
                                                <Typography variant="h3" sx={{ fontWeight: 800, color: 'warning.main', mb: 0.5 }}>
                                                    24/7
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary" sx={{ fontWeight: 500 }}>
                                                    Hỗ trợ
                                                </Typography>
                                            </StatsCard>
                                        </motion.div>
                                    </Grid>
                                </Grid>
                            </motion.div>
                        </Container>
                    </HeroSection>

                    <Container maxWidth="xl" sx={{ py: 3 }}>
                        <motion.div initial="hidden" animate="visible" variants={containerVariants}>
                            {/* Search and Filter Section */}
                            <SearchSection elevation={1}>
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6 }}
                                >
                                    <Typography
                                        variant="h4"
                                        sx={{
                                            fontWeight: 700,
                                            mb: 3,
                                            textAlign: 'center',
                                            background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                                            backgroundClip: 'text',
                                            WebkitBackgroundClip: 'text',
                                            WebkitTextFillColor: 'transparent',
                                        }}
                                    >
                                        🔍 Tìm kiếm sản phẩm
                                    </Typography>

                                    <Box
                                        sx={{
                                            display: "flex",
                                            flexDirection: { xs: "column", md: "row" },
                                            gap: 3,
                                            alignItems: { xs: "stretch", md: "center" },
                                            justifyContent: "space-between",
                                            mb: 3
                                        }}
                                    >
                                        {/* Search Bar */}
                                        <Box sx={{ flex: 1, maxWidth: { md: "500px" } }}>
                                            <CustomTextField
                                                fullWidth
                                                placeholder="Nhập tên sản phẩm, Arduino, Raspberry Pi..."
                                                value={searchTerm}
                                                onChange={(e) => setSearchTerm(e.target.value)}
                                                slotProps={{
                                                    input: {
                                                        startAdornment: (
                                                            <InputAdornment position="start">
                                                                <SearchIcon color="primary" />
                                                            </InputAdornment>
                                                        ),
                                                    },
                                                }}
                                            // sx={{
                                            //     '& .MuiOutlinedInput-root': {
                                            //         borderRadius: 4,
                                            //         backgroundColor: 'background.paper',
                                            //         fontSize: '1.1rem',
                                            //         padding: '4px 0',
                                            //         border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                                            //         transition: 'all 0.3s ease',
                                            //         '&:hover': {
                                            //             borderColor: alpha(theme.palette.primary.main, 0.3),
                                            //             boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.15)}`
                                            //         },
                                            //         '&.Mui-focused': {
                                            //             borderColor: theme.palette.primary.main,
                                            //             boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.25)}`
                                            //         }
                                            //     }
                                            // }}
                                            />
                                        </Box>

                                        {/* View Mode & Sort Controls */}
                                        <Box sx={{ display: "flex", gap: 2, alignItems: "center", flexWrap: 'wrap' }}>
                                            {/* View Mode Toggle */}
                                            <Paper
                                                sx={{
                                                    display: "flex",
                                                    border: `2px solid ${alpha(theme.palette.primary.main, 0.1)}`,
                                                    borderRadius: 3,
                                                    overflow: 'hidden',
                                                    background: 'background.paper'
                                                }}
                                            >
                                                <Tooltip title="Dạng lưới">
                                                    <IconButton
                                                        onClick={() => handleViewModeChange('grid')}
                                                        sx={{
                                                            color: viewMode === 'grid' ? 'white' : 'text.secondary',
                                                            backgroundColor: viewMode === 'grid' ? 'primary.main' : 'transparent',
                                                            borderRadius: 0,
                                                            '&:hover': {
                                                                backgroundColor: viewMode === 'grid' ? 'primary.dark' : alpha(theme.palette.primary.main, 0.1)
                                                            }
                                                        }}
                                                    >
                                                        <ViewModule />
                                                    </IconButton>
                                                </Tooltip>
                                                <Tooltip title="Dạng danh sách">
                                                    <IconButton
                                                        onClick={() => handleViewModeChange('list')}
                                                        sx={{
                                                            color: viewMode === 'list' ? 'white' : 'text.secondary',
                                                            backgroundColor: viewMode === 'list' ? 'primary.main' : 'transparent',
                                                            borderRadius: 0,
                                                            '&:hover': {
                                                                backgroundColor: viewMode === 'list' ? 'primary.dark' : alpha(theme.palette.primary.main, 0.1)
                                                            }
                                                        }}
                                                    >
                                                        <ViewList />
                                                    </IconButton>
                                                </Tooltip>
                                            </Paper>

                                            {/* Sort Dropdown */}
                                            <Button
                                                variant="outlined"
                                                startIcon={<Sort />}
                                                onClick={() => handleSortChange(sortBy === 'name' ? 'price' : 'name')}
                                                sx={{
                                                    borderRadius: 3,
                                                    textTransform: 'none',
                                                    minWidth: '140px',
                                                    fontWeight: 600,
                                                    borderWidth: 2,
                                                    '&:hover': {
                                                        borderWidth: 2,
                                                        transform: 'translateY(-1px)',
                                                        boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                                                    }
                                                }}
                                            >
                                                {sortBy === 'name' ? '📝 Tên A-Z' : '💰 Giá thấp'}
                                            </Button>

                                            {/* Filter Toggle */}
                                            <Tooltip title="Bộ lọc">
                                                <IconButton
                                                    onClick={() => setShowFilters(!showFilters)}
                                                    sx={{
                                                        color: showFilters ? 'white' : 'text.secondary',
                                                        backgroundColor: showFilters ? 'primary.main' : 'background.paper',
                                                        border: `2px solid ${alpha(theme.palette.primary.main, showFilters ? 1 : 0.2)}`,
                                                        borderRadius: 3,
                                                        width: 48,
                                                        height: 48,
                                                        '&:hover': {
                                                            backgroundColor: showFilters ? 'primary.dark' : alpha(theme.palette.primary.main, 0.1),
                                                            transform: 'translateY(-1px)',
                                                            boxShadow: `0 4px 12px ${alpha(theme.palette.primary.main, 0.2)}`
                                                        }
                                                    }}
                                                >
                                                    <Badge badgeContent={tag.length} color="error">
                                                        <FilterAlt />
                                                    </Badge>
                                                </IconButton>
                                            </Tooltip>
                                        </Box>
                                    </Box>
                                </motion.div>

                                {/* Category Filters */}
                                <Slide in={showFilters} direction="down">
                                    <Box
                                        sx={{
                                            display: showFilters ? "flex" : "none",
                                            flexWrap: "wrap",
                                            gap: 2,
                                            mt: 2
                                        }}
                                    >
                                        <Chip
                                            label="Tất cả"
                                            variant={selectedCategory === "" ? "filled" : "outlined"}
                                            color={selectedCategory === "" ? "primary" : "default"}
                                            onClick={() => setSelectedCategory("")}
                                            sx={{
                                                transition: "all 0.3s ease",
                                                "&:hover": {
                                                    transform: "translateY(-2px)",
                                                    boxShadow: 2,
                                                },
                                                height: "40px",
                                            }}
                                        />
                                        {category.map((cat, index) => (
                                            <Chip
                                                key={index}
                                                label={cat.name}
                                                variant={cat.id === selectedCategory ? "filled" : "outlined"}
                                                color={cat.id === selectedCategory ? "primary" : "default"}
                                                onClick={() => setSelectedCategory(cat.id)}
                                                sx={{
                                                    transition: "all 0.3s ease",
                                                    "&:hover": {
                                                        transform: "translateY(-2px)",
                                                        boxShadow: 2,
                                                    },
                                                    height: "40px",
                                                }}
                                            />
                                        ))}
                                    </Box>
                                </Slide>
                            </SearchSection>

                            {/* Products Section */}
                            <Box sx={{ mb: 6 }}>
                                {/* Results Header */}
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.6, delay: 0.2 }}
                                >
                                    <Paper
                                        sx={{
                                            p: 3,
                                            mb: 4,
                                            borderRadius: 3,
                                            background: `linear-gradient(135deg,
                                                ${alpha(theme.palette.background.paper, 0.9)} 0%,
                                                ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
                                            border: `1px solid ${alpha(theme.palette.divider, 0.1)}`,
                                            boxShadow: `0 4px 20px ${alpha(theme.palette.common.black, 0.02)}`
                                        }}
                                    >
                                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                                            <Box>
                                                <Typography variant="h4" sx={{ fontWeight: 700, mb: 1 }}>
                                                    {filteredProducts.length > 0 ? (
                                                        <>🎯 Tìm thấy <span style={{ color: theme.palette.primary.main }}>{filteredProducts.length}</span> sản phẩm</>
                                                    ) : (
                                                        '❌ Không tìm thấy sản phẩm nào'
                                                    )}
                                                </Typography>
                                                <Typography variant="body1" color="text.secondary">
                                                    {searchTerm ? `Kết quả cho "${searchTerm}"` : 'Tất cả sản phẩm có sẵn'}
                                                </Typography>
                                            </Box>

                                            {/* Quick Filters */}
                                            <Box sx={{ display: 'flex', gap: 1.5, flexWrap: 'wrap' }}>
                                                <Chip
                                                    icon={<TrendingUp />}
                                                    label="🔥 Bán chạy"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.error.main, 0.1),
                                                            borderColor: theme.palette.error.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                                <Chip
                                                    icon={<LocalOffer />}
                                                    label="💸 Giảm giá"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.warning.main, 0.1),
                                                            borderColor: theme.palette.warning.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                                <Chip
                                                    icon={<Star />}
                                                    label="⭐ Đánh giá cao"
                                                    variant="outlined"
                                                    clickable
                                                    sx={{
                                                        borderRadius: 3,
                                                        fontWeight: 600,
                                                        '&:hover': {
                                                            backgroundColor: alpha(theme.palette.success.main, 0.1),
                                                            borderColor: theme.palette.success.main,
                                                            transform: 'translateY(-1px)'
                                                        }
                                                    }}
                                                />
                                            </Box>
                                        </Box>
                                    </Paper>
                                </motion.div>

                                {/* Products Grid */}
                                {loadingProduct ? (
                                    <Box
                                        sx={{
                                            minHeight: "40vh",
                                            display: "flex",
                                            alignItems: "center",
                                            justifyContent: "center",
                                        }}
                                    >
                                        <Loading />
                                    </Box>
                                ) : filteredProducts.length === 0 ? (
                                    <Box
                                        sx={{
                                            textAlign: 'center',
                                            py: 8,
                                            color: 'text.secondary'
                                        }}
                                    >
                                        <ShoppingCart sx={{ fontSize: 80, mb: 2, opacity: 0.3 }} />
                                        <Typography variant="h6" sx={{ mb: 1 }}>
                                            Không tìm thấy sản phẩm
                                        </Typography>
                                        <Typography variant="body2">
                                            Thử thay đổi từ khóa tìm kiếm hoặc bộ lọc
                                        </Typography>
                                    </Box>
                                ) : (
                                    <motion.div variants={containerVariants} initial="hidden" animate="visible">
                                        <Grid container spacing={viewMode === 'grid' ? 3 : 2}>
                                            {filteredProducts.map((item, index) => (
                                                <Grid
                                                    size={{
                                                        xs: viewMode === 'grid' ? 12 : 12,
                                                        sm: viewMode === 'grid' ? 6 : 12,
                                                        md: viewMode === 'grid' ? 4 : 12,
                                                        lg: viewMode === 'grid' ? 3 : 12
                                                    }}
                                                    key={index}
                                                >
                                                    <motion.div
                                                        variants={itemVariants}
                                                        whileHover={{ y: -4 }}
                                                        transition={{ type: "spring", stiffness: 300 }}
                                                    >
                                                        <ProductCard
                                                            item={item}
                                                            setOpenSnackbar={setOpenSnackbar}
                                                            cartItem={cartItem}
                                                            setCartItem={setCartItem}
                                                        />
                                                    </motion.div>
                                                </Grid>
                                            ))}
                                        </Grid>
                                    </motion.div>
                                )}
                            </Box>
                        </motion.div>
                    </Container>

                    {/* Floating Action Button - Scroll to Top */}
                    <Fade in={showScrollTop}>
                        <FloatingActionButton
                            onClick={scrollToTop}
                            aria-label="scroll to top"
                        >
                            <ArrowUpward sx={{ color: 'white' }} />
                        </FloatingActionButton>
                    </Fade>

                    {/* Snackbar */}
                    <Snackbar
                        anchorOrigin={{
                            vertical: "bottom",
                            horizontal: "right",
                        }}
                        open={openSnackbar}
                        onClose={handleCloseSnackbar}
                        autoHideDuration={3000}
                        slots={{ transition: Fade }}
                    >
                        <Alert
                            severity="success"
                            sx={{
                                borderRadius: theme.shape.borderRadius,
                                boxShadow: theme.shadows[3],
                            }}
                        >
                            Đã thêm vào giỏ hàng!
                        </Alert>
                    </Snackbar>
                </>
            )}
        </CustomerLayout>
    );
}

export default BeeShop;
