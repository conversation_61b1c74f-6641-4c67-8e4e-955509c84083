import { motion, useScroll, useTransform } from "framer-motion";
import React from "react";
import Project from './Project';
import Course from './Course';
import Shopping from "./Shopping";
import Carousel from "./Carousel";
import CustomerLayout from "../Common/CustomerLayout";
import axiosInstance from "../../services/axiosInstance";
import Blog from "./Blog";
import Snackbar from "@mui/material/Snackbar";
import Alert from "@mui/material/Alert";
import { useDocumentTitle } from "../../hooks/useDocumentTitle";
import {
    Box,
    Container,
    Typography,
    Button,
    Card,
    CardContent,
    Avatar,
    useTheme,
    alpha,
    styled
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import {
    PlayArrow,
    School,
    ShoppingBag,
    Article,
    Code,
    CheckCircle,
    Rocket,
    AutoAwesome
} from "@mui/icons-material";

// Styled Components
const GradientBox = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg,
        ${alpha(theme.palette.primary.main, 0.1)} 0%,
        ${alpha(theme.palette.secondary.main, 0.1)} 50%,
        ${alpha(theme.palette.primary.light, 0.05)} 100%)`,
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 20% 80%, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 50%),
                     radial-gradient(circle at 80% 20%, ${alpha(theme.palette.secondary.main, 0.1)} 0%, transparent 50%)`,
        pointerEvents: 'none'
    }
}));

const FeatureCard = styled(Card)(({ theme }) => ({
    height: '100%',
    borderRadius: theme.spacing(3),
    border: `1px solid ${alpha(theme.palette.primary.main, 0.1)}`,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    cursor: 'pointer',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.02)} 0%, transparent 100%)`,
        opacity: 0,
        transition: 'opacity 0.3s ease'
    },
    '&:hover': {
        transform: 'translateY(-12px) scale(1.02)',
        boxShadow: `0 20px 40px ${alpha(theme.palette.primary.main, 0.15)}`,
        '&::before': {
            opacity: 1
        }
    }
}));

const StatsCard = styled(Box)(({ theme }) => ({
    background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
    borderRadius: theme.spacing(2),
    padding: theme.spacing(3),
    textAlign: 'center',
    border: `1px solid ${alpha(theme.palette.primary.main, 0.08)}`,
    transition: 'all 0.3s ease',
    '&:hover': {
        transform: 'translateY(-4px)',
        boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.1)}`
    }
}));

function Home({ user, setUser, cartItem, setCartItem }) {
    const theme = useTheme();
    const [loading, setLoading] = React.useState(true);
    const [itemList, setItemList] = React.useState([]);
    const [projectList, setProjectList] = React.useState([]);
    const [courseList, setCourseList] = React.useState([]);
    const [openSnackbar, setOpenSnackbar] = React.useState(false);

    useDocumentTitle("Trang chủ | BeE");

    const handleAddToCart = (item) => {
        setCartItem([...cartItem, item]);
        setOpenSnackbar(true);
    };

    const handleCloseSnackbar = () => {
        setOpenSnackbar(false);
    };

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                // Fetch products
                const productResponse = await axiosInstance.get("/api/shopping/home/<USER>/");
                setItemList(productResponse.data);

                // Fetch projects (BeE board)
                try {
                    const projectResponse = await axiosInstance.get("/api/projects/");
                    setProjectList(projectResponse.data.slice(0, 3)); // Get first 3 projects
                } catch (projectErr) {
                    console.error("Error fetching projects:", projectErr);
                    setProjectList([]);
                }

                // Fetch courses
                try {
                    const courseResponse = await axiosInstance.get("/api/courses/");
                    setCourseList(courseResponse.data.slice(0, 4)); // Get first 4 courses
                } catch (courseErr) {
                    console.error("Error fetching courses:", courseErr);
                    setCourseList([]);
                }
            } catch (err) {
                console.error("Error fetching data:", err);
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);



    // Modern Hero Section
    const HeroSection = () => {
        const { scrollY } = useScroll();
        const y = useTransform(scrollY, [0, 500], [0, 150]);

        return (
            <GradientBox sx={{ minHeight: '90vh', display: 'flex', alignItems: 'center', position: 'relative' }}>
                <Container maxWidth="lg" sx={{ position: 'relative', zIndex: 2 }}>
                    <Grid container spacing={6} alignItems="center">
                        {/* Left Content */}
                        <Grid size={{ xs: 12, lg: 6 }}>
                            <motion.div
                                initial={{ opacity: 0, x: -50 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, ease: "easeOut" }}
                            >
                                <Typography
                                    variant="h1"
                                    sx={{
                                        fontSize: { xs: '2.5rem', md: '3.5rem', lg: '4rem' },
                                        fontWeight: 800,
                                        lineHeight: 1.1,
                                        mb: 3,
                                        background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.secondary.main} 100%)`,
                                        backgroundClip: 'text',
                                        WebkitBackgroundClip: 'text',
                                        WebkitTextFillColor: 'transparent',
                                    }}
                                >
                                    Tương lai công nghệ
                                    <br />
                                    <Box component="span" sx={{ color: 'text.primary' }}>
                                        bắt đầu từ BeE
                                    </Box>
                                </Typography>

                                <Typography
                                    variant="h5"
                                    sx={{
                                        color: 'text.secondary',
                                        mb: 4,
                                        lineHeight: 1.6,
                                        fontWeight: 400
                                    }}
                                >
                                    Nền tảng học tập và sáng tạo toàn diện với BeE STEM Solution,
                                    cửa hàng công nghệ, khóa học chuyên nghiệp và cộng đồng tri thức.
                                </Typography>

                                <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
                                    <Button
                                        variant="contained"
                                        size="large"
                                        startIcon={<Rocket />}
                                        sx={{
                                            px: 4,
                                            py: 1.5,
                                            borderRadius: 3,
                                            fontSize: '1.1rem',
                                            fontWeight: 600,
                                            background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                            boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
                                            '&:hover': {
                                                transform: 'translateY(-2px)',
                                                boxShadow: `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
                                            }
                                        }}
                                        href="/studio"
                                    >
                                        Bắt đầu ngay
                                    </Button>
                                    <Button
                                        variant="outlined"
                                        size="large"
                                        startIcon={<PlayArrow />}
                                        sx={{
                                            px: 4,
                                            py: 1.5,
                                            borderRadius: 3,
                                            fontSize: '1.1rem',
                                            fontWeight: 600,
                                            borderWidth: 2,
                                            '&:hover': {
                                                borderWidth: 2,
                                                transform: 'translateY(-2px)'
                                            }
                                        }}
                                        href="/e-learning"
                                    >
                                        Khóa học
                                    </Button>
                                </Box>

                                {/* Stats */}
                                <Grid container spacing={3}>
                                    <Grid size={4}>
                                        <StatsCard>
                                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main', mb: 0.5 }}>
                                                1000+
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                Học viên
                                            </Typography>
                                        </StatsCard>
                                    </Grid>
                                    <Grid size={4}>
                                        <StatsCard>
                                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'success.main', mb: 0.5 }}>
                                                4.9★
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                Đánh giá
                                            </Typography>
                                        </StatsCard>
                                    </Grid>
                                    <Grid size={4}>
                                        <StatsCard>
                                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'warning.main', mb: 0.5 }}>
                                                50+
                                            </Typography>
                                            <Typography variant="body2" color="text.secondary">
                                                Dự án
                                            </Typography>
                                        </StatsCard>
                                    </Grid>
                                </Grid>
                            </motion.div>
                        </Grid>

                        {/* Right Visual */}
                        <Grid size={{ xs: 12, lg: 6 }}>
                            <motion.div
                                style={{ y }}
                                initial={{ opacity: 0, x: 50 }}
                                animate={{ opacity: 1, x: 0 }}
                                transition={{ duration: 0.8, delay: 0.2 }}
                            >
                                <Box
                                    sx={{
                                        position: 'relative',
                                        display: 'flex',
                                        justifyContent: 'center',
                                        alignItems: 'center'
                                    }}
                                >
                                    {/* Main Visual */}
                                    <Box
                                        sx={{
                                            width: { xs: 300, md: 400 },
                                            height: { xs: 300, md: 400 },
                                            borderRadius: '50%',
                                            background: `conic-gradient(from 0deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main}, ${theme.palette.primary.main})`,
                                            display: 'flex',
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                            position: 'relative',
                                            '&::before': {
                                                content: '""',
                                                position: 'absolute',
                                                inset: 4,
                                                borderRadius: '50%',
                                                background: theme.palette.background.default,
                                            }
                                        }}
                                    >
                                        <Box
                                            sx={{
                                                position: 'relative',
                                                zIndex: 1,
                                                textAlign: 'center'
                                            }}
                                        >
                                            <AutoAwesome sx={{ fontSize: 80, color: 'primary.main', mb: 2 }} />
                                            <Typography variant="h4" sx={{ fontWeight: 700, color: 'primary.main' }}>
                                                BeE
                                            </Typography>
                                        </Box>
                                    </Box>

                                    {/* Floating Elements */}
                                    {[
                                        { icon: <Code />, color: 'primary.main', top: '10%', left: '10%' },
                                        { icon: <School />, color: 'info.main', top: '20%', right: '15%' },
                                        { icon: <ShoppingBag />, color: 'success.main', bottom: '25%', left: '5%' },
                                        { icon: <Article />, color: 'warning.main', bottom: '15%', right: '10%' }
                                    ].map((item, index) => (
                                        <motion.div
                                            key={index}
                                            initial={{ opacity: 0, scale: 0 }}
                                            animate={{ opacity: 1, scale: 1 }}
                                            transition={{ delay: 0.5 + index * 0.1, duration: 0.5 }}
                                            style={{ position: 'absolute', ...item }}
                                        >
                                            <Avatar
                                                sx={{
                                                    bgcolor: item.color,
                                                    width: 60,
                                                    height: 60,
                                                    boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.2)}`
                                                }}
                                            >
                                                {item.icon}
                                            </Avatar>
                                        </motion.div>
                                    ))}
                                </Box>
                            </motion.div>
                        </Grid>
                    </Grid>
                </Container>

                {/* Scroll Indicator */}
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 1.5 }}
                    style={{
                        position: 'absolute',
                        bottom: 30,
                        left: '50%',
                        transform: 'translateX(-50%)'
                    }}
                >
                    <Box
                        sx={{
                            width: 2,
                            height: 40,
                            bgcolor: 'primary.main',
                            borderRadius: 1,
                            opacity: 0.6,
                            animation: 'bounce 2s infinite',
                            '@keyframes bounce': {
                                '0%, 20%, 50%, 80%, 100%': {
                                    transform: 'translateY(0)'
                                },
                                '40%': {
                                    transform: 'translateY(-10px)'
                                },
                                '60%': {
                                    transform: 'translateY(-5px)'
                                }
                            }
                        }}
                    />
                </motion.div>
            </GradientBox>
        );
    };

    return (
        <CustomerLayout user={user} setUser={setUser} cartItem={cartItem} setCartItem={setCartItem}>
            {/* Hero Section */}
            <HeroSection />

            {/* Features Section */}
            <Container maxWidth="lg" sx={{ py: 8 }}>
                <motion.div
                    initial={{ opacity: 0, y: 50 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8 }}
                    viewport={{ once: true }}
                >
                    <Typography
                        variant="h2"
                        component="h2"
                        sx={{
                            textAlign: 'center',
                            mb: 2,
                            fontWeight: 700,
                            fontSize: { xs: '2rem', md: '3rem' }
                        }}
                    >
                        Tất cả trong một nền tảng
                    </Typography>
                    <Typography
                        variant="h6"
                        sx={{
                            textAlign: 'center',
                            color: 'text.secondary',
                            mb: 6,
                            maxWidth: '600px',
                            mx: 'auto'
                        }}
                    >
                        Từ ý tưởng đến thực hiện, BeE cung cấp mọi công cụ bạn cần để thành công
                    </Typography>

                    <Grid container spacing={4}>
                        {/* BeE Board */}
                        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300 }}
                            >
                                <FeatureCard
                                    component={Button}
                                    href="/projects"
                                    sx={{ textTransform: 'none', color: 'inherit', p: 0 }}
                                >
                                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 80,
                                                height: 80,
                                                mx: 'auto',
                                                mb: 3,
                                                background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                                boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`
                                            }}
                                        >
                                            <Code sx={{ fontSize: 40 }} />
                                        </Avatar>
                                        <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                                            BeE Board
                                        </Typography>
                                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                            Nền tảng phát triển dự án với các công cụ mạnh mẽ và giao diện trực quan
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                                            <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                                            <Typography variant="body2" color="success.main">
                                                Miễn phí sử dụng
                                            </Typography>
                                        </Box>
                                    </CardContent>
                                </FeatureCard>
                            </motion.div>
                        </Grid>

                        {/* Cửa hàng */}
                        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300 }}
                            >
                                <FeatureCard
                                    component={Button}
                                    href="/shop"
                                    sx={{ textTransform: 'none', color: 'inherit', p: 0 }}
                                >
                                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 80,
                                                height: 80,
                                                mx: 'auto',
                                                mb: 3,
                                                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                                boxShadow: `0 8px 25px ${alpha(theme.palette.success.main, 0.3)}`
                                            }}
                                        >
                                            <ShoppingBag sx={{ fontSize: 40 }} />
                                        </Avatar>
                                        <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                                            Cửa hàng
                                        </Typography>
                                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                            Mua sắm các sản phẩm công nghệ chất lượng cao với giá tốt nhất
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                                            <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                                            <Typography variant="body2" color="success.main">
                                                Giao hàng nhanh
                                            </Typography>
                                        </Box>
                                    </CardContent>
                                </FeatureCard>
                            </motion.div>
                        </Grid>

                        {/* E-Learning */}
                        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300 }}
                            >
                                <FeatureCard
                                    component={Button}
                                    href="/courses"
                                    sx={{ textTransform: 'none', color: 'inherit', p: 0 }}
                                >
                                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 80,
                                                height: 80,
                                                mx: 'auto',
                                                mb: 3,
                                                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                                                boxShadow: `0 8px 25px ${alpha(theme.palette.info.main, 0.3)}`
                                            }}
                                        >
                                            <School sx={{ fontSize: 40 }} />
                                        </Avatar>
                                        <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                                            E-Learning
                                        </Typography>
                                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                            Học tập trực tuyến với các khóa học chuyên sâu từ các chuyên gia
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                                            <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                                            <Typography variant="body2" color="success.main">
                                                Chứng chỉ uy tín
                                            </Typography>
                                        </Box>
                                    </CardContent>
                                </FeatureCard>
                            </motion.div>
                        </Grid>

                        {/* Blog */}
                        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
                            <motion.div
                                whileHover={{ scale: 1.05 }}
                                transition={{ type: "spring", stiffness: 300 }}
                            >
                                <FeatureCard
                                    component={Button}
                                    href="/blog"
                                    sx={{ textTransform: 'none', color: 'inherit', p: 0 }}
                                >
                                    <CardContent sx={{ p: 4, textAlign: 'center' }}>
                                        <Avatar
                                            sx={{
                                                width: 80,
                                                height: 80,
                                                mx: 'auto',
                                                mb: 3,
                                                background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                                boxShadow: `0 8px 25px ${alpha(theme.palette.warning.main, 0.3)}`
                                            }}
                                        >
                                            <Article sx={{ fontSize: 40 }} />
                                        </Avatar>
                                        <Typography variant="h5" sx={{ fontWeight: 600, mb: 2 }}>
                                            Bài viết
                                        </Typography>
                                        <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                                            Cập nhật xu hướng công nghệ và chia sẻ kiến thức từ cộng đồng
                                        </Typography>
                                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
                                            <CheckCircle sx={{ fontSize: 16, color: 'success.main' }} />
                                            <Typography variant="body2" color="success.main">
                                                Nội dung chất lượng
                                            </Typography>
                                        </Box>
                                    </CardContent>
                                </FeatureCard>
                            </motion.div>
                        </Grid>
                    </Grid>
                </motion.div>
            </Container>

            {/* Carousel Section */}
            <Box sx={{ bgcolor: 'grey.50', py: 6 }}>
                <Container maxWidth="lg">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                    >
                        <Typography
                            variant="h4"
                            component="h2"
                            sx={{
                                textAlign: 'center',
                                mb: 4,
                                fontWeight: 600
                            }}
                        >
                            Tin tức & Sự kiện
                        </Typography>
                        <Carousel />
                    </motion.div>
                </Container>
            </Box>

            {/* Content Sections */}
            <Container maxWidth="lg" sx={{ py: 6 }}>
                {/* Shopping Section */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                >
                    <Shopping
                        loading={loading}
                        items={itemList}
                        cartItem={cartItem}
                        setCartItem={setCartItem}
                        handleAddToCart={handleAddToCart}
                    />
                </motion.div>

                {/* BeE Board Projects Section */}
                {projectList.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                        style={{ marginTop: '4rem' }}
                    >
                        <Typography
                            variant="h4"
                            sx={{
                                mb: 4,
                                fontWeight: 600,
                                textAlign: 'center'
                            }}
                        >
                            Dự án nổi bật
                        </Typography>
                        <Project projects={projectList.length > 0 ? projectList : [
                            {
                                id: 1,
                                name: "Smart Home IoT System",
                                description: "Hệ thống nhà thông minh với Arduino và ESP32",
                                image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop",
                                viewed: 1250,
                                liked: 89
                            },
                            {
                                id: 2,
                                name: "AI Chatbot Assistant",
                                description: "Trợ lý ảo thông minh sử dụng Natural Language Processing",
                                image: "https://images.unsplash.com/photo-1531746790731-6c087fecd65a?w=400&h=200&fit=crop",
                                viewed: 980,
                                liked: 67
                            },
                            {
                                id: 3,
                                name: "E-commerce Mobile App",
                                description: "Ứng dụng mua sắm trực tuyến với React Native",
                                image: "https://images.unsplash.com/photo-1563013544-824ae1b704d3?w=400&h=200&fit=crop",
                                viewed: 756,
                                liked: 45
                            }
                        ]} />
                    </motion.div>
                )}

                {/* Blog Section */}
                <motion.div
                    initial={{ opacity: 0, y: 30 }}
                    whileInView={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    viewport={{ once: true }}
                    style={{ marginTop: '4rem' }}
                >
                    <Blog />
                </motion.div>

                {/* E-Learning Section */}
                {courseList.length > 0 && (
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.6 }}
                        viewport={{ once: true }}
                        style={{ marginTop: '4rem' }}
                    >
                        <Course courses={courseList.length > 0 ? courseList : [
                            {
                                id: 1,
                                name: "Lập trình Python cơ bản",
                                description: "Học lập trình Python từ cơ bản đến nâng cao với các dự án thực tế",
                                image: "https://images.unsplash.com/photo-1526379095098-d400fd0bf935?w=400&h=200&fit=crop",
                                rating: 4.8,
                                duration: 180
                            },
                            {
                                id: 2,
                                name: "React.js cho người mới bắt đầu",
                                description: "Xây dựng ứng dụng web hiện đại với React.js và các công cụ hỗ trợ",
                                image: "https://images.unsplash.com/photo-1633356122544-f134324a6cee?w=400&h=200&fit=crop",
                                rating: 4.9,
                                duration: 240
                            },
                            {
                                id: 3,
                                name: "Machine Learning với Python",
                                description: "Khám phá thế giới AI và Machine Learning với Python và TensorFlow",
                                image: "https://images.unsplash.com/photo-1555949963-aa79dcee981c?w=400&h=200&fit=crop",
                                rating: 4.7,
                                duration: 300
                            },
                            {
                                id: 4,
                                name: "UI/UX Design Fundamentals",
                                description: "Thiết kế giao diện người dùng chuyên nghiệp với Figma và Adobe XD",
                                image: "https://images.unsplash.com/photo-1561070791-2526d30994b5?w=400&h=200&fit=crop",
                                rating: 4.6,
                                duration: 150
                            }
                        ]} />
                    </motion.div>
                )}
            </Container>

            {/* Final CTA Section */}
            <Box
                sx={{
                    background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
                    py: 8,
                    textAlign: 'center'
                }}
            >
                <Container maxWidth="md">
                    <motion.div
                        initial={{ opacity: 0, y: 30 }}
                        whileInView={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.8 }}
                        viewport={{ once: true }}
                    >
                        <Typography
                            variant="h3"
                            sx={{
                                mb: 3,
                                fontWeight: 700,
                                fontSize: { xs: '2rem', md: '2.5rem' }
                            }}
                        >
                            Sẵn sàng khám phá?
                        </Typography>
                        <Typography
                            variant="h6"
                            sx={{
                                mb: 4,
                                color: 'text.secondary',
                                lineHeight: 1.6
                            }}
                        >
                            Tham gia cộng đồng BeE ngay hôm nay và bắt đầu hành trình công nghệ của bạn
                        </Typography>
                        <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
                            <Button
                                variant="contained"
                                size="large"
                                startIcon={<Rocket />}
                                href="/studio"
                                sx={{
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 3,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
                                    boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.3)}`,
                                    '&:hover': {
                                        transform: 'translateY(-2px)',
                                        boxShadow: `0 12px 35px ${alpha(theme.palette.primary.main, 0.4)}`
                                    }
                                }}
                            >
                                Bắt đầu ngay
                            </Button>
                            <Button
                                variant="outlined"
                                size="large"
                                startIcon={<PlayArrow />}
                                href="/e-learning"
                                sx={{
                                    px: 4,
                                    py: 1.5,
                                    borderRadius: 3,
                                    fontSize: '1.1rem',
                                    fontWeight: 600,
                                    borderWidth: 2,
                                    '&:hover': {
                                        borderWidth: 2,
                                        transform: 'translateY(-2px)'
                                    }
                                }}
                            >
                                Khám phá khóa học
                            </Button>
                        </Box>
                    </motion.div>
                </Container>
            </Box>

            <Snackbar open={openSnackbar} autoHideDuration={3000} onClose={handleCloseSnackbar}>
                <Alert severity="success" onClose={handleCloseSnackbar}>
                    Thêm vào giỏ hàng thành công!
                </Alert>
            </Snackbar>
        </CustomerLayout>
    );
}

export default Home;
