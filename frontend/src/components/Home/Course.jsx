import {
    Card,
    CardActions,
    CardContent,
    CardMedia,
    Chip,
    Typography,
    Box,
    Button,
    LinearProgress,
    useTheme,
    alpha,
    styled
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import React from "react";
import { motion } from "framer-motion";
import {
    Star,
    AccessTime,
    School,
    PlayArrow,
    ArrowForward,
    People,
    TrendingUp,
    EmojiEvents
} from "@mui/icons-material";

// Styled Components
const CourseSectionContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(6, 0),
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.info.main, 0.02)} 100%)`,
    borderRadius: theme.spacing(3),
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 80% 20%, ${alpha(theme.palette.info.main, 0.05)} 0%, transparent 50%)`,
        pointerEvents: 'none'
    }
}));

const CourseHeaderBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(4),
    position: 'relative',
    zIndex: 2
}));

const CourseViewAllButton = styled(Button)(({ theme }) => ({
    borderRadius: theme.spacing(3),
    padding: theme.spacing(1, 3),
    textTransform: 'none',
    fontWeight: 600,
    background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
    color: 'white',
    boxShadow: `0 4px 15px ${alpha(theme.palette.info.main, 0.3)}`,
    '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px ${alpha(theme.palette.info.main, 0.4)}`
    }
}));

const ModernCourseCard = styled(Card)(({ theme }) => ({
    height: '100%',
    borderRadius: theme.spacing(3),
    border: `1px solid ${alpha(theme.palette.info.main, 0.1)}`,
    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
    cursor: 'pointer',
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `linear-gradient(135deg, ${alpha(theme.palette.info.main, 0.02)} 0%, transparent 100%)`,
        opacity: 0,
        transition: 'opacity 0.3s ease'
    },
    '&:hover': {
        transform: 'translateY(-8px)',
        boxShadow: `0 20px 40px ${alpha(theme.palette.info.main, 0.15)}`,
        '&::before': {
            opacity: 1
        }
    }
}));

function CourseCard(props) {
    const theme = useTheme();
    function display_duration(time) {
        let hour = Math.floor(time / 60);
        let min = Math.round(time - hour * 60);
        return hour + "h" + min + "m";
    }

    const progress = Math.random() * 100; // Mock progress data
    const students = Math.floor(Math.random() * 1000) + 100; // Mock student count

    return (
        <ModernCourseCard>
            <Box sx={{ position: 'relative' }}>
                <CardMedia
                    sx={{
                        width: "100%",
                        height: 200,
                        objectFit: "cover",
                        position: 'relative'
                    }}
                    image={props.course.image || 'https://via.placeholder.com/400x200?text=Course+Image'}
                    title={props.course.name}
                />
                <Box
                    sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        background: alpha(theme.palette.background.paper, 0.9),
                        borderRadius: 2,
                        p: 1,
                        backdropFilter: 'blur(10px)'
                    }}
                >
                    <Chip
                        icon={<Star />}
                        label={props.course.rating || '4.8'}
                        size="small"
                        color="warning"
                        sx={{ fontWeight: 600 }}
                    />
                </Box>
                <Box
                    sx={{
                        position: 'absolute',
                        bottom: 12,
                        left: 12,
                        background: alpha(theme.palette.info.main, 0.9),
                        borderRadius: 2,
                        p: 1,
                        color: 'white',
                        display: 'flex',
                        alignItems: 'center',
                        gap: 0.5
                    }}
                >
                    <PlayArrow sx={{ fontSize: 16 }} />
                    <Typography variant="caption" sx={{ fontWeight: 600 }}>
                        {display_duration(props.course.duration || 120)}
                    </Typography>
                </Box>
            </Box>

            <CardContent sx={{ p: 3 }}>
                <Typography
                    variant="h6"
                    component="h3"
                    sx={{
                        fontWeight: 600,
                        mb: 1,
                        lineHeight: 1.3,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden'
                    }}
                >
                    {props.course.name}
                </Typography>

                {props.course.description && (
                    <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                            mb: 2,
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            overflow: 'hidden'
                        }}
                    >
                        {props.course.description}
                    </Typography>
                )}

                {/* Progress Bar */}
                <Box sx={{ mb: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="caption" color="text.secondary">
                            Tiến độ
                        </Typography>
                        <Typography variant="caption" color="primary.main" sx={{ fontWeight: 600 }}>
                            {Math.round(progress)}%
                        </Typography>
                    </Box>
                    <LinearProgress
                        variant="determinate"
                        value={progress}
                        sx={{
                            borderRadius: 1,
                            height: 6,
                            backgroundColor: alpha(theme.palette.info.main, 0.1),
                            '& .MuiLinearProgress-bar': {
                                borderRadius: 1
                            }
                        }}
                    />
                </Box>

                {/* Stats */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <People sx={{ fontSize: 16, color: 'text.secondary' }} />
                        <Typography variant="caption" color="text.secondary">
                            {students} học viên
                        </Typography>
                    </Box>
                    <Chip
                        icon={<AccessTime />}
                        label={display_duration(props.course.duration || 120)}
                        size="small"
                        variant="outlined"
                        color="info"
                    />
                </Box>
            </CardContent>

            <CardActions sx={{ p: 3, pt: 0 }}>
                <Button
                    variant="contained"
                    fullWidth
                    startIcon={<PlayArrow />}
                    sx={{
                        borderRadius: 2,
                        py: 1.5,
                        background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                        '&:hover': {
                            background: `linear-gradient(135deg, ${theme.palette.info.dark} 0%, ${theme.palette.info.main} 100%)`,
                        }
                    }}
                >
                    Bắt đầu học
                </Button>
            </CardActions>
        </ModernCourseCard>
    );
}

function Course(props) {
    const theme = useTheme();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    return (
        <CourseSectionContainer>
            <Box sx={{ px: 3, position: 'relative', zIndex: 2 }}>
                {/* Header */}
                <CourseHeaderBox>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Box
                            sx={{
                                width: 50,
                                height: 50,
                                borderRadius: 2,
                                background: `linear-gradient(135deg, ${theme.palette.info.main} 0%, ${theme.palette.info.dark} 100%)`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                boxShadow: `0 4px 15px ${alpha(theme.palette.info.main, 0.3)}`
                            }}
                        >
                            <School sx={{ color: 'white', fontSize: 24 }} />
                        </Box>
                        <Box>
                            <Typography
                                variant="h3"
                                sx={{
                                    fontWeight: 700,
                                    fontSize: { xs: '1.8rem', md: '2.5rem' },
                                    background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.info.main} 100%)`,
                                    backgroundClip: 'text',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                }}
                            >
                                E-Learning
                            </Typography>
                            <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                                Học tập trực tuyến với chuyên gia hàng đầu
                            </Typography>
                        </Box>
                    </Box>
                    <CourseViewAllButton
                        href="/e-learning"
                        endIcon={<ArrowForward />}
                    >
                        Xem tất cả
                    </CourseViewAllButton>
                </CourseHeaderBox>

                {/* Categories */}
                <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
                    <Chip
                        icon={<TrendingUp />}
                        label="Phổ biến"
                        color="info"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<EmojiEvents />}
                        label="Chứng chỉ"
                        color="warning"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<People />}
                        label="Nhiều học viên"
                        color="success"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                </Box>

                {/* Courses Grid */}
                <motion.div
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                >
                    <Grid container spacing={3}>
                        {props.courses.map((course, index) => (
                            <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={course.id || index}>
                                <motion.div
                                    variants={itemVariants}
                                    whileHover={{ scale: 1.02 }}
                                    transition={{ type: "spring", stiffness: 300 }}
                                >
                                    <CourseCard course={course} />
                                </motion.div>
                            </Grid>
                        ))}
                    </Grid>
                </motion.div>

                {/* Call to Action */}
                {props.courses.length > 4 && (
                    <Box sx={{ textAlign: 'center', mt: 6 }}>
                        <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                            Còn {props.courses.length - 4}+ khóa học chất lượng khác
                        </Typography>
                        <CourseViewAllButton
                            href="/courses"
                            size="large"
                            endIcon={<ArrowForward />}
                        >
                            Khám phá tất cả khóa học
                        </CourseViewAllButton>
                    </Box>
                )}
            </Box>
        </CourseSectionContainer>
    );
}

export default Course;
