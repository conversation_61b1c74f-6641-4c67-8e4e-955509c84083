
import PropTypes from "prop-types";
import {
    <PERSON>,
    <PERSON><PERSON><PERSON>,
    But<PERSON>,
    Chip,
    useTheme,
    alpha,
    styled
} from "@mui/material";
import Grid from "@mui/material/Grid2";
import ProductCard from "../Common/ProductCard";
import Loading from "../Common/Loading";
import { motion } from "framer-motion";
import {
    ShoppingCart,
    TrendingUp,
    LocalOffer,
    ArrowForward,
    Star
} from "@mui/icons-material";

// Styled Components
const SectionContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(6, 0),
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.8)} 0%, ${alpha(theme.palette.primary.main, 0.02)} 100%)`,
    borderRadius: theme.spacing(3),
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 80% 20%, ${alpha(theme.palette.primary.main, 0.05)} 0%, transparent 50%)`,
        pointerEvents: 'none'
    }
}));

const HeaderBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(4),
    position: 'relative',
    zIndex: 2
}));

const ViewAllButton = styled(Button)(({ theme }) => ({
    borderRadius: theme.spacing(3),
    padding: theme.spacing(1, 3),
    textTransform: 'none',
    fontWeight: 600,
    background: `linear-gradient(135deg, ${theme.palette.primary.main} 0%, ${theme.palette.primary.dark} 100%)`,
    color: 'white',
    boxShadow: `0 4px 15px ${alpha(theme.palette.primary.main, 0.3)}`,
    '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px ${alpha(theme.palette.primary.main, 0.4)}`
    }
}));

Shopping.propTypes = {
    items: PropTypes.array.isRequired,
    cartItem: PropTypes.array.isRequired,
    setCartItem: PropTypes.func.isRequired,
    loading: PropTypes.bool.isRequired,
    handleAddToCart: PropTypes.func.isRequired
};

function Shopping(props) {
    const theme = useTheme();

    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    return (
        <SectionContainer>
            <Box sx={{ px: 3, position: 'relative', zIndex: 2 }}>
                {/* Header */}
                <HeaderBox>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Box
                            sx={{
                                width: 50,
                                height: 50,
                                borderRadius: 2,
                                background: `linear-gradient(135deg, ${theme.palette.success.main} 0%, ${theme.palette.success.dark} 100%)`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                boxShadow: `0 4px 15px ${alpha(theme.palette.success.main, 0.3)}`
                            }}
                        >
                            <ShoppingCart sx={{ color: 'white', fontSize: 24 }} />
                        </Box>
                        <Box>
                            <Typography
                                variant="h3"
                                sx={{
                                    fontWeight: 700,
                                    fontSize: { xs: '1.8rem', md: '2.5rem' },
                                    background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.primary.main} 100%)`,
                                    backgroundClip: 'text',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                }}
                            >
                                Cửa hàng công nghệ
                            </Typography>
                            <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                                Khám phá các sản phẩm công nghệ hàng đầu
                            </Typography>
                        </Box>
                    </Box>
                    <ViewAllButton
                        href="/shop"
                        endIcon={<ArrowForward />}
                    >
                        Xem tất cả
                    </ViewAllButton>
                </HeaderBox>

                {/* Stats */}
                <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
                    <Chip
                        icon={<TrendingUp />}
                        label="Bán chạy nhất"
                        color="success"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<LocalOffer />}
                        label="Giá tốt nhất"
                        color="warning"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<Star />}
                        label="Đánh giá cao"
                        color="primary"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                </Box>

                {/* Products Grid */}
                {props.loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                        <Loading />
                    </Box>
                ) : (
                    <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        <Grid container spacing={3}>
                            {props.items.slice(0, 8).map((item, index) => (
                                <Grid size={{ xs: 12, sm: 6, md: 4, lg: 3 }} key={index}>
                                    <motion.div
                                        variants={itemVariants}
                                        whileHover={{ y: -8 }}
                                        transition={{ type: "spring", stiffness: 300 }}
                                    >
                                        <ProductCard
                                            item={item}
                                            setCartItem={props.setCartItem}
                                        />
                                    </motion.div>
                                </Grid>
                            ))}
                        </Grid>
                    </motion.div>
                )}

                {/* Call to Action */}
                {props.items.length > 8 && (
                    <Box sx={{ textAlign: 'center', mt: 6 }}>
                        <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                            Còn {props.items.length - 8}+ sản phẩm khác đang chờ bạn khám phá
                        </Typography>
                        <ViewAllButton
                            href="/shop"
                            size="large"
                            endIcon={<ArrowForward />}
                        >
                            Khám phá tất cả sản phẩm
                        </ViewAllButton>
                    </Box>
                )}
            </Box>
        </SectionContainer>
    );
}

export default Shopping;
