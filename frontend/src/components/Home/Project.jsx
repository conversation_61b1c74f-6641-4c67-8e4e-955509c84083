import { Fab, IconButton, Paper, Typography } from "@mui/material";
import PropTypes from "prop-types";
import Grid from "@mui/material/Grid2";
import Card from "@mui/material/Card";
import CardActions from "@mui/material/CardActions";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import { CardActionArea, Chip } from "@mui/material";
import VisibilityIcon from "@mui/icons-material/Visibility";
import FavoriteIcon from "@mui/icons-material/Favorite";
import AddIcon from "@mui/icons-material/Add";

ProjectCard.propTypes = {
    project: PropTypes.object.isRequired,
};

function ProjectCard(props) {
    return (
        <Card sx={{ width: "100%", mr: "10px", borderRadius: "20px" }}>
            <CardActionArea href={"/project/" + props.project.id}>
                <CardMedia
                    sx={{ width: "100%", height: 140, objectFit: "cover" }}
                    image={props.project.image}
                    title={props.project.name}
                />
                <CardContent>
                    <Typography>{props.project.name}</Typography>
                    {props.project.description === "" && (
                        <Typography variant="body2" color="text.secondary">
                            {props.project.description}
                        </Typography>
                    )}
                </CardContent>
                <CardActions>
                    <Chip
                        icon={<VisibilityIcon />}
                        label={props.project.viewed}
                        variant="outlined"
                        color="primary"
                        size="small"
                    />
                    <Chip
                        icon={<FavoriteIcon />}
                        label={props.project.liked}
                        variant="outlined"
                        color="error"
                        size="small"
                    />
                </CardActions>
            </CardActionArea>
        </Card>
    );
}

Project.propTypes = {
    projects: PropTypes.array.isRequired,
};

function Project(props) {
    return (
        <div>
            <Typography sx={{ mb: "10px", fontSize: "30px", fontWeight: 300 }}>Dự án mới</Typography>
            <img
                width="100%"
                height="200px"
                style={{ objectFit: "contain", marginBottom: "30px" }}
                alt="my-project"
                src="https://cdn.europosters.eu/image/hp/75998.jpg"
            />
            <Grid container spacing={4} sx={{ mb: "100px" }}>
                {props.projects.map((project) => (
                    <Grid size={4} sx={{ mb: "10px" }} key={project.id}>
                        <ProjectCard project={project} />
                    </Grid>
                ))}
                {/* <Grid size={4} sx={{ mb: "10px" }}>
                    <Paper
                        sx={{
                            width: "100%",
                            mr: "10px",
                            borderRadius: "20px",
                            height: "100%",
                            display: "flex",
                            justifyContent: "center",
                            alignItems: "center",
                        }}
                    >
                        <Fab color="primary" aria-label="add" size="large">
                            <AddIcon />
                        </Fab>
                    </Paper>
                </Grid> */}
            </Grid>
        </div>
    );
}

export default Project;
