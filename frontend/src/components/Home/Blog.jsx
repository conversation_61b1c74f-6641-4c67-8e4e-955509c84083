import {
    Box,
    Typo<PERSON>,
    But<PERSON>,
    Chip,
    useTheme,
    alpha,
    styled
} from "@mui/material";
import React from "react";
import Grid from "@mui/material/Grid2";
import BlogCard from "../Blog/BlogCard";
import Loading from "../Common/Loading";
import axiosInstance from "../../services/axiosInstance";
import { motion } from "framer-motion";
import {
    Article,
    TrendingUp,
    Schedule,
    ArrowForward,
    Visibility
} from "@mui/icons-material";

// Styled Components
const BlogSectionContainer = styled(Box)(({ theme }) => ({
    padding: theme.spacing(6, 0),
    background: `linear-gradient(135deg, ${alpha(theme.palette.background.paper, 0.9)} 0%, ${alpha(theme.palette.warning.main, 0.02)} 100%)`,
    borderRadius: theme.spacing(3),
    position: 'relative',
    overflow: 'hidden',
    '&::before': {
        content: '""',
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        background: `radial-gradient(circle at 20% 80%, ${alpha(theme.palette.warning.main, 0.05)} 0%, transparent 50%)`,
        pointerEvents: 'none'
    }
}));

const BlogHeaderBox = styled(Box)(({ theme }) => ({
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing(4),
    position: 'relative',
    zIndex: 2
}));

const BlogViewAllButton = styled(Button)(({ theme }) => ({
    borderRadius: theme.spacing(3),
    padding: theme.spacing(1, 3),
    textTransform: 'none',
    fontWeight: 600,
    background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
    color: 'white',
    boxShadow: `0 4px 15px ${alpha(theme.palette.warning.main, 0.3)}`,
    '&:hover': {
        transform: 'translateY(-2px)',
        boxShadow: `0 8px 25px ${alpha(theme.palette.warning.main, 0.4)}`
    }
}));

function Blog() {
    const theme = useTheme();
    const containerVariants = {
        hidden: { opacity: 0 },
        visible: {
            opacity: 1,
            transition: {
                staggerChildren: 0.1,
            },
        },
    };

    const itemVariants = {
        hidden: { y: 20, opacity: 0 },
        visible: {
            y: 0,
            opacity: 1,
        },
    };

    const [loading, setLoading] = React.useState(true);
    const [posts, setPosts] = React.useState([]);

    React.useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await axiosInstance.get("/api/shopping/home/<USER>");
                setPosts(response.data);
            } catch (error) {
                console.error("Đã có lỗi fetch bài viết");
            } finally {
                setLoading(false);
            }
        };
        fetchData();
    }, []);

    return (
        <BlogSectionContainer>
            <Box sx={{ px: 3, position: 'relative', zIndex: 2 }}>
                {/* Header */}
                <BlogHeaderBox>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                        <Box
                            sx={{
                                width: 50,
                                height: 50,
                                borderRadius: 2,
                                background: `linear-gradient(135deg, ${theme.palette.warning.main} 0%, ${theme.palette.warning.dark} 100%)`,
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                boxShadow: `0 4px 15px ${alpha(theme.palette.warning.main, 0.3)}`
                            }}
                        >
                            <Article sx={{ color: 'white', fontSize: 24 }} />
                        </Box>
                        <Box>
                            <Typography
                                variant="h3"
                                sx={{
                                    fontWeight: 700,
                                    fontSize: { xs: '1.8rem', md: '2.5rem' },
                                    background: `linear-gradient(135deg, ${theme.palette.text.primary} 0%, ${theme.palette.warning.main} 100%)`,
                                    backgroundClip: 'text',
                                    WebkitBackgroundClip: 'text',
                                    WebkitTextFillColor: 'transparent',
                                }}
                            >
                                Bài viết & Tin tức
                            </Typography>
                            <Typography variant="body1" color="text.secondary" sx={{ mt: 0.5 }}>
                                Cập nhật xu hướng công nghệ mới nhất
                            </Typography>
                        </Box>
                    </Box>
                    <BlogViewAllButton
                        href="/blog"
                        endIcon={<ArrowForward />}
                    >
                        Xem tất cả
                    </BlogViewAllButton>
                </BlogHeaderBox>

                {/* Categories */}
                <Box sx={{ display: 'flex', gap: 2, mb: 4, flexWrap: 'wrap' }}>
                    <Chip
                        icon={<TrendingUp />}
                        label="Xu hướng"
                        color="warning"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<Schedule />}
                        label="Mới nhất"
                        color="info"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                    <Chip
                        icon={<Visibility />}
                        label="Đọc nhiều"
                        color="success"
                        variant="outlined"
                        sx={{
                            borderRadius: 2,
                            '& .MuiChip-icon': { fontSize: 18 }
                        }}
                    />
                </Box>

                {/* Posts Grid */}
                {loading ? (
                    <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
                        <Loading />
                    </Box>
                ) : (
                    <motion.div
                        variants={containerVariants}
                        initial="hidden"
                        animate="visible"
                    >
                        <Grid container spacing={3}>
                            {posts.slice(0, 6).map((post, index) => (
                                <Grid size={{ xs: 12, sm: 6, md: 4 }} key={index}>
                                    <motion.div
                                        variants={itemVariants}
                                        whileHover={{ y: -8 }}
                                        transition={{ type: "spring", stiffness: 300 }}
                                        style={{ height: "100%" }}
                                    >
                                        <BlogCard post={post} />
                                    </motion.div>
                                </Grid>
                            ))}
                        </Grid>
                    </motion.div>
                )}

                {/* Call to Action */}
                {posts.length > 6 && (
                    <Box sx={{ textAlign: 'center', mt: 6 }}>
                        <Typography variant="h6" sx={{ mb: 2, color: 'text.secondary' }}>
                            Còn {posts.length - 6}+ bài viết thú vị khác
                        </Typography>
                        <BlogViewAllButton
                            href="/blog"
                            size="large"
                            endIcon={<ArrowForward />}
                        >
                            Đọc thêm bài viết
                        </BlogViewAllButton>
                    </Box>
                )}
            </Box>
        </BlogSectionContainer>
    );
}

export default Blog;
