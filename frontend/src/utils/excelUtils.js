/**
 * Utility functions for Excel import/export operations
 */

/**
 * Validate Excel row data for question import
 * @param {Object} row - Excel row data
 * @param {number} rowNumber - Row number for error reporting
 * @returns {Object} - { isValid: boolean, errors: string[] }
 */
export const validateQuestionRow = (row, rowNumber) => {
    const errors = [];

    // Check required fields
    const requiredFields = ['Câu hỏi', 'Đáp án A', 'Đáp án B', 'Đáp án C', 'Đáp án D', 'Đáp án đúng'];

    for (const field of requiredFields) {
        if (!row[field] || row[field].toString().trim() === '') {
            errors.push(`Dòng ${rowNumber}: Thiếu thông tin "${field}"`);
        }
    }

    // Validate correct answers format
    if (row['Đáp án đúng']) {
        const correctAnswersStr = row['Đáp án đúng'].toString().toUpperCase();
        const answerLetters = correctAnswersStr.split(/[,\s]+/).filter(letter => letter.match(/^[A-D]$/));

        if (answerLetters.length === 0) {
            errors.push(`Dòng ${rowNumber}: Đáp án đúng không hợp lệ. Sử dụng A, B, C, hoặc D`);
        }
    }

    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Convert Excel row to question data format
 * @param {Object} row - Excel row data
 * @param {Array} subjects - Array of subject objects
 * @param {Array} grades - Array of grade objects
 * @returns {Object} - Question data object
 */
export const convertRowToQuestion = (row, subjects = [], grades = []) => {
    // Parse correct answers
    const correctAnswersStr = row['Đáp án đúng'].toString().toUpperCase();
    const correctAnswers = [];

    const answerLetters = correctAnswersStr.split(/[,\s]+/).filter(letter => letter.match(/^[A-D]$/));
    answerLetters.forEach(letter => {
        const index = letter.charCodeAt(0) - 65; // Convert A,B,C,D to 0,1,2,3
        if (index >= 0 && index <= 3) {
            correctAnswers.push(index);
        }
    });

    // Find subject by name
    let subjectId = null;
    if (row['Môn học']) {
        const subjectName = row['Môn học'].toString().trim();
        const foundSubject = subjects.find(s =>
            s.name.toLowerCase() === subjectName.toLowerCase()
        );
        subjectId = foundSubject ? foundSubject.id : null;
    }

    // Find grade by name
    let gradeId = null;
    if (row['Lớp học']) {
        const gradeName = row['Lớp học'].toString().trim();
        const foundGrade = grades.find(g =>
            g.name.toLowerCase() === gradeName.toLowerCase()
        );
        gradeId = foundGrade ? foundGrade.id : null;
    }

    // Parse difficulty
    let difficulty = 'medium'; // Default
    if (row['Độ khó']) {
        const difficultyStr = row['Độ khó'].toString().trim().toLowerCase();
        if (difficultyStr.includes('dễ') || difficultyStr === 'easy') {
            difficulty = 'easy';
        } else if (difficultyStr.includes('khó') || difficultyStr === 'hard') {
            difficulty = 'hard';
        }
    }

    // Parse points
    let points = 1; // Default
    if (row['Điểm']) {
        const pointsValue = parseInt(row['Điểm']);
        if (!isNaN(pointsValue) && pointsValue > 0) {
            points = pointsValue;
        }
    }

    // Parse tags
    let tags = [];
    if (row['Tags']) {
        tags = row['Tags'].toString().split(',').map(tag => tag.trim()).filter(tag => tag);
    }

    return {
        question_text: row['Câu hỏi'].toString().trim(),
        question_type: 'multiple_choice',
        difficulty: difficulty,
        points: points,
        options: [
            row['Đáp án A'].toString().trim(),
            row['Đáp án B'].toString().trim(),
            row['Đáp án C'].toString().trim(),
            row['Đáp án D'].toString().trim()
        ],
        correct_answers: correctAnswers,
        explanation: row['Giải thích'] ? row['Giải thích'].toString().trim() : '',
        tags: tags,
        subject: subjectId,
        grade: gradeId
    };
};

/**
 * Convert question data to Excel export format
 * @param {Array} questions - Array of question objects
 * @param {Array} subjects - Array of subject objects
 * @param {Array} grades - Array of grade objects
 * @param {Array} difficulties - Array of difficulty objects
 * @returns {Array} - Array of Excel row objects
 */
export const convertQuestionsToExcelFormat = (questions, subjects, grades, difficulties) => {
    return questions.map((question, index) => {
        const subjectName = subjects.find(s => s.id === question.subject)?.name || '';
        const gradeName = grades.find(g => g.id === question.grade)?.name || '';
        const difficultyLabel = difficulties.find(d => d.value === question.difficulty)?.label || question.difficulty;

        // Convert correct answers from indices to letters
        const correctAnswersLetters = question.correct_answers?.map(index => String.fromCharCode(65 + index)).join(', ') || '';

        return {
            'Số thứ tự': index + 1,
            'Câu hỏi': question.question_text,
            'Đáp án A': question.options[0] || '',
            'Đáp án B': question.options[1] || '',
            'Đáp án C': question.options[2] || '',
            'Đáp án D': question.options[3] || '',
            'Đáp án đúng': correctAnswersLetters,
            'Giải thích': question.explanation || '',
            'Môn học': subjectName,
            'Lớp học': gradeName,
            'Độ khó': difficultyLabel,
            'Điểm': question.points || 1,
            'Tags': Array.isArray(question.tags) ? question.tags.join(', ') : question.tags || ''
        };
    });
};

/**
 * Generate Excel template data
 * @returns {Array} - Array of template row objects
 */
export const generateTemplateData = () => {
    return [
        {
            'Số thứ tự': 1,
            'Câu hỏi': 'Thủ đô của Việt Nam là gì?',
            'Đáp án A': 'Hà Nội',
            'Đáp án B': 'Hồ Chí Minh',
            'Đáp án C': 'Đà Nẵng',
            'Đáp án D': 'Cần Thơ',
            'Đáp án đúng': 'A',
            'Giải thích': 'Hà Nội là thủ đô của Việt Nam từ năm 1010',
            'Môn học': 'Địa lý',
            'Lớp học': 'Lớp 6',
            'Độ khó': 'Dễ',
            'Điểm': 1,
            'Tags': 'địa lý, thủ đô'
        },
        {
            'Số thứ tự': 2,
            'Câu hỏi': 'Kết quả của phép tính 2 + 3 là?',
            'Đáp án A': '4',
            'Đáp án B': '5',
            'Đáp án C': '6',
            'Đáp án D': '7',
            'Đáp án đúng': 'B',
            'Giải thích': '2 + 3 = 5',
            'Môn học': 'Toán học',
            'Lớp học': 'Lớp 1',
            'Độ khó': 'Dễ',
            'Điểm': 1,
            'Tags': 'toán học, cộng'
        },
        {
            'Số thứ tự': 3,
            'Câu hỏi': 'Những ngôn ngữ nào sau đây là ngôn ngữ lập trình?',
            'Đáp án A': 'Python',
            'Đáp án B': 'HTML',
            'Đáp án C': 'JavaScript',
            'Đáp án D': 'CSS',
            'Đáp án đúng': 'A, C',
            'Giải thích': 'Python và JavaScript là ngôn ngữ lập trình, HTML và CSS là ngôn ngữ đánh dấu và định dạng',
            'Môn học': 'Tin học',
            'Lớp học': 'Lớp 10',
            'Độ khó': 'Trung bình',
            'Điểm': 2,
            'Tags': 'lập trình, ngôn ngữ'
        }
    ];
};

/**
 * Get Excel column widths configuration
 * @returns {Array} - Array of column width objects
 */
export const getExcelColumnWidths = () => {
    return [
        { wch: 10 }, // Số thứ tự
        { wch: 50 }, // Câu hỏi
        { wch: 30 }, // Đáp án A
        { wch: 30 }, // Đáp án B
        { wch: 30 }, // Đáp án C
        { wch: 30 }, // Đáp án D
        { wch: 15 }, // Đáp án đúng
        { wch: 40 }, // Giải thích
        { wch: 15 }, // Môn học
        { wch: 10 }, // Lớp học
        { wch: 10 }, // Độ khó
        { wch: 8 },  // Điểm
        { wch: 20 }  // Tags
    ];
};

/**
 * Get template column widths configuration
 * @returns {Array} - Array of column width objects for template
 */
export const getTemplateColumnWidths = () => {
    return [
        { wch: 10 }, // Số thứ tự
        { wch: 50 }, // Câu hỏi
        { wch: 30 }, // Đáp án A
        { wch: 30 }, // Đáp án B
        { wch: 30 }, // Đáp án C
        { wch: 30 }, // Đáp án D
        { wch: 15 }, // Đáp án đúng
        { wch: 40 }, // Giải thích
        { wch: 15 }, // Môn học
        { wch: 10 }, // Lớp học
        { wch: 10 }, // Độ khó
        { wch: 8 },  // Điểm
        { wch: 20 }  // Tags
    ];
};

/**
 * Find duplicate question by comparing question text and options
 * @param {Object} newQuestion - New question data
 * @param {Array} existingQuestions - Array of existing questions
 * @returns {Object|null} - Found duplicate question or null
 */
export const findDuplicateQuestion = (newQuestion, existingQuestions) => {
    const normalizeText = (text) => {
        return text.toLowerCase()
            .replace(/\s+/g, ' ')
            .trim()
            .replace(/[^\w\s]/g, ''); // Remove special characters
    };

    const newQuestionNormalized = normalizeText(newQuestion.question_text);

    return existingQuestions.find(existing => {
        const existingNormalized = normalizeText(existing.question_text);

        // Check if question text is similar (exact match after normalization)
        if (existingNormalized === newQuestionNormalized) {
            return true;
        }

        // Check if question text is very similar (90% similarity)
        const similarity = calculateSimilarity(existingNormalized, newQuestionNormalized);
        return similarity > 0.9;
    });
};

/**
 * Calculate text similarity using Levenshtein distance
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Similarity ratio (0-1)
 */
const calculateSimilarity = (str1, str2) => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;

    if (longer.length === 0) {
        return 1.0;
    }

    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
};

/**
 * Calculate Levenshtein distance between two strings
 * @param {string} str1 - First string
 * @param {string} str2 - Second string
 * @returns {number} - Levenshtein distance
 */
const levenshteinDistance = (str1, str2) => {
    const matrix = [];

    for (let i = 0; i <= str2.length; i++) {
        matrix[i] = [i];
    }

    for (let j = 0; j <= str1.length; j++) {
        matrix[0][j] = j;
    }

    for (let i = 1; i <= str2.length; i++) {
        for (let j = 1; j <= str1.length; j++) {
            if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
                matrix[i][j] = matrix[i - 1][j - 1];
            } else {
                matrix[i][j] = Math.min(
                    matrix[i - 1][j - 1] + 1,
                    matrix[i][j - 1] + 1,
                    matrix[i - 1][j] + 1
                );
            }
        }
    }

    return matrix[str2.length][str1.length];
};

/**
 * Check if question data has significant changes compared to existing question
 * @param {Object} newQuestion - New question data
 * @param {Object} existingQuestion - Existing question data
 * @returns {boolean} - True if there are significant changes
 */
export const hasSignificantChanges = (newQuestion, existingQuestion) => {
    // Check if options are different
    const optionsChanged = !newQuestion.options.every((option, index) =>
        option.toLowerCase().trim() === (existingQuestion.options[index] || '').toLowerCase().trim()
    );

    // Check if correct answers are different
    const correctAnswersChanged = JSON.stringify(newQuestion.correct_answers.sort()) !==
        JSON.stringify((existingQuestion.correct_answers || []).sort());

    // Check if explanation is different
    const explanationChanged = (newQuestion.explanation || '').trim() !==
        (existingQuestion.explanation || '').trim();

    // Check if other metadata is different
    const metadataChanged = newQuestion.difficulty !== existingQuestion.difficulty ||
        newQuestion.points !== existingQuestion.points ||
        newQuestion.subject !== existingQuestion.subject ||
        newQuestion.grade !== existingQuestion.grade;

    return optionsChanged || correctAnswersChanged || explanationChanged || metadataChanged;
};
