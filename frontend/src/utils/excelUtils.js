/**
 * Utility functions for Excel import/export operations
 */

/**
 * Validate Excel row data for question import
 * @param {Object} row - Excel row data
 * @param {number} rowNumber - Row number for error reporting
 * @returns {Object} - { isValid: boolean, errors: string[] }
 */
export const validateQuestionRow = (row, rowNumber) => {
    const errors = [];
    
    // Check required fields
    const requiredFields = ['Câu hỏi', 'Đáp án A', 'Đáp án B', 'Đáp án C', 'Đáp án D', 'Đáp án đúng'];
    
    for (const field of requiredFields) {
        if (!row[field] || row[field].toString().trim() === '') {
            errors.push(`Dòng ${rowNumber}: Thiếu thông tin "${field}"`);
        }
    }
    
    // Validate correct answers format
    if (row['Đáp án đúng']) {
        const correctAnswersStr = row['Đáp án đúng'].toString().toUpperCase();
        const answerLetters = correctAnswersStr.split(/[,\s]+/).filter(letter => letter.match(/^[A-D]$/));
        
        if (answerLetters.length === 0) {
            errors.push(`Dòng ${rowNumber}: Đáp án đúng không hợp lệ. Sử dụng A, B, C, hoặc D`);
        }
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
};

/**
 * Convert Excel row to question data format
 * @param {Object} row - Excel row data
 * @returns {Object} - Question data object
 */
export const convertRowToQuestion = (row) => {
    // Parse correct answers
    const correctAnswersStr = row['Đáp án đúng'].toString().toUpperCase();
    const correctAnswers = [];
    
    const answerLetters = correctAnswersStr.split(/[,\s]+/).filter(letter => letter.match(/^[A-D]$/));
    answerLetters.forEach(letter => {
        const index = letter.charCodeAt(0) - 65; // Convert A,B,C,D to 0,1,2,3
        if (index >= 0 && index <= 3) {
            correctAnswers.push(index);
        }
    });

    return {
        question_text: row['Câu hỏi'].toString().trim(),
        question_type: 'multiple_choice',
        difficulty: 'medium', // Default difficulty
        points: 1, // Default points
        options: [
            row['Đáp án A'].toString().trim(),
            row['Đáp án B'].toString().trim(),
            row['Đáp án C'].toString().trim(),
            row['Đáp án D'].toString().trim()
        ],
        correct_answers: correctAnswers,
        explanation: row['Giải thích'] ? row['Giải thích'].toString().trim() : '',
        tags: [],
        subject: null, // Will be set by user later
        grade: null    // Will be set by user later
    };
};

/**
 * Convert question data to Excel export format
 * @param {Array} questions - Array of question objects
 * @param {Array} subjects - Array of subject objects
 * @param {Array} grades - Array of grade objects
 * @param {Array} difficulties - Array of difficulty objects
 * @returns {Array} - Array of Excel row objects
 */
export const convertQuestionsToExcelFormat = (questions, subjects, grades, difficulties) => {
    return questions.map((question, index) => {
        const subjectName = subjects.find(s => s.id === question.subject)?.name || '';
        const gradeName = grades.find(g => g.id === question.grade)?.name || '';
        const difficultyLabel = difficulties.find(d => d.value === question.difficulty)?.label || question.difficulty;
        
        // Convert correct answers from indices to letters
        const correctAnswersLetters = question.correct_answers?.map(index => String.fromCharCode(65 + index)).join(', ') || '';
        
        return {
            'Số thứ tự': index + 1,
            'Câu hỏi': question.question_text,
            'Đáp án A': question.options[0] || '',
            'Đáp án B': question.options[1] || '',
            'Đáp án C': question.options[2] || '',
            'Đáp án D': question.options[3] || '',
            'Đáp án đúng': correctAnswersLetters,
            'Giải thích': question.explanation || '',
            'Môn học': subjectName,
            'Lớp học': gradeName,
            'Độ khó': difficultyLabel,
            'Điểm': question.points || 1,
            'Tags': Array.isArray(question.tags) ? question.tags.join(', ') : question.tags || ''
        };
    });
};

/**
 * Generate Excel template data
 * @returns {Array} - Array of template row objects
 */
export const generateTemplateData = () => {
    return [
        {
            'Số thứ tự': 1,
            'Câu hỏi': 'Thủ đô của Việt Nam là gì?',
            'Đáp án A': 'Hà Nội',
            'Đáp án B': 'Hồ Chí Minh',
            'Đáp án C': 'Đà Nẵng',
            'Đáp án D': 'Cần Thơ',
            'Đáp án đúng': 'A',
            'Giải thích': 'Hà Nội là thủ đô của Việt Nam từ năm 1010'
        },
        {
            'Số thứ tự': 2,
            'Câu hỏi': 'Kết quả của phép tính 2 + 3 là?',
            'Đáp án A': '4',
            'Đáp án B': '5',
            'Đáp án C': '6',
            'Đáp án D': '7',
            'Đáp án đúng': 'B',
            'Giải thích': '2 + 3 = 5'
        },
        {
            'Số thứ tự': 3,
            'Câu hỏi': 'Những ngôn ngữ nào sau đây là ngôn ngữ lập trình?',
            'Đáp án A': 'Python',
            'Đáp án B': 'HTML',
            'Đáp án C': 'JavaScript',
            'Đáp án D': 'CSS',
            'Đáp án đúng': 'A, C',
            'Giải thích': 'Python và JavaScript là ngôn ngữ lập trình, HTML và CSS là ngôn ngữ đánh dấu và định dạng'
        }
    ];
};

/**
 * Get Excel column widths configuration
 * @returns {Array} - Array of column width objects
 */
export const getExcelColumnWidths = () => {
    return [
        { wch: 10 }, // Số thứ tự
        { wch: 50 }, // Câu hỏi
        { wch: 30 }, // Đáp án A
        { wch: 30 }, // Đáp án B
        { wch: 30 }, // Đáp án C
        { wch: 30 }, // Đáp án D
        { wch: 15 }, // Đáp án đúng
        { wch: 40 }, // Giải thích
        { wch: 15 }, // Môn học
        { wch: 10 }, // Lớp học
        { wch: 10 }, // Độ khó
        { wch: 8 },  // Điểm
        { wch: 20 }  // Tags
    ];
};

/**
 * Get template column widths configuration
 * @returns {Array} - Array of column width objects for template
 */
export const getTemplateColumnWidths = () => {
    return [
        { wch: 10 }, // Số thứ tự
        { wch: 50 }, // Câu hỏi
        { wch: 30 }, // Đáp án A
        { wch: 30 }, // Đáp án B
        { wch: 30 }, // Đáp án C
        { wch: 30 }, // Đáp án D
        { wch: 15 }, // Đáp án đúng
        { wch: 40 }  // Giải thích
    ];
};
